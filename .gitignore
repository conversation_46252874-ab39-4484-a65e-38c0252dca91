### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# JetBrain IDE
*.iml
*.ipr
*.iws
.idea
.DS_Store

# Custom directories and files
bin
logs
git_data
dependencies

*.log
*.pprof_*
go.work*
*.darwin
*.linux
