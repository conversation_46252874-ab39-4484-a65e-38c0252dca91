# these ARGs are used in the FROM statements or as global variables if declared again (without the default value)
# use `cr.ttyuyin.com/probe/golang:1.23.3` as build time image in Sentinel
ARG BUILDTIME_IMAGE="golang:1.23.3"
# use `cr.ttyuyin.com/probe/python:3.10.15-slim-bookworm` as runtime image for services requiring Python environment
ARG RUNTIME_IMAGE="debian:bookworm-20240904-slim"

FROM $BUILDTIME_IMAGE AS builder

ARG SERVICE
ARG MAKE_CMD="all-linux"

LABEL stage=gobuilder

ENV CGO_ENABLED=0 \
    GO111MODULE="on" \
    GOPROXY="http://yw-nexus.ttyuyin.com:8081/repository/group-go/" \
    GOSUMDB="off"

RUN git config --global http.sslVerify false

WORKDIR /build

COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN cd $SERVICE && \
    make $MAKE_CMD


FROM $RUNTIME_IMAGE

ARG SERVICE
ARG BINARY

RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt update && \
    apt install -y iputils-ping net-tools procps dnsutils curl git jq

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai

WORKDIR /app
COPY --from=builder /build/$SERVICE/bin/$BINARY /app/$SERVICE
ENV TZ=Asia/Shanghai \
    PATH=${PATH}:/app \
    SERVICE=$SERVICE

# run service with default configuration
ENTRYPOINT ["/app/$SERVICE"]
