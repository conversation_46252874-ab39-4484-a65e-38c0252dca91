package common

import (
	"sync"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

var (
	serverID        string
	genServerIDOnce sync.Once
)

func ID() string {
	genServerIDOnce.Do(
		func() {
			serverID = GenerateServerID()
		},
	)

	return serverID
}

func GenerateServerID() string {
	return utils.GenNanoId(ConstServerIDPrefix)
}

func GenerateClientID() string {
	return utils.GenNanoId(ConstClientIDPrefix)
}
