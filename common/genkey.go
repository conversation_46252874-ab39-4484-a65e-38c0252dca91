package common

import "fmt"

// ServerInfoKeyPrefix returns a prefix for the server info key.
func ServerInfoKeyPrefix(name string) string {
	return fmt.Sprintf("%s:info:", name)
}

// RouterInfoKeyPrefix returns a prefix for the router service info key.
func RouterInfoKeyPrefix() string {
	return ServerInfoKeyPrefix(RouterServiceName)
}

// RouterInfoKey returns a redis key for the given router id.
func RouterInfoKey(rid string) string {
	if rid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", RouterInfoKeyPrefix(), rid, constRedisTypeString)
}

// NodeInfoKeyPrefix returns a prefix for the node service info key.
func NodeInfoKeyPrefix() string {
	return ServerInfoKeyPrefix(NodeServiceName)
}

// NodeInfoKey returns a redis key for the given node id.
func NodeInfoKey(nid string) string {
	if nid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", NodeInfoKeyPrefix(), nid, constRedisTypeString)
}

// NodeWithClientsKeyPrefix returns a prefix for the key of
// the relationship between the node service and the clients.
func NodeWithClientsKeyPrefix() string {
	return fmt.Sprintf("%s:clients:", NodeServiceName)
}

// NodeWithClientsKey returns a redis key for the relationship
// between the node service and the clients.
func NodeWithClientsKey(nid string) string {
	if nid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", NodeWithClientsKeyPrefix(), nid, constRedisTypeHash)
}

// NodeWithUsersKeyPrefix returns a prefix for the key of
// the relationship between the node service and the users.
func NodeWithUsersKeyPrefix() string {
	return fmt.Sprintf("%s:users:", NodeServiceName)
}

// NodeWithUsersKey returns a redis key for the relationship
// between the node service and the users.
func NodeWithUsersKey(nid string) string {
	if nid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", NodeWithUsersKeyPrefix(), nid, constRedisTypeHash)
}

// ClientInfoKeyPrefix returns a prefix for the client info key.
func ClientInfoKeyPrefix() string {
	return ConstClientInfoKeyPrefix
}

// ClientInfoKey returns a redis key for the given client id.
func ClientInfoKey(cid string) string {
	if cid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", ClientInfoKeyPrefix(), cid, constRedisTypeString)
}

// UserInfoKeyPrefix returns a prefix for the user info key.
func UserInfoKeyPrefix() string {
	return ConstUserInfoKeyPrefix
}

// UserInfoKey returns a redis key for the given user id.
func UserInfoKey(uid string) string {
	if uid == "" {
		return ""
	}

	return fmt.Sprintf("%s%s:%s", UserInfoKeyPrefix(), uid, constRedisTypeString)
}
