package common

const (
	ProductNameHeaderKey = "X-API-Proxy-Product-Name"
	ClientTypeHeaderKey  = "X-API-Proxy-Client-Type"

	RateLimitAllowedHeaderKey    = "X-RateLimit-Allowed"
	RateLimitRemainingHeaderKey  = "X-RateLimit-Remaining"
	RateLimitRetryAfterHeaderKey = "X-RateLimit-RetryAfter"
	RateLimitResetAfterHeaderKey = "X-RateLimit-ResetAfter"
)

type (
	redisConfContextKey     struct{}
	usernameContextKey      struct{}
	clientIDContextKey      struct{}
	clientInfoContextKey    struct{}
	customHeadersContextKey struct{}

	nodeInfoAttributeKey     struct{}
	nodeAttrInfoAttributeKey struct{}
)

var (
	RedisConfContextKey     redisConfContextKey
	UsernameContextKey      usernameContextKey
	ClientIDContextKey      clientIDContextKey
	ClientInfoContextKey    clientInfoContextKey // Deprecated: use CustomHeadersContextKey instead.
	CustomHeadersContextKey customHeadersContextKey

	NodeInfoAttributeKey     nodeInfoAttributeKey
	NodeAttrInfoAttributeKey nodeAttrInfoAttributeKey
)
