// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/event.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Operate 操作类型
type OperateType int32

const (
	OperateType_OT_UNKNOWN OperateType = 0
	OperateType_CREATE     OperateType = 1 // 增
	OperateType_DELETE     OperateType = 2 // 删
	OperateType_MODIFY     OperateType = 3 // 改
)

// Enum value maps for OperateType.
var (
	OperateType_name = map[int32]string{
		0: "OT_UNKNOWN",
		1: "CREATE",
		2: "DELETE",
		3: "MODIFY",
	}
	OperateType_value = map[string]int32{
		"OT_UNKNOWN": 0,
		"CREATE":     1,
		"DELETE":     2,
		"MODIFY":     3,
	}
)

func (x OperateType) Enum() *OperateType {
	p := new(OperateType)
	*p = x
	return p
}

func (x OperateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperateType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_event_proto_enumTypes[0].Descriptor()
}

func (OperateType) Type() protoreflect.EnumType {
	return &file_common_event_proto_enumTypes[0]
}

func (x OperateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperateType.Descriptor instead.
func (OperateType) EnumDescriptor() ([]byte, []int) {
	return file_common_event_proto_rawDescGZIP(), []int{0}
}

// TargetType 对象类型
type TargetType int32

const (
	TargetType_TT_UNKNOWN TargetType = 0
	TargetType_ROUTER     TargetType = 1 // 路由服务
	TargetType_NODE       TargetType = 2 // 节点服务
	TargetType_CLIENT     TargetType = 3 // 客户端
)

// Enum value maps for TargetType.
var (
	TargetType_name = map[int32]string{
		0: "TT_UNKNOWN",
		1: "ROUTER",
		2: "NODE",
		3: "CLIENT",
	}
	TargetType_value = map[string]int32{
		"TT_UNKNOWN": 0,
		"ROUTER":     1,
		"NODE":       2,
		"CLIENT":     3,
	}
)

func (x TargetType) Enum() *TargetType {
	p := new(TargetType)
	*p = x
	return p
}

func (x TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_event_proto_enumTypes[1].Descriptor()
}

func (TargetType) Type() protoreflect.EnumType {
	return &file_common_event_proto_enumTypes[1]
}

func (x TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetType.Descriptor instead.
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return file_common_event_proto_rawDescGZIP(), []int{1}
}

// Event 事件
type Event struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Operate OperateType            `protobuf:"varint,1,opt,name=operate,proto3,enum=common.OperateType" json:"operate,omitempty"` // 操作类型
	Target  TargetType             `protobuf:"varint,2,opt,name=target,proto3,enum=common.TargetType" json:"target,omitempty"`    // 对象类型
	// Types that are valid to be assigned to Item:
	//
	//	*Event_ServerInfo
	//	*Event_ClientInfo
	Item          isEvent_Item `protobuf_oneof:"item"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Event) Reset() {
	*x = Event{}
	mi := &file_common_event_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_common_event_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_common_event_proto_rawDescGZIP(), []int{0}
}

func (x *Event) GetOperate() OperateType {
	if x != nil {
		return x.Operate
	}
	return OperateType_OT_UNKNOWN
}

func (x *Event) GetTarget() TargetType {
	if x != nil {
		return x.Target
	}
	return TargetType_TT_UNKNOWN
}

func (x *Event) GetItem() isEvent_Item {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *Event) GetServerInfo() *ServerInfo {
	if x != nil {
		if x, ok := x.Item.(*Event_ServerInfo); ok {
			return x.ServerInfo
		}
	}
	return nil
}

func (x *Event) GetClientInfo() *ClientInfo {
	if x != nil {
		if x, ok := x.Item.(*Event_ClientInfo); ok {
			return x.ClientInfo
		}
	}
	return nil
}

type isEvent_Item interface {
	isEvent_Item()
}

type Event_ServerInfo struct {
	ServerInfo *ServerInfo `protobuf:"bytes,3,opt,name=server_info,json=serverInfo,proto3,oneof"`
}

type Event_ClientInfo struct {
	ClientInfo *ClientInfo `protobuf:"bytes,4,opt,name=client_info,json=clientInfo,proto3,oneof"`
}

func (*Event_ServerInfo) isEvent_Item() {}

func (*Event_ClientInfo) isEvent_Item() {}

var File_common_event_proto protoreflect.FileDescriptor

var file_common_event_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xd8, 0x01, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x06, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x2a, 0x41, 0x0a, 0x0b, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x10, 0x03, 0x2a, 0x3e, 0x0a,
	0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x54,
	0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52,
	0x4f, 0x55, 0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x44, 0x45, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x42, 0x38, 0x5a,
	0x36, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_event_proto_rawDescOnce sync.Once
	file_common_event_proto_rawDescData = file_common_event_proto_rawDesc
)

func file_common_event_proto_rawDescGZIP() []byte {
	file_common_event_proto_rawDescOnce.Do(func() {
		file_common_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_event_proto_rawDescData)
	})
	return file_common_event_proto_rawDescData
}

var file_common_event_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_event_proto_goTypes = []any{
	(OperateType)(0),   // 0: common.OperateType
	(TargetType)(0),    // 1: common.TargetType
	(*Event)(nil),      // 2: common.Event
	(*ServerInfo)(nil), // 3: common.ServerInfo
	(*ClientInfo)(nil), // 4: common.ClientInfo
}
var file_common_event_proto_depIdxs = []int32{
	0, // 0: common.Event.operate:type_name -> common.OperateType
	1, // 1: common.Event.target:type_name -> common.TargetType
	3, // 2: common.Event.server_info:type_name -> common.ServerInfo
	4, // 3: common.Event.client_info:type_name -> common.ClientInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_common_event_proto_init() }
func file_common_event_proto_init() {
	if File_common_event_proto != nil {
		return
	}
	file_common_info_proto_init()
	file_common_event_proto_msgTypes[0].OneofWrappers = []any{
		(*Event_ServerInfo)(nil),
		(*Event_ClientInfo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_event_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_event_proto_goTypes,
		DependencyIndexes: file_common_event_proto_depIdxs,
		EnumInfos:         file_common_event_proto_enumTypes,
		MessageInfos:      file_common_event_proto_msgTypes,
	}.Build()
	File_common_event_proto = out.File
	file_common_event_proto_rawDesc = nil
	file_common_event_proto_goTypes = nil
	file_common_event_proto_depIdxs = nil
}
