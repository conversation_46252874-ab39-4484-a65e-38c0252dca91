package pb

import (
	"fmt"
	"slices"
)

func (x *ServerInfo) Address() string {
	if x == nil {
		return ""
	}

	return fmt.Sprintf("%s:%d", x.GetHost(), x.GetPort())
}

func (x *ServerInfo) Equal(o any) bool {
	v, ok := o.(*ServerInfo)
	if !ok {
		return false
	}

	if x == nil || v == nil {
		return x == nil && v == nil
	}

	if x.GetKey() != v.<PERSON>ey() {
		return false
	}
	if x.GetServerId() != v.GetServerId() {
		return false
	}
	if x.GetHost() != v.GetHost() {
		return false
	}
	if x.GetPort() != v.GetPort() {
		return false
	}
	if x.GetRegistry() != v.GetRegistry() {
		return false
	}
	if x.GetStartedAt().GetSeconds() != v.GetStartedAt().GetSeconds() ||
		x.GetStartedAt().GetNanos() != v.GetStartedAt().GetNanos() {
		return false
	}

	return true
}

func (x *RouterServerInfo) Equal(o any) bool {
	v, ok := o.(*RouterServerInfo)
	if !ok {
		return false
	}

	return x.GetInfo().Equal(v.GetInfo())
}

func (x *NodeServerInfo) Equal(o any) bool {
	v, ok := o.(*NodeServerInfo)
	if !ok {
		return false
	}

	return x.GetInfo().Equal(v.GetInfo())
}

func (x *CreateInfo) Key() (key string) {
	if x == nil {
		return ""
	}

	fields := x.GetCustomFields().GetFields()
	keys := make([]string, 0, len(fields))
	for k := range fields {
		keys = append(keys, k)
	}

	slices.Sort[[]string, string](keys)
	for _, k := range keys {
		v, ok := fields[k]
		if !ok {
			continue
		}

		vv := v.AsInterface()
		if key == "" {
			key = fmt.Sprintf("%s=%v", k, vv)
		} else {
			key = fmt.Sprintf("%s:%s=%v", key, k, vv)
		}
	}

	typ := x.GetType()
	url := x.GetUrl()
	if key == "" {
		key = fmt.Sprintf("type=%s:url=%s", typ, url)
	} else {
		key = fmt.Sprintf("type=%s:url=%s:%s", typ, url, key)
	}

	return key
}
