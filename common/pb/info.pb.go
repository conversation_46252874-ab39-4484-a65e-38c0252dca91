// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/info.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ServerInfo 服务基础信息（包括：路由服务、节点服务）
type ServerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`                              // 服务Key名称
	ServerId      string                 `protobuf:"bytes,2,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`    // 服务ID
	Host          string                 `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`                            // 服务地址
	Port          int32                  `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`                           // 服务监听端口
	Registry      string                 `protobuf:"bytes,5,opt,name=registry,proto3" json:"registry,omitempty"`                    // 注册中心（如：redis://host/db）
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"` // 服务启动时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerInfo) Reset() {
	*x = ServerInfo{}
	mi := &file_common_info_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfo) ProtoMessage() {}

func (x *ServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfo.ProtoReflect.Descriptor instead.
func (*ServerInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{0}
}

func (x *ServerInfo) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ServerInfo) GetServerId() string {
	if x != nil {
		return x.ServerId
	}
	return ""
}

func (x *ServerInfo) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ServerInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerInfo) GetRegistry() string {
	if x != nil {
		return x.Registry
	}
	return ""
}

func (x *ServerInfo) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

// RouterServerInfo 路由服务基础信息
type RouterServerInfo struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Info          *ServerInfo                `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`         // 路由服务信息
	Metadata      *RouterServerInfo_Metadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"` // 路由服务元数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouterServerInfo) Reset() {
	*x = RouterServerInfo{}
	mi := &file_common_info_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouterServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouterServerInfo) ProtoMessage() {}

func (x *RouterServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouterServerInfo.ProtoReflect.Descriptor instead.
func (*RouterServerInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{1}
}

func (x *RouterServerInfo) GetInfo() *ServerInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *RouterServerInfo) GetMetadata() *RouterServerInfo_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// NodeServerInfo 节点服务基础信息
type NodeServerInfo struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Info          *ServerInfo              `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`         // 节点服务信息
	Metadata      *NodeServerInfo_Metadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"` // 节点服务元数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeServerInfo) Reset() {
	*x = NodeServerInfo{}
	mi := &file_common_info_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeServerInfo) ProtoMessage() {}

func (x *NodeServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeServerInfo.ProtoReflect.Descriptor instead.
func (*NodeServerInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{2}
}

func (x *NodeServerInfo) GetInfo() *ServerInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *NodeServerInfo) GetMetadata() *NodeServerInfo_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// ClientInfo 客户端基础信息
type ClientInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Cid             string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"`                                                  // 客户端ID
	Nid             string                 `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"`                                                  // 所在的节点服务ID
	Product         string                 `protobuf:"bytes,3,opt,name=product,proto3" json:"product,omitempty"`                                          // 产品名称
	Type            string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                                                // 客户端类型
	Status          string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                                            // 客户端状态
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                     // 客户端的创建时间
	LastCreatedAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_created_at,json=lastCreatedAt,proto3" json:"last_created_at,omitempty"`       // 最近一次客户端的创建时间
	LastRequestedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_requested_at,json=lastRequestedAt,proto3" json:"last_requested_at,omitempty"` // 最近一次请求时间
	NumOfRequest    uint64                 `protobuf:"varint,9,opt,name=num_of_request,json=numOfRequest,proto3" json:"num_of_request,omitempty"`         // 请求次数

	CustomInfo    *structpb.Struct `protobuf:"bytes,10,opt,name=custom_info,json=customInfo,proto3" json:"custom_fields,omitempty" copier:"CustomFields"` // 客户端自定义信息，注：为了兼容原接口定义，故把序列化字段名称设置为`custom_fields`
	LoginResp     *structpb.Struct `protobuf:"bytes,11,opt,name=login_resp,json=loginResp,proto3" json:"login_resp,omitempty"`                            // 登录响应信息
	UserInfo      *UserInfo        `protobuf:"bytes,12,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`                               // 用户基础信息
	CreateInfo    *CreateInfo      `protobuf:"bytes,13,opt,name=create_info,json=createInfo,proto3" json:"create_info,omitempty"`                         // 客户端的创建信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	mi := &file_common_info_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{3}
}

func (x *ClientInfo) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *ClientInfo) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *ClientInfo) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *ClientInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ClientInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ClientInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ClientInfo) GetLastCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCreatedAt
	}
	return nil
}

func (x *ClientInfo) GetLastRequestedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRequestedAt
	}
	return nil
}

func (x *ClientInfo) GetNumOfRequest() uint64 {
	if x != nil {
		return x.NumOfRequest
	}
	return 0
}

func (x *ClientInfo) GetCustomInfo() *structpb.Struct {
	if x != nil {
		return x.CustomInfo
	}
	return nil
}

func (x *ClientInfo) GetLoginResp() *structpb.Struct {
	if x != nil {
		return x.LoginResp
	}
	return nil
}

func (x *ClientInfo) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *ClientInfo) GetCreateInfo() *CreateInfo {
	if x != nil {
		return x.CreateInfo
	}
	return nil
}

// CreateInfo 客户端的创建信息
type CreateInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                     // 客户端类型
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`                                       // 服务端连接地址
	CustomFields  *structpb.Struct       `protobuf:"bytes,3,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"` // 客户端的请求自定义字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateInfo) Reset() {
	*x = CreateInfo{}
	mi := &file_common_info_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInfo) ProtoMessage() {}

func (x *CreateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInfo.ProtoReflect.Descriptor instead.
func (*CreateInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{4}
}

func (x *CreateInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreateInfo) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

// UserInfo 用户基础信息
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cid           string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"` // 客户端ID
	Nid           string                 `protobuf:"bytes,2,opt,name=nid,proto3" json:"nid,omitempty"` // 所在的节点服务ID
	Uid           string                 `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"` // 用户唯一标识
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_common_info_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{5}
}

func (x *UserInfo) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *UserInfo) GetNid() string {
	if x != nil {
		return x.Nid
	}
	return ""
}

func (x *UserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

// AttrInfo 解析器地址的属性信息
type AttrInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *ServerInfo            `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`                                                                                 // 节点服务信息
	Clients       map[string]*ClientInfo `protobuf:"bytes,2,rep,name=clients,proto3" json:"clients,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 节点服务下全部客户端的信息
	Users         map[string]*UserInfo   `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`     // 节点服务下各客户端对应的用户信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttrInfo) Reset() {
	*x = AttrInfo{}
	mi := &file_common_info_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrInfo) ProtoMessage() {}

func (x *AttrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrInfo.ProtoReflect.Descriptor instead.
func (*AttrInfo) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{6}
}

func (x *AttrInfo) GetNode() *ServerInfo {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *AttrInfo) GetClients() map[string]*ClientInfo {
	if x != nil {
		return x.Clients
	}
	return nil
}

func (x *AttrInfo) GetUsers() map[string]*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

type RouterServerInfo_Metadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouterServerInfo_Metadata) Reset() {
	*x = RouterServerInfo_Metadata{}
	mi := &file_common_info_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouterServerInfo_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouterServerInfo_Metadata) ProtoMessage() {}

func (x *RouterServerInfo_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouterServerInfo_Metadata.ProtoReflect.Descriptor instead.
func (*RouterServerInfo_Metadata) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{1, 0}
}

type NodeServerInfo_Metadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clients       int64                  `protobuf:"varint,1,opt,name=clients,proto3" json:"clients,omitempty"` // 客户端数量
	Users         int64                  `protobuf:"varint,2,opt,name=users,proto3" json:"users,omitempty"`     // 用户数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeServerInfo_Metadata) Reset() {
	*x = NodeServerInfo_Metadata{}
	mi := &file_common_info_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeServerInfo_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeServerInfo_Metadata) ProtoMessage() {}

func (x *NodeServerInfo_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_info_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeServerInfo_Metadata.ProtoReflect.Descriptor instead.
func (*NodeServerInfo_Metadata) Descriptor() ([]byte, []int) {
	return file_common_info_proto_rawDescGZIP(), []int{2, 0}
}

func (x *NodeServerInfo_Metadata) GetClients() int64 {
	if x != nil {
		return x.Clients
	}
	return 0
}

func (x *NodeServerInfo_Metadata) GetUsers() int64 {
	if x != nil {
		return x.Users
	}
	return 0
}

var File_common_info_proto protoreflect.FileDescriptor

var file_common_info_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x01, 0x0a, 0x0a, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x12, 0x39, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x85, 0x01, 0x0a, 0x10, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x0a, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xb1, 0x01, 0x0a, 0x0e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3a, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x22, 0xb9, 0x04, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x63, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6e, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x11, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6e, 0x75, 0x6d,
	0x4f, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x70, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x3c, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x22, 0x40, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6e, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x22, 0xba, 0x02, 0x0a, 0x08, 0x41, 0x74, 0x74, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x26, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x31, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x1a, 0x4e, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4a, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x42, 0x38, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_common_info_proto_rawDescOnce sync.Once
	file_common_info_proto_rawDescData = file_common_info_proto_rawDesc
)

func file_common_info_proto_rawDescGZIP() []byte {
	file_common_info_proto_rawDescOnce.Do(func() {
		file_common_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_info_proto_rawDescData)
	})
	return file_common_info_proto_rawDescData
}

var file_common_info_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_common_info_proto_goTypes = []any{
	(*ServerInfo)(nil),                // 0: common.ServerInfo
	(*RouterServerInfo)(nil),          // 1: common.RouterServerInfo
	(*NodeServerInfo)(nil),            // 2: common.NodeServerInfo
	(*ClientInfo)(nil),                // 3: common.ClientInfo
	(*CreateInfo)(nil),                // 4: common.CreateInfo
	(*UserInfo)(nil),                  // 5: common.UserInfo
	(*AttrInfo)(nil),                  // 6: common.AttrInfo
	(*RouterServerInfo_Metadata)(nil), // 7: common.RouterServerInfo.Metadata
	(*NodeServerInfo_Metadata)(nil),   // 8: common.NodeServerInfo.Metadata
	nil,                               // 9: common.AttrInfo.ClientsEntry
	nil,                               // 10: common.AttrInfo.UsersEntry
	(*timestamppb.Timestamp)(nil),     // 11: google.protobuf.Timestamp
	(*structpb.Struct)(nil),           // 12: google.protobuf.Struct
}
var file_common_info_proto_depIdxs = []int32{
	11, // 0: common.ServerInfo.started_at:type_name -> google.protobuf.Timestamp
	0,  // 1: common.RouterServerInfo.info:type_name -> common.ServerInfo
	7,  // 2: common.RouterServerInfo.metadata:type_name -> common.RouterServerInfo.Metadata
	0,  // 3: common.NodeServerInfo.info:type_name -> common.ServerInfo
	8,  // 4: common.NodeServerInfo.metadata:type_name -> common.NodeServerInfo.Metadata
	11, // 5: common.ClientInfo.created_at:type_name -> google.protobuf.Timestamp
	11, // 6: common.ClientInfo.last_created_at:type_name -> google.protobuf.Timestamp
	11, // 7: common.ClientInfo.last_requested_at:type_name -> google.protobuf.Timestamp
	12, // 8: common.ClientInfo.custom_info:type_name -> google.protobuf.Struct
	12, // 9: common.ClientInfo.login_resp:type_name -> google.protobuf.Struct
	5,  // 10: common.ClientInfo.user_info:type_name -> common.UserInfo
	4,  // 11: common.ClientInfo.create_info:type_name -> common.CreateInfo
	12, // 12: common.CreateInfo.custom_fields:type_name -> google.protobuf.Struct
	0,  // 13: common.AttrInfo.node:type_name -> common.ServerInfo
	9,  // 14: common.AttrInfo.clients:type_name -> common.AttrInfo.ClientsEntry
	10, // 15: common.AttrInfo.users:type_name -> common.AttrInfo.UsersEntry
	3,  // 16: common.AttrInfo.ClientsEntry.value:type_name -> common.ClientInfo
	5,  // 17: common.AttrInfo.UsersEntry.value:type_name -> common.UserInfo
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_common_info_proto_init() }
func file_common_info_proto_init() {
	if File_common_info_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_info_proto_goTypes,
		DependencyIndexes: file_common_info_proto_depIdxs,
		MessageInfos:      file_common_info_proto_msgTypes,
	}.Build()
	File_common_info_proto = out.File
	file_common_info_proto_rawDesc = nil
	file_common_info_proto_goTypes = nil
	file_common_info_proto_depIdxs = nil
}
