package pb

import (
	"reflect"
	"strconv"
	"unicode/utf8"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
)

func NewValue(v any) (*Value, error) {
	typ := Value_NULL

	if v == nil {
		return &Value{
			Type:  typ,
			Value: nil,
		}, nil
	}

	rv := reflect.ValueOf(v)
	rv = reflect.Indirect(rv)

	switch rv.Kind() {
	case reflect.Bool:
		typ = Value_BOOL
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		typ = Value_INT
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		typ = Value_UINT
	case reflect.Float32, reflect.Float64:
		typ = Value_FLOAT
	case reflect.String:
		typ = Value_STRING
	case reflect.Map, reflect.Struct:
		typ = Value_OBJECT
	case reflect.Slice:
		typ = Value_ARRAY
	default:
		return nil, errors.Errorf("invalid value type, type: %T, value: %v", v, v)
	}

	bs, err := jsonx.Marshal(v)
	if err != nil {
		return nil, err
	}

	return &Value{
		Type:  typ,
		Value: bs,
	}, nil
}

func (x *Value) AsGoAny() (any, error) {
	if x == nil {
		return nil, nil
	}

	val := x.GetValue()
	if val == nil {
		return nil, nil
	}

	var v any
	if err := jsonx.Unmarshal(val, &v); err != nil {
		return nil, err
	}

	rv := reflect.ValueOf(v)
	rv = reflect.Indirect(rv)
	kind := rv.Kind()

	switch typ := x.GetType(); typ {
	case Value_NULL:
		return nil, nil
	case Value_BOOL, Value_STRING, Value_OBJECT, Value_ARRAY:
		return v, nil
	case Value_INT:
		switch kind {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return rv.Int(), nil
		case reflect.String:
			return strconv.ParseInt(rv.String(), 10, 64)
		default:
			return nil, errors.Errorf("invalid value type, expectd: %s, but got: %s", Value_INT.String(), kind.String())
		}
	case Value_UINT:
		switch kind {
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return rv.Uint(), nil
		case reflect.String:
			return strconv.ParseUint(rv.String(), 10, 64)
		default:
			return nil, errors.Errorf(
				"invalid value type, expectd: %s, but got: %s", Value_UINT.String(), kind.String(),
			)
		}
	case Value_FLOAT:
		switch kind {
		case reflect.Float32, reflect.Float64:
			return rv.Float(), nil
		case reflect.String:
			return strconv.ParseFloat(rv.String(), 64)
		default:
			return nil, errors.Errorf(
				"invalid value type, expectd: %s, but got: %s", Value_FLOAT.String(), kind.String(),
			)
		}
	default:
		return nil, errors.Errorf("invalid value type: %s", typ.String())
	}
}

func NewObject(v map[string]any) (*Object, error) {
	x := &Object{
		Fields: make(map[string]*Value, len(v)),
	}
	for key, val := range v {
		if !utf8.ValidString(key) {
			return nil, errors.Errorf("invalid UTF-8 in string: %q", key)
		}

		var err error
		x.Fields[key], err = NewValue(val)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}

func (x *Object) AsGoMap() (map[string]any, error) {
	fs := x.GetFields()
	m := make(map[string]any, len(fs))
	for key, val := range fs {
		var err error
		m[key], err = val.AsGoAny()
		if err != nil {
			return nil, err
		}
	}

	return m, nil
}

func NewArray(v []any) (*Array, error) {
	x := &Array{
		Items: make([]*Value, len(v)),
	}
	for index, val := range v {
		var err error
		x.Items[index], err = NewValue(val)
		if err != nil {
			return nil, err
		}
	}

	return x, nil
}

func (x *Array) AsGoSlice() ([]any, error) {
	items := x.GetItems()
	s := make([]any, len(items))
	for index, val := range items {
		var err error
		s[index], err = val.AsGoAny()
		if err != nil {
			return nil, err
		}
	}

	return s, nil
}
