package pb

import (
	"reflect"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
)

var (
	object = map[string]any{
		"name": "allen",
		"age":  18,
	}
	array = []any{nil, false, 1, -2, 3.4, "5"}
)

func TestNewValue(t *testing.T) {
	type args struct {
		v any
	}
	tests := []struct {
		name    string
		args    args
		want    *Value
		wantErr bool
	}{
		{
			name: "null",
			args: args{
				v: nil,
			},
			want: &Value{
				Type:  Value_NULL,
				Value: nil,
			},
			wantErr: false,
		},
		{
			name: "bool",
			args: args{
				v: true,
			},
			want: &Value{
				Type:  Value_BOOL,
				Value: []byte("true"),
			},
			wantErr: false,
		},
		{
			name: "int",
			args: args{
				v: -123,
			},
			want: &Value{
				Type:  Value_INT,
				Value: []byte("-123"),
			},
			wantErr: false,
		},
		{
			name: "uint",
			args: args{
				v: uint(1234),
			},
			want: &Value{
				Type:  Value_UINT,
				Value: []byte("1234"),
			},
			wantErr: false,
		},
		{
			name: "float",
			args: args{
				v: 12.34,
			},
			want: &Value{
				Type:  Value_FLOAT,
				Value: []byte("12.34"),
			},
			wantErr: false,
		},
		{
			name: "int64",
			args: args{
				v: int64(7140289139090698240),
			},
			want: &Value{
				Type:  Value_INT,
				Value: []byte("7140289139090698240"),
			},
			wantErr: false,
		},
		{
			name: "string",
			args: args{
				v: "allen",
			},
			want: &Value{
				Type:  Value_STRING,
				Value: []byte("\"allen\""),
			},
			wantErr: false,
		},
		{
			name: "object",
			args: args{
				v: object,
			},
			want: &Value{
				Type:  Value_OBJECT,
				Value: jsonx.MarshalIgnoreError(object),
			},
			wantErr: false,
		},
		{
			name: "array",
			args: args{
				v: array,
			},
			want: &Value{
				Type:  Value_ARRAY,
				Value: jsonx.MarshalIgnoreError(array),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := NewValue(tt.args.v)
				if (err != nil) != tt.wantErr {
					t.Errorf("NewValue() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("NewValue() got = %v, want %v", got, tt.want)
				}

				v, err := got.AsGoAny()
				if err != nil {
					t.Errorf("AsGoAny(), error: %v", err)
				}

				t.Logf("AsGoAny(), value: %T, %v", v, v)
			},
		)
	}
}
