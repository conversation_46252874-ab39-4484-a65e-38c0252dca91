// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/json.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Value_Type int32

const (
	Value_NULL   Value_Type = 0
	Value_BOOL   Value_Type = 1
	Value_INT    Value_Type = 2
	Value_UINT   Value_Type = 3
	Value_FLOAT  Value_Type = 4
	Value_STRING Value_Type = 5
	Value_OBJECT Value_Type = 6
	Value_ARRAY  Value_Type = 7
)

// Enum value maps for Value_Type.
var (
	Value_Type_name = map[int32]string{
		0: "NULL",
		1: "BOOL",
		2: "INT",
		3: "UINT",
		4: "FLOAT",
		5: "STRING",
		6: "OBJECT",
		7: "ARRAY",
	}
	Value_Type_value = map[string]int32{
		"NULL":   0,
		"BOOL":   1,
		"INT":    2,
		"UINT":   3,
		"FLOAT":  4,
		"STRING": 5,
		"OBJECT": 6,
		"ARRAY":  7,
	}
)

func (x Value_Type) Enum() *Value_Type {
	p := new(Value_Type)
	*p = x
	return p
}

func (x Value_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Value_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_common_json_proto_enumTypes[0].Descriptor()
}

func (Value_Type) Type() protoreflect.EnumType {
	return &file_common_json_proto_enumTypes[0]
}

func (x Value_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Value_Type.Descriptor instead.
func (Value_Type) EnumDescriptor() ([]byte, []int) {
	return file_common_json_proto_rawDescGZIP(), []int{0, 0}
}

// `Value` represents a dynamically typed value which can be either
// null, a number, a string, a boolean, a recursive struct value, or a
// list of values.
//
// The JSON representation for `Value` is JSON value.
type Value struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          Value_Type             `protobuf:"varint,1,opt,name=type,proto3,enum=common.Value_Type" json:"type,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Value) Reset() {
	*x = Value{}
	mi := &file_common_json_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_common_json_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_common_json_proto_rawDescGZIP(), []int{0}
}

func (x *Value) GetType() Value_Type {
	if x != nil {
		return x.Type
	}
	return Value_NULL
}

func (x *Value) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

// `Object` represents a structured data value, consisting of fields
// which map to dynamically typed values.
//
// The JSON representation for `Object` is JSON object.
type Object struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fields        map[string]*Value      `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Object) Reset() {
	*x = Object{}
	mi := &file_common_json_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object) ProtoMessage() {}

func (x *Object) ProtoReflect() protoreflect.Message {
	mi := &file_common_json_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object.ProtoReflect.Descriptor instead.
func (*Object) Descriptor() ([]byte, []int) {
	return file_common_json_proto_rawDescGZIP(), []int{1}
}

func (x *Object) GetFields() map[string]*Value {
	if x != nil {
		return x.Fields
	}
	return nil
}

// `Array` is a wrapper around a repeated field of values.
//
// The JSON representation for `Array` is JSON array.
type Array struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Value               `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Array) Reset() {
	*x = Array{}
	mi := &file_common_json_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Array) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Array) ProtoMessage() {}

func (x *Array) ProtoReflect() protoreflect.Message {
	mi := &file_common_json_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Array.ProtoReflect.Descriptor instead.
func (*Array) Descriptor() ([]byte, []int) {
	return file_common_json_proto_rawDescGZIP(), []int{2}
}

func (x *Array) GetItems() []*Value {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_common_json_proto protoreflect.FileDescriptor

var file_common_json_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xa2, 0x01, 0x0a, 0x05,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x5b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e,
	0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x49, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a,
	0x45, 0x43, 0x54, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x52, 0x52, 0x41, 0x59, 0x10, 0x07,
	0x22, 0x86, 0x01, 0x0a, 0x06, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a,
	0x48, 0x0a, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2c, 0x0a, 0x05, 0x41, 0x72, 0x72,
	0x61, 0x79, 0x12, 0x23, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42, 0x38, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70,
	0x69, 0x2d, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_json_proto_rawDescOnce sync.Once
	file_common_json_proto_rawDescData = file_common_json_proto_rawDesc
)

func file_common_json_proto_rawDescGZIP() []byte {
	file_common_json_proto_rawDescOnce.Do(func() {
		file_common_json_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_json_proto_rawDescData)
	})
	return file_common_json_proto_rawDescData
}

var file_common_json_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_json_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_common_json_proto_goTypes = []any{
	(Value_Type)(0), // 0: common.Value.Type
	(*Value)(nil),   // 1: common.Value
	(*Object)(nil),  // 2: common.Object
	(*Array)(nil),   // 3: common.Array
	nil,             // 4: common.Object.FieldsEntry
}
var file_common_json_proto_depIdxs = []int32{
	0, // 0: common.Value.type:type_name -> common.Value.Type
	4, // 1: common.Object.fields:type_name -> common.Object.FieldsEntry
	1, // 2: common.Array.items:type_name -> common.Value
	1, // 3: common.Object.FieldsEntry.value:type_name -> common.Value
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_common_json_proto_init() }
func file_common_json_proto_init() {
	if File_common_json_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_json_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_json_proto_goTypes,
		DependencyIndexes: file_common_json_proto_depIdxs,
		EnumInfos:         file_common_json_proto_enumTypes,
		MessageInfos:      file_common_json_proto_msgTypes,
	}.Build()
	File_common_json_proto = out.File
	file_common_json_proto_rawDesc = nil
	file_common_json_proto_goTypes = nil
	file_common_json_proto_depIdxs = nil
}
