package common

import "github.com/redis/go-redis/v9"

var (
	// ServerRegisterScript returns a redis script for server registration.
	//
	// INPUT:
	// KEYS[1]: <server_name>:info:<server_id>:string
	// --
	// ARGV[1]: server info
	// ARGV[2]: ttl for server info (value: >= 10, unit: second)
	ServerRegisterScript = redis.NewScript(
		`
if redis.call("GET", KEYS[1]) == ARGV[1] then
	if redis.call("TTL", KEYS[1]) < (tonumber(ARGV[2]) / 2) then
		redis.call("EXPIRE", KEYS[1], ARGV[2])
	end
else
	redis.call("SET", KEYS[1], ARGV[1], "EX", ARGV[2])
end

return redis.status_reply("OK")
`,
	)

	// ClientRegisterScript returns a redis script for client registration.
	// Deprecated: use ClientRegisterScriptV2 instead.
	//
	// INPUT:
	// KEYS[1]: client:info:<client_id>:string
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: user:info:<user_id>:string
	// KEYS[4]: node:users:<node_id>:hash
	// --
	// ARGV[1]: client info
	// ARGV[2]: user info
	// ARGV[3]: ttl for client info and user info (value: >= 20, unit: second)
	// ARGV[4]: ttl for the relationship between the node service and the clients (value: >= 60, unit: second)
	ClientRegisterScript = redis.NewScript(
		`
if redis.call("GET", KEYS[1]) == ARGV[1] then
	if redis.call("TTL", KEYS[1]) < (tonumber(ARGV[3]) / 2) then
		redis.call("EXPIRE", KEYS[1], ARGV[3])
	end
else
	redis.call("SET", KEYS[1], ARGV[1], "EX", ARGV[3])
end

redis.call("HSETNX", KEYS[2], KEYS[1], ARGV[1])
if redis.call("TTL", KEYS[2]) < (tonumber(ARGV[4]) / 6) then
	redis.call("EXPIRE", KEYS[2], ARGV[4])
end

if KEYS[3] ~= "" then
	if redis.call("GET", KEYS[3]) == ARGV[2] then
		if redis.call("TTL", KEYS[3]) < (tonumber(ARGV[3]) / 2) then
			redis.call("EXPIRE", KEYS[3], ARGV[3])
		end
	else
		redis.call("SET", KEYS[3], ARGV[2], "EX", ARGV[3])
	end

	redis.call("HSETNX", KEYS[4], KEYS[3], ARGV[2])
	if redis.call("TTL", KEYS[4]) < (tonumber(ARGV[4]) / 6) then
		redis.call("EXPIRE", KEYS[4], ARGV[4])
	end
end

return redis.status_reply("OK")
`,
	)

	// ClientRegisterScriptV2 returns a redis script for client registration.
	// Deprecated: use ClientRegisterScriptV3 instead.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	// --
	// ARGV[1]: operation (register or keepalive)
	// ARGV[2]: client id
	// ARGV[3]: client info
	// ARGV[4]: user id
	// ARGV[5]: user info
	// ARGV[6]: ttl for all clients and users (value: >= 20, unit: second)
	// ARGV[7]: ttl for the relationship between the node service and the clients (value: >= 60, unit: second)
	ClientRegisterScriptV2 = redis.NewScript(
		`
if ARGV[1] == "register" then
	redis.call("HSET", KEYS[1], ARGV[2], ARGV[3])
	redis.call("HSET", KEYS[2], ARGV[2], ARGV[3])
else
	redis.call("HSETNX", KEYS[1], ARGV[2], ARGV[3])
	redis.call("HSETNX", KEYS[2], ARGV[2], ARGV[3])
end
if redis.call("TTL", KEYS[1]) < (tonumber(ARGV[6]) / 2) then
	redis.call("EXPIRE", KEYS[1], ARGV[6])
end
if redis.call("TTL", KEYS[2]) < (tonumber(ARGV[7]) / 6) then
	redis.call("EXPIRE", KEYS[2], ARGV[7])
end

if ARGV[4] ~= "" then
	if ARGV[1] == "register" then
		redis.call("HSET", KEYS[3], ARGV[4], ARGV[5])
		redis.call("HSET", KEYS[4], ARGV[4], ARGV[5])
	else
		redis.call("HSETNX", KEYS[3], ARGV[4], ARGV[5])
		redis.call("HSETNX", KEYS[4], ARGV[4], ARGV[5])
	end
	if redis.call("TTL", KEYS[3]) < (tonumber(ARGV[6]) / 2) then
		redis.call("EXPIRE", KEYS[3], ARGV[6])
	end	
	if redis.call("TTL", KEYS[4]) < (tonumber(ARGV[7]) / 6) then
		redis.call("EXPIRE", KEYS[4], ARGV[7])
	end
end

return redis.status_reply("OK")
`,
	)

	// ClientRegisterScriptV3 returns a redis script for client registration.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	// KEYS[5]: unmanaged_clients:info:hash
	// --
	// ARGV[1]: operation (register or keepalive)
	// ARGV[2]: client id
	// ARGV[3]: client info
	// ARGV[4]: user id
	// ARGV[5]: user info
	// ARGV[6]: ttl for all clients and users (value: >= 20, unit: second)
	// ARGV[7]: ttl for the relationship between the node service and the clients (value: >= 60, unit: second)
	ClientRegisterScriptV3 = redis.NewScript(
		`
if ARGV[1] == "register" then
	redis.call("HSET", KEYS[1], ARGV[2], ARGV[3])
	redis.call("HSET", KEYS[2], ARGV[2], ARGV[3])
else
	redis.call("HSETNX", KEYS[1], ARGV[2], ARGV[3])
	redis.call("HSETNX", KEYS[2], ARGV[2], ARGV[3])
end
if redis.call("TTL", KEYS[1]) < (tonumber(ARGV[6]) / 2) then
	redis.call("EXPIRE", KEYS[1], ARGV[6])
end
if redis.call("TTL", KEYS[2]) < (tonumber(ARGV[7]) / 6) then
	redis.call("EXPIRE", KEYS[2], ARGV[7])
end

if ARGV[4] ~= "" then
	if ARGV[1] == "register" then
		redis.call("HSET", KEYS[3], ARGV[4], ARGV[5])
		redis.call("HSET", KEYS[4], ARGV[4], ARGV[5])
	else
		redis.call("HSETNX", KEYS[3], ARGV[4], ARGV[5])
		redis.call("HSETNX", KEYS[4], ARGV[4], ARGV[5])
	end
	if redis.call("TTL", KEYS[3]) < (tonumber(ARGV[6]) / 2) then
		redis.call("EXPIRE", KEYS[3], ARGV[6])
	end	
	if redis.call("TTL", KEYS[4]) < (tonumber(ARGV[7]) / 6) then
		redis.call("EXPIRE", KEYS[4], ARGV[7])
	end
end

redis.call("HDEL", KEYS[5], ARGV[2])

return redis.status_reply("OK")
`,
	)

	// ClientUnregisterScript returns a redis script for client de-registration.
	// Deprecated: use ClientUnregisterScriptV2 instead.
	//
	// INPUT:
	// KEYS[1]: client:info:<client_id>:string
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: user:info:<user_id>:string
	// KEYS[4]: node:users:<node_id>:hash
	ClientUnregisterScript = redis.NewScript(
		`
redis.call("DEL", KEYS[1])
redis.call("HDEL", KEYS[2], KEYS[1])
if KEYS[3] ~= "" then
	redis.call("DEL", KEYS[3])
	redis.call("HDEL", KEYS[4], KEYS[3])
end

return redis.status_reply("OK")
`,
	)

	// ClientUnregisterScriptV2 returns a redis script for client de-registration.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	// --
	// ARGV[1]: client id
	// ARGV[2]: user id
	ClientUnregisterScriptV2 = redis.NewScript(
		`
redis.call("HDEL", KEYS[1], ARGV[1])
redis.call("HDEL", KEYS[2], ARGV[1])
if ARGV[2] ~= "" then
	redis.call("HDEL", KEYS[3], ARGV[2])
	redis.call("HDEL", KEYS[4], ARGV[2])
end

return redis.status_reply("OK")
`,
	)

	// AllClientsUnregisterScript returns a redis script for all clients de-registration.
	// Deprecated: use AllClientsUnregisterScriptV2 instead.
	//
	// INPUT:
	// KEYS[1]: node:clients:<node_id>:hash
	// KEYS[2]: node:users:<node_id>:hash
	AllClientsUnregisterScript = redis.NewScript(
		`
local clientKeys = redis.call("HKEYS"， KEYS[1])
if #clientKeys > 0 then
	redis.call("DEL", unpack(clientKeys))
end

redis.call("DEL", KEYS[1])

local userKeys = redis.call("HKEYS", KEYS[2])
if #userKeys > 0 then
	redis.call("DEL", unpack(userKeys))
end

redis.call("DEL", KEYS[2])

return redis.status_reply("OK")
`,
	)

	// AllClientsUnregisterScriptV2 returns a redis script for all clients de-registration.
	// Deprecated: use AllClientsUnregisterScriptV3 instead.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	AllClientsUnregisterScriptV2 = redis.NewScript(
		`
local clientKeys = redis.call("HKEYS", KEYS[2])
if #clientKeys > 0 then
	redis.call("HDEL", KEYS[1], unpack(clientKeys))
end

redis.call("DEL", KEYS[2])

local userKeys = redis.call("HKEYS", KEYS[4])
if #userKeys > 0 then
	redis.call("HDEL", KEYS[3], unpack(userKeys))
end

redis.call("DEL", KEYS[4])

return redis.status_reply("OK")
`,
	)

	// AllClientsUnregisterScriptV3 returns a redis script for all clients de-registration.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	// KEYS[5]: unmanaged_clients:info:hash
	AllClientsUnregisterScriptV3 = redis.NewScript(
		`
local data = redis.call("HGETALL", KEYS[2])
if #data > 0 then
	local fields = {}
	for i=1, #data, 2 do
		local field, value = data[i], data[i+1]
		redis.call("HSET", KEYS[5], field, value)
		table.insert(fields, field)
	end

	if #fields > 0 then
		redis.call("HDEL", KEYS[1], unpack(fields))
	end
end

redis.call("DEL", KEYS[2])

local userKeys = redis.call("HKEYS", KEYS[4])
if #userKeys > 0 then
	redis.call("HDEL", KEYS[3], unpack(userKeys))
end

redis.call("DEL", KEYS[4])

return redis.status_reply("OK")
`,
	)

	// GetMatchServerScript returns a redis script for retrieving all matching service information.
	// Note: the total number of services cannot be too much, as using SCAN for iteration in
	// Lua script would block other requests.
	//
	// INPUT:
	// KEYS[1]: <server_name>:info:*
	GetMatchServerScript = redis.NewScript(
		`
local cursor = 0
local keys = {}
local match = KEYS[1]

repeat
    local result = redis.call("SCAN", cursor, "MATCH", match)
    cursor = tonumber(result[1])
    local scanKeys = result[2]

    for _, key in ipairs(scanKeys) do
        table.insert(keys, key)
    end
until cursor == 0

if #keys > 0 then
	return redis.call("MGET", unpack(keys))
end

return {}
`,
	)

	// GetAllClientsOrUsersScript returns a redis script for retrieving all client or user
	// information under the specified node service.
	//
	// INPUT:
	// KEYS[1]: node:clients:<node_id>:hash or node:users:<node_id>:hash
	GetAllClientsOrUsersScript = redis.NewScript(
		`
local result = redis.call("HGETALL", KEYS[1])

local values = {}
for i = 2, #result, 2 do
	table.insert(values, result[i])
end

return values
`,
	)

	// KeepaliveAllInfoScript returns a redis script for refreshing the ttl of
	// all client and user information under the specified node service.
	//
	// INPUT:
	// KEYS[1]: clients:info:hash
	// KEYS[2]: node:clients:<node_id>:hash
	// KEYS[3]: users:info:hash
	// KEYS[4]: node:users:<node_id>:hash
	// --
	// ARGV[1]: ttl for all clients and users (value: >= 20, unit: second)
	// ARGV[2]: ttl for the relationship between the node service and the clients (value: >= 60, unit: second)
	KeepaliveAllInfoScript = redis.NewScript(
		`
if redis.call("TTL", KEYS[1]) < (tonumber(ARGV[1]) / 2) then
	redis.call("EXPIRE", KEYS[1], ARGV[1])
end
if redis.call("TTL", KEYS[2]) < (tonumber(ARGV[2]) / 6) then
	redis.call("EXPIRE", KEYS[2], ARGV[2])
end

if redis.call("EXISTS", KEYS[4]) == 1 then
	if redis.call("TTL", KEYS[3]) < (tonumber(ARGV[1]) / 2) then
		redis.call("EXPIRE", KEYS[3], ARGV[1])
	end
	if redis.call("TTL", KEYS[4]) < (tonumber(ARGV[2]) / 6) then
		redis.call("EXPIRE", KEYS[4], ARGV[2])
	end
end

return redis.status_reply("OK")
`,
	)
)
