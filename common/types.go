package common

import "time"

type (
	// ClientOptions gRPC客户端keepalive选项
	// google.golang.org/grpc: keepalive.ClientParameters
	ClientOptions struct {
		KeepaliveTime                time.Duration `json:",optional,default=20s"`  // 客户端keepalive间隔
		KeepaliveTimeout             time.Duration `json:",optional,default=5s"`   // keepalive超时时间
		KeepalivePermitWithoutStream bool          `json:",optional,default=true"` // 允许无流时keepalive
	}

	// ServerOptions gRPC服务端keepalive选项
	// google.golang.org/grpc: keepalive.ServerParameters, keepalive.EnforcementPolicy
	ServerOptions struct {
		MaxConnectionAge             time.Duration `json:",optional,default=0s"`   // 连接最大存活时间，0表示无限制
		MaxConnectionIdle            time.Duration `json:",optional,default=0s"`   // 连接最大空闲时间，0表示无限制
		MaxConnectionAgeGrace        time.Duration `json:",optional,default=0s"`   // 连接年龄宽限期，0表示无限制
		KeepaliveTime                time.Duration `json:",optional,default=30s"`  // 服务端keepalive间隔
		KeepaliveTimeout             time.Duration `json:",optional,default=5s"`   // keepalive超时时间
		KeepaliveMinTime             time.Duration `json:",optional,default=10s"`  // 客户端keepalive最小间隔
		KeepalivePermitWithoutStream bool          `json:",optional,default=true"` // 允许无流时keepalive
	}
)
