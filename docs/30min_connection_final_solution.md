# 30分钟连接问题最终解决方案

## 问题总结

经过深入分析，确认30分钟连接定期断开的根本原因是：

**gRPC服务器端的默认`MaxConnectionAge`设置（30分钟）导致服务器主动关闭连接**

## 完整解决方案

### 1. 服务器端连接管理（主要解决方案）

**修改node服务配置文件**：

```yaml
# node/rpc/etc/node.yaml
ServerOptions:
  MaxConnectionAge: 0                # 禁用连接年龄限制，0s表示无限制
  MaxConnectionIdle: 0               # 禁用空闲连接超时，0s表示无限制
  MaxConnectionAgeGrace: 0           # 禁用连接年龄宽限期
  KeepaliveTime: 30s                 # 服务端keepalive间隔
  KeepaliveTimeout: 5s               # keepalive超时时间
  KeepaliveMinTime: 10s              # 客户端keepalive最小间隔
  KeepalivePermitWithoutStream: true # 允许无流时keepalive
```

### 2. 客户端预防性刷新（辅助解决方案）

resolver已设置为25分钟刷新，在服务器30分钟连接重置之前主动刷新：

```go
// router/api/resolver/resolver.go
const defaultTickerDuration = time.Minute * 25
```

### 3. 健康检查集成（新增功能）

新增了`HealthChecker`组件，为resolver提供gRPC健康检查功能：

**主要特性**：
- 使用`grpc_health_v1.HealthClient`进行健康检查
- 支持`Check`方法进行单次检查
- 支持`Watch`方法进行持续监控
- 自动过滤不健康的地址，避免将请求路由到不可用的节点

**核心功能**：
```go
// 为每个发现的node服务创建健康检查
healthChecker.AddAddress(nodeAddress)

// 在updateTargetState中过滤不健康的地址
if !healthChecker.IsHealthy(addr) {
    // 排除不健康的地址
    continue
}
```

## 技术实现细节

### gRPC连接管理参数说明

1. **MaxConnectionAge**: 连接最大存活时间
   - 默认值通常为30分钟
   - 设置为0禁用连接年龄限制

2. **MaxConnectionIdle**: 连接最大空闲时间
   - 控制空闲连接的生命周期
   - 设置为0禁用空闲超时

3. **MaxConnectionAgeGrace**: 连接年龄宽限期
   - 连接关闭前的宽限时间
   - 允许正在进行的请求完成

### 健康检查工作原理

1. **地址发现**: resolver发现新的node地址时，自动添加健康检查
2. **双重检查**: 同时使用Check和Watch方法确保及时发现状态变化
3. **智能过滤**: 在更新balancer状态时，只包含健康的地址
4. **自动清理**: 地址移除时，自动清理对应的健康检查

## 部署步骤

### 1. 更新配置文件

确保node服务配置文件中的ServerOptions已正确设置（已完成）。

### 2. 编译和部署

```bash
# 编译服务
make build

# 部署node服务（包含gRPC服务器配置修改）
kubectl rollout restart deployment api-proxy-node

# 等待node服务稳定
kubectl rollout status deployment api-proxy-node

# 部署router服务（包含健康检查功能）
kubectl rollout restart deployment api-proxy-router

# 等待router服务稳定
kubectl rollout status deployment api-proxy-router
```

### 3. 验证部署

```bash
# 运行连接稳定性测试
./scripts/test_connection_stability.sh

# 检查健康检查日志
kubectl logs -l app=api-proxy-router | grep -i health

# 监控连接状态
kubectl logs -f -l app=api-proxy-router | grep "ready sub conns"
```

## 监控和验证

### 关键日志监控

1. **连接状态日志**：
```bash
kubectl logs -l app=api-proxy-router | grep "ready sub conns"
```

2. **健康检查日志**：
```bash
kubectl logs -l app=api-proxy-router | grep "health check"
```

3. **resolver统计日志**：
```bash
kubectl logs -l app=api-proxy-router | grep "Resolver Monitor Stats"
```

### 成功指标

- **零连接事件频率**：应该显著减少或完全消失
- **健康检查成功率**：应该保持在95%以上
- **连接恢复时间**：如果出现问题，恢复时间应该在10秒内

## 故障排除

### 如果问题仍然存在

1. **验证gRPC服务器配置**：
```bash
kubectl logs -l app=api-proxy-node | grep -i "MaxConnection\|keepalive"
```

2. **检查健康检查状态**：
```bash
kubectl logs -l app=api-proxy-router | grep "health.*failed\|unhealthy"
```

3. **监控gRPC连接日志**：
```bash
kubectl logs -l app=api-proxy-node | grep -i "connection\|close\|goaway"
```

### 配置调优

如果仍有少量连接问题，可以调整以下参数：

```go
// router/api/resolver/resolver.go
const (
    defaultTickerDuration = time.Minute * 20  // 减少到20分钟
    addressExpireTime     = time.Minute * 15  // 增加到15分钟
)
```

## 预期效果

通过这个完整的解决方案：

1. **根本解决30分钟连接重置问题**：通过禁用MaxConnectionAge
2. **提高系统可靠性**：通过健康检查避免路由到不可用节点
3. **增强监控能力**：通过详细的日志和统计信息
4. **保持向后兼容**：不影响现有功能

这个解决方案结合了服务器端配置修改、客户端预防性措施和健康检查机制，形成了一个全面的连接稳定性保障体系。
