# 30分钟连接问题修复指南

## 问题描述

在K8s环境中，api-proxy-router服务每隔约30分钟会出现所有gRPC连接数变为0的问题，表现为：
- 日志显示 `ready sub conns: [0, null]` (8次)
- 然后连接逐步恢复到 `ready sub conns: [8, [...]]`
- 问题具有明显的30分钟周期性

## 根本原因

1. **30分钟定时器触发**：resolver每30分钟主动刷新节点信息
2. **Redis临时问题**：在K8s环境中，Redis查询可能因网络延迟、负载等原因返回空结果
3. **过度清理机制**：原始代码无条件清空所有不在Redis结果中的地址
4. **连接强制关闭**：balancer收到空地址列表后主动关闭所有gRPC连接

## 修复方案

### 1. 智能地址管理
- **地址过期时间**：10分钟宽限期，只有长时间未确认的地址才被移除
- **连续空解析保护**：连续3次空解析后才开始清理地址
- **最后确认时间追踪**：记录每个地址最后在Redis中确认的时间

### 2. 增强监控
- **连接状态监控器**：实时追踪连接状态变化
- **resolver监控器**：记录解析统计和地址历史
- **详细日志记录**：包含连接状态变化的详细信息

### 3. 配置优化
- **节点注册TTL**：从10秒增加到30秒
- **resolver刷新频率**：从30分钟减少到5分钟
- **gRPC keepalive**：启用30秒keepalive机制

## 关键配置参数

### router/api/resolver/resolver.go
```go
const (
    defaultTickerDuration = time.Minute * 5  // resolver刷新间隔
    addressExpireTime     = time.Minute * 10 // 地址过期时间
    maxConsecutiveEmptyResolves = 3          // 最大连续空解析次数
)
```

### registry/registry.go
```go
const (
    defaultExpireTime = 30 // 节点注册TTL (秒)
)
```

### router/api/etc/router.yaml
```yaml
NodeRPC:
  KeepaliveTime: 30s
  KeepaliveTimeout: 5s
  KeepalivePermitWithoutStream: true
```

## 验证修复效果

### 1. 运行测试脚本
```bash
./scripts/test_30min_fix.sh
```

### 2. 监控关键日志
```bash
# 检查保护机制是否工作
kubectl logs -l app=api-proxy-router | grep "keeping existing addresses"

# 检查地址过期日志
kubectl logs -l app=api-proxy-router | grep "removing expired address"

# 检查连续空解析保护
kubectl logs -l app=api-proxy-router | grep "consecutive empty resolve"
```

### 3. 监控指标
- **零连接事件频率**：应该显著减少或消失
- **连接恢复时间**：如果出现问题，恢复时间应该更快
- **Redis查询成功率**：监控Redis连接健康状态

## 部署建议

### 1. 分阶段部署
```bash
# 1. 先部署node服务（向后兼容）
kubectl rollout restart deployment api-proxy-node

# 2. 等待node服务稳定
kubectl rollout status deployment api-proxy-node

# 3. 部署router服务
kubectl rollout restart deployment api-proxy-router
```

### 2. 监控部署效果
```bash
# 监控连接状态
kubectl logs -f -l app=api-proxy-router | grep "ready sub conns"

# 检查Redis节点注册
redis-cli keys "node:info:*" | wc -l
```

## 故障排除

### 如果问题仍然存在

1. **检查Redis性能**
   ```bash
   redis-cli --latency-history -h redis-host
   ```

2. **增加地址过期时间**
   ```go
   addressExpireTime = time.Minute * 20 // 增加到20分钟
   ```

3. **增加连续空解析阈值**
   ```go
   maxConsecutiveEmptyResolves = 5 // 增加到5次
   ```

4. **检查网络策略**
   - 验证K8s网络策略是否影响Redis连接
   - 检查DNS解析延迟

### 回滚方案

如果修复导致其他问题，可以通过以下方式回滚：

1. **恢复原始配置**
   ```bash
   git checkout HEAD~1 -- router/api/resolver/resolver.go
   git checkout HEAD~1 -- registry/registry.go
   ```

2. **重新部署**
   ```bash
   kubectl rollout restart deployment api-proxy-router
   ```

## 长期监控

建议设置以下告警：
- 零连接事件告警
- Redis连接失败告警
- 连接恢复时间过长告警
- resolver空解析频率告警

通过这些修复和监控措施，30分钟连接问题应该得到根本解决。
