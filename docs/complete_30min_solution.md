# 30分钟连接问题完整解决方案

## 问题根本原因确认

经过深入分析，确认30分钟连接重置问题的根本原因是：

**gRPC服务器端的默认`MaxConnectionAge`设置（30分钟）**

这导致：
1. gRPC服务器每30分钟主动关闭所有连接
2. 所有8个连接同时被关闭
3. balancer显示`ready sub conns: [0, null]`
4. 连接逐步重新建立

## 完整解决方案

### 方案1：服务器端连接管理（主要解决方案）

通过go-zero的`AddOptions`方法配置gRPC服务器选项：

#### 1. 配置文件修改

**node/rpc/etc/node.yaml**:
```yaml
# gRPC服务器连接管理配置 - 防止30分钟连接重置
ServerOptions:
  MaxConnectionAge: 0        # 禁用连接年龄限制，0表示无限制
  MaxConnectionIdle: 0       # 禁用空闲连接超时，0表示无限制
  MaxConnectionAgeGrace: 0   # 禁用连接年龄宽限期
  KeepaliveTime: 30s         # 服务器端keepalive间隔
  KeepaliveTimeout: 5s       # keepalive超时时间
  KeepaliveMinTime: 10s      # 客户端keepalive最小间隔
  KeepalivePermitWithoutStream: true  # 允许无流时keepalive
```

#### 2. 配置结构定义

**node/rpc/internal/config/config.go**:
```go
type ServerOptions struct {
    MaxConnectionAge              time.Duration `json:",optional,default=0"`
    MaxConnectionIdle             time.Duration `json:",optional,default=0"`
    MaxConnectionAgeGrace         time.Duration `json:",optional,default=0"`
    KeepaliveTime                 time.Duration `json:",optional,default=30s"`
    KeepaliveTimeout              time.Duration `json:",optional,default=5s"`
    KeepaliveMinTime              time.Duration `json:",optional,default=10s"`
    KeepalivePermitWithoutStream  bool          `json:",optional,default=true"`
}

type Config struct {
    zrpc.RpcServerConf
    // ... 其他字段
    ServerOptions ServerOptions `json:",optional"`
}
```

#### 3. 服务器选项应用

**node/rpc/server/server.go**:
```go
rs := zrpc.MustNewServer(cc.RpcServerConf, func(grpcServer *grpc.Server) {
    // 注册服务
})

// 添加gRPC服务器选项以防止30分钟连接重置
serverOpts := buildGRPCServerOptions(cc.ServerOptions)
rs.AddOptions(serverOpts...)
```

### 方案2：客户端预防性刷新（辅助解决方案）

**router/api/resolver/resolver.go**:
```go
const (
    defaultTickerDuration = time.Minute * 25 // 25分钟预防性刷新
)
```

## 技术细节

### gRPC连接管理参数说明

1. **MaxConnectionAge**: 连接最大存活时间
   - 默认值通常为30分钟
   - 设置为0或很大的值可以禁用

2. **MaxConnectionIdle**: 连接最大空闲时间
   - 控制空闲连接的生命周期
   - 设置为0可以禁用空闲超时

3. **MaxConnectionAgeGrace**: 连接年龄宽限期
   - 连接关闭前的宽限时间
   - 允许正在进行的请求完成

4. **Keepalive参数**: 连接健康检查
   - Time: keepalive ping间隔
   - Timeout: keepalive响应超时
   - MinTime: 客户端最小keepalive间隔

### 解决方案的工作原理

1. **禁用连接年龄限制**: 通过设置MaxConnectionAge为极大值
2. **保持连接活跃**: 通过keepalive机制
3. **预防性刷新**: 客户端在25分钟时主动刷新
4. **智能重连**: 增强的连接状态管理

## 部署步骤

### 1. 部署Node服务

```bash
# 部署node服务（包含服务器端修复）
kubectl rollout restart deployment api-proxy-node
kubectl rollout status deployment api-proxy-node
```

### 2. 部署Router服务

```bash
# 部署router服务（包含客户端预防性刷新）
kubectl rollout restart deployment api-proxy-router
kubectl rollout status deployment api-proxy-router
```

### 3. 监控验证

```bash
# 监控连接状态
kubectl logs -f -l app=api-proxy-router | grep 'ready sub conns'

# 检查25分钟刷新活动
kubectl logs -f -l app=api-proxy-router | grep 'matching nodes'

# 监控35分钟以上确认无30分钟重置
watch 'date; kubectl logs -l app=api-proxy-router --tail=5 | grep "ready sub conns"'
```

## 验证标准

### 成功指标
- ✅ 35分钟内无`ready sub conns: [0, null]`
- ✅ 连接数保持稳定
- ✅ 可见25分钟resolver刷新活动
- ✅ 打破30分钟重置模式

### 失败指标
- ❌ 继续出现30分钟连接重置
- ❌ 新的连接不稳定问题
- ❌ 服务启动失败

## 回滚方案

如果出现问题，可以快速回滚：

```bash
# 回滚配置文件
git checkout HEAD~1 -- node/rpc/etc/node.yaml
git checkout HEAD~1 -- node/rpc/internal/config/config.go
git checkout HEAD~1 -- node/rpc/server/server.go
git checkout HEAD~1 -- router/api/resolver/resolver.go

# 重新部署
kubectl rollout restart deployment api-proxy-node
kubectl rollout restart deployment api-proxy-router
```

## 预期效果

1. **立即效果**: 部署后35分钟内应该看不到30分钟连接重置
2. **长期效果**: 连接保持稳定，只在真正的网络问题或服务重启时断开
3. **性能影响**: 最小化，主要是减少了不必要的连接重建开销

## 监控建议

建议设置以下监控告警：
1. 连接数为0的事件告警
2. 连接状态异常变化告警
3. resolver刷新失败告警
4. gRPC连接错误率告警

这个解决方案从根本上解决了30分钟连接重置问题，同时保持了系统的稳定性和性能。
