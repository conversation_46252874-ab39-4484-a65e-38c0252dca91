# 30分钟连接问题修复部署指南

## 修复概述

我们实现了双重解决方案来彻底解决30分钟连接重置问题：

### 方案1：服务器端根本修复（主要）
- 通过go-zero的`AddOptions`方法配置gRPC服务器选项
- 禁用`MaxConnectionAge`限制，防止30分钟强制连接关闭
- 配置合适的keepalive参数保持连接健康

### 方案2：客户端预防性刷新（辅助）
- 将resolver刷新间隔从30分钟改为25分钟
- 在服务器可能关闭连接之前主动刷新
- 增强连接状态监控和保护机制

## 部署步骤

### 第一步：部署Node服务（服务器端修复）

```bash
# 1. 验证配置
echo "验证Node服务配置..."
grep -A 8 "ServerOptions:" node/rpc/etc/node.yaml

# 2. 构建测试
echo "构建测试..."
cd node/rpc && go build -o /tmp/test-node ./node.go && rm -f /tmp/test-node
echo "✅ 构建成功"

# 3. 部署
echo "部署Node服务..."
kubectl rollout restart deployment api-proxy-node

# 4. 等待部署完成
kubectl rollout status deployment api-proxy-node --timeout=300s

# 5. 验证服务状态
kubectl get pods -l app=api-proxy-node
kubectl logs -l app=api-proxy-node --tail=10
```

### 第二步：部署Router服务（客户端预防性刷新）

```bash
# 1. 验证配置
echo "验证Router服务配置..."
grep "time.Minute \* 25" router/api/resolver/resolver.go

# 2. 构建测试
echo "构建测试..."
cd router/api && go build -o /tmp/test-router . && rm -f /tmp/test-router
echo "✅ 构建成功"

# 3. 部署
echo "部署Router服务..."
kubectl rollout restart deployment api-proxy-router

# 4. 等待部署完成
kubectl rollout status deployment api-proxy-router --timeout=300s

# 5. 验证服务状态
kubectl get pods -l app=api-proxy-router
kubectl logs -l app=api-proxy-router --tail=10
```

### 第三步：监控验证

```bash
# 1. 开始监控连接状态
echo "开始监控连接状态..."
kubectl logs -f -l app=api-proxy-router | grep 'ready sub conns' &
MONITOR_PID=$!

# 2. 记录开始时间
START_TIME=$(date)
echo "监控开始时间: $START_TIME"

# 3. 等待35分钟进行验证
echo "等待35分钟验证修复效果..."
echo "在另一个终端运行以下命令进行实时监控："
echo "watch 'date; kubectl logs -l app=api-proxy-router --tail=5 | grep \"ready sub conns\"'"

# 4. 检查25分钟刷新活动
echo "监控25分钟resolver刷新活动:"
kubectl logs -f -l app=api-proxy-router | grep 'matching nodes' &
RESOLVER_PID=$!

echo "监控进程已启动，PID: $MONITOR_PID, $RESOLVER_PID"
echo "使用 kill $MONITOR_PID $RESOLVER_PID 停止监控"
```

## 验证标准

### 成功指标（35分钟内观察）

✅ **主要成功指标**：
- 无`ready sub conns: [0, null]`日志出现
- 连接数保持稳定（通常为8）
- 无30分钟周期性重置模式

✅ **次要成功指标**：
- 可见25分钟resolver刷新活动
- 连接状态变化日志减少
- 系统整体稳定性提升

### 失败指标

❌ **需要进一步调查**：
- 仍然出现30分钟连接重置
- 新的连接不稳定问题
- 服务启动或运行异常

## 监控命令

### 实时监控
```bash
# 连接状态监控
kubectl logs -f -l app=api-proxy-router | grep 'ready sub conns'

# 时间戳监控
kubectl logs -f -l app=api-proxy-router | grep 'ready sub conns' | while read line; do echo "$(date): $line"; done

# 25分钟刷新监控
kubectl logs -f -l app=api-proxy-router | grep 'matching nodes'
```

### 定期检查
```bash
# 每5分钟检查一次
watch -n 300 'date; kubectl logs -l app=api-proxy-router --tail=10 | grep "ready sub conns" | tail -3'

# 连接数统计
kubectl logs -l app=api-proxy-router --since=1h | grep 'ready sub conns' | tail -20
```

## 故障排除

### 如果Node服务启动失败
```bash
# 检查配置语法
kubectl logs -l app=api-proxy-node | grep -i error

# 检查配置加载
kubectl describe pods -l app=api-proxy-node

# 回滚Node服务
git checkout HEAD~1 -- node/rpc/etc/node.yaml node/rpc/internal/config/config.go node/rpc/server/server.go
kubectl rollout restart deployment api-proxy-node
```

### 如果Router服务启动失败
```bash
# 检查日志
kubectl logs -l app=api-proxy-router | grep -i error

# 回滚Router服务
git checkout HEAD~1 -- router/api/resolver/resolver.go
kubectl rollout restart deployment api-proxy-router
```

### 如果问题仍然存在
```bash
# 检查K8s环境
kubectl get nodes
kubectl get services
kubectl describe service api-proxy-node

# 检查网络策略
kubectl get networkpolicies
kubectl get ingress

# 检查service mesh
kubectl get pods -n istio-system 2>/dev/null || echo "No Istio found"
```

## 回滚计划

### 完全回滚
```bash
# 1. 回滚所有代码更改
git checkout HEAD~1 -- node/rpc/etc/node.yaml
git checkout HEAD~1 -- node/rpc/internal/config/config.go  
git checkout HEAD~1 -- node/rpc/server/server.go
git checkout HEAD~1 -- router/api/resolver/resolver.go

# 2. 重新部署
kubectl rollout restart deployment api-proxy-node
kubectl rollout restart deployment api-proxy-router

# 3. 验证回滚
kubectl rollout status deployment api-proxy-node
kubectl rollout status deployment api-proxy-router
```

## 预期结果

### 立即效果（部署后）
- 服务正常启动和运行
- 连接建立正常
- 无明显性能影响

### 短期效果（35分钟内）
- 无30分钟连接重置
- 连接数保持稳定
- 可见25分钟刷新活动

### 长期效果（持续运行）
- 连接稳定性显著提升
- 减少不必要的连接重建
- 系统整体可靠性提高

## 成功部署确认

当您看到以下情况时，可以确认修复成功：

1. ✅ 35分钟内无连接重置
2. ✅ 服务运行稳定
3. ✅ 25分钟刷新正常工作
4. ✅ 打破了30分钟重置模式

此时可以认为30分钟连接问题已经得到根本解决。
