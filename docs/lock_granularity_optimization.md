# 锁粒度优化：从全局锁到细粒度锁

## 问题背景

在健康检查器的实现中，我们遇到了一个经典的并发设计问题：如何正确地保护共享数据的并发访问。

## 原始设计的问题

### ❌ 使用全局锁保护所有状态

```go
type HealthChecker struct {
    mutex       sync.RWMutex  // 全局锁
    connections map[string]*healthConnection
}

type healthConnection struct {
    isHealthy bool  // 被全局锁保护
    lastCheck time.Time
    lastError error
}

func (hc *HealthChecker) checkHealth(healthConn *healthConnection) {
    // ...
    hc.mutex.Lock()  // 使用全局锁
    healthConn.isHealthy = true
    hc.mutex.Unlock()
}
```

### 🚫 问题分析

1. **锁粒度过大**：
   - 所有健康检查操作都竞争同一个锁
   - 即使检查不同的连接，也会相互阻塞

2. **性能瓶颈**：
   - 8个node连接 = 8个健康检查goroutine
   - 所有goroutine竞争同一个锁
   - 串行化了本应并行的操作

3. **逻辑混乱**：
   - 用管理连接映射的锁来保护单个连接的状态
   - 违反了"锁应该保护相关数据"的原则

## 优化后的设计

### ✅ 细粒度锁设计

```go
type HealthChecker struct {
    mutex       sync.RWMutex  // 只保护connections映射
    connections map[string]*healthConnection
}

type healthConnection struct {
    mu        sync.RWMutex  // 保护自己的状态
    isHealthy bool
    lastCheck time.Time
    lastError error
}
```

### 🎯 锁的职责分离

1. **全局锁 (`hc.mutex`)**：
   - 只保护`connections`映射的增删操作
   - 保护对映射的遍历操作

2. **连接锁 (`healthConn.mu`)**：
   - 保护单个连接的健康状态
   - 保护`isHealthy`、`lastCheck`、`lastError`字段

### 📊 性能对比

#### 原始设计（全局锁）
```
健康检查1: 等待全局锁 -> 检查 -> 释放锁
健康检查2: 等待全局锁 -> 检查 -> 释放锁  ← 被阻塞
健康检查3: 等待全局锁 -> 检查 -> 释放锁  ← 被阻塞
...
```

#### 优化设计（细粒度锁）
```
健康检查1: 获取连接1锁 -> 检查 -> 释放锁
健康检查2: 获取连接2锁 -> 检查 -> 释放锁  ← 并行执行
健康检查3: 获取连接3锁 -> 检查 -> 释放锁  ← 并行执行
...
```

## 实现细节

### 1. 读取健康状态

```go
func (hc *HealthChecker) IsHealthy(address string) bool {
    // 步骤1：获取连接引用（短暂持有全局锁）
    hc.mutex.RLock()
    healthConn, exists := hc.connections[address]
    hc.mutex.RUnlock()

    if !exists {
        return false
    }

    // 步骤2：读取连接状态（使用连接自己的锁）
    healthConn.mu.RLock()
    defer healthConn.mu.RUnlock()
    return healthConn.isHealthy
}
```

### 2. 更新健康状态

```go
func (hc *HealthChecker) checkHealth(healthConn *healthConnection) {
    // 执行健康检查...
    
    // 只锁定当前连接，不影响其他连接的检查
    healthConn.mu.Lock()
    defer healthConn.mu.Unlock()
    
    healthConn.isHealthy = isHealthy
    healthConn.lastCheck = time.Now()
}
```

### 3. 批量操作优化

```go
func (hc *HealthChecker) GetHealthStatus() map[string]bool {
    // 步骤1：快速获取所有连接引用
    hc.mutex.RLock()
    connections := make([]*healthConnection, 0, len(hc.connections))
    addresses := make([]string, 0, len(hc.connections))
    for address, healthConn := range hc.connections {
        connections = append(connections, healthConn)
        addresses = append(addresses, address)
    }
    hc.mutex.RUnlock()

    // 步骤2：并行读取各连接状态
    status := make(map[string]bool)
    for i, healthConn := range connections {
        healthConn.mu.RLock()
        status[addresses[i]] = healthConn.isHealthy
        healthConn.mu.RUnlock()
    }
    return status
}
```

## 设计原则

### 1. 锁的单一职责原则
- 每个锁只保护相关的数据
- 避免用一个锁保护不相关的数据

### 2. 最小锁粒度原则
- 使用尽可能小的锁粒度
- 减少锁的竞争和持有时间

### 3. 锁层次化原则
```
Level 1: hc.mutex (全局，保护映射)
Level 2: healthConn.mu (局部，保护连接状态)
```

### 4. 避免嵌套锁
- 先释放高层锁，再获取低层锁
- 避免死锁风险

## 性能收益

1. **并发性提升**：
   - 8个连接可以并行进行健康检查
   - 读取操作也可以并行进行

2. **锁竞争减少**：
   - 全局锁只在映射操作时使用
   - 连接锁只在状态更新时使用

3. **响应时间改善**：
   - `IsHealthy`调用不会被其他连接的检查阻塞
   - 系统整体响应更快

## 总结

通过将全局锁拆分为细粒度锁，我们实现了：

1. **更好的并发性**：多个健康检查可以并行执行
2. **更清晰的设计**：每个锁的职责明确
3. **更好的性能**：减少了锁竞争和等待时间
4. **更好的可维护性**：代码逻辑更清晰

这是一个经典的并发优化案例，展示了如何通过合理的锁设计来提升系统性能。
