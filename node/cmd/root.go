package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

const (
	rootCmdUse   = common.NodeServiceName
	rootCmdShort = "The node service is one part of the api-proxy."
	rootCmdLong  = `The node service is one part of the api-proxy.
It is primarily responsible for managing clients and forwarding requests to the business-side servers.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
