package metrics

import (
	commonmetrics "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/metrics"
)

var BusinessMetricHandler commonmetrics.MetricGaugeHandler

func init() {
	gaugeVecOpts := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts.Name = metrics.ConstMetricsBusinessStatus + string(commonmetrics.ConstSystemMetricUnitTypeInfo)
	gaugeVecOpts.Help = "upload the business metrics."
	gaugeVecOpts.Labels = []string{
		"product_name",
		"request_protocol", "request_host", "request_path",
		"grpc_response_status", "http_response_status", "business_response_status",
	}
	BusinessMetricHandler = commonmetrics.NewMetricGauge(&gaugeVecOpts)
}
