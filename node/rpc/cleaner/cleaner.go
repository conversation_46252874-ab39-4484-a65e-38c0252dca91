package cleaner

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	goredis "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	zeroredis "github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

const (
	lockKeyOfCleanUpInvalidClients   = "lock:cleaner:cleanUpInvalidClients"
	lockKeyOfCleanUpInvalidUsers     = "lock:cleaner:cleanUpInvalidUsers"
	lockKeyOfCleanUpUnmanagedClients = "lock:cleaner:cleanUpUnmanagedClients"

	timeoutOfCleanUpInvalidClients    = time.Minute
	timeoutOfCleanUpInvalidUsers      = time.Minute
	timeoutOfCleanUpUnmanagedClients  = time.Minute
	intervalOfCleanUpUnmanagedClients = 30 * time.Minute

	countOfScan       = 100
	capacityOfClients = 1024
	capacityOfUsers   = 1024
)

type (
	Config struct {
		InvalidClients       string        // 清理无效客户端的`Cron`表达式
		InvalidUsers         string        // 清理无效用户的`Cron`表达式
		UnmanagedClients     string        // 清理未被管理的客户端的`Cron`表达式
		MaxRetentionDuration time.Duration `json:",default=30m"` // 未被管理的客户端最大保留时长
	}
	Cleaner struct {
		logx.Logger
		ctx       context.Context
		closeOnce sync.Once

		r         *zeroredis.Redis
		rdb       goredis.UniversalClient
		scheduler *cronscheduler.Scheduler

		c   Config
		nid string
	}
)

func InitCleaner(c Config, rc zeroredis.RedisConf) error {
	_, err := newCleaner(c, rc)
	return err
}

func newCleaner(c Config, rc zeroredis.RedisConf) (*Cleaner, error) {
	ctx := context.Background()
	id := common.ID()

	r, err := zeroredis.NewRedis(rc, zeroredis.WithDB(rc.DB))
	if err != nil {
		return nil, err
	}

	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	cleaner := &Cleaner{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, id)),
		ctx:    ctx,

		r:         r,
		rdb:       qetredis.NewClient(rc),
		scheduler: scheduler,

		c:   c,
		nid: id,
	}
	cleaner.register()
	proc.AddShutdownListener(cleaner.Close)

	return cleaner, nil
}

func (c *Cleaner) Close() {
	c.closeOnce.Do(
		func() {
			c.Infof("closing the cleaner")

			if c.rdb != nil {
				_ = c.rdb.Close()
			}
			if c.scheduler != nil {
				c.scheduler.Stop()
			}
		},
	)
}

func (c *Cleaner) register() {
	var err error
	if c.c.InvalidClients != "" {
		if _, e := c.scheduler.Cron(c.c.InvalidClients).Do(c.cleanUpInvalidClients); e != nil {
			err = errors.Join(err, e)
		}
	}
	if c.c.InvalidUsers != "" {
		if _, e := c.scheduler.Cron(c.c.InvalidUsers).Do(c.cleanUpInvalidUsers); e != nil {
			err = errors.Join(err, e)
		}
	}
	if c.c.UnmanagedClients != "" {
		if c.c.MaxRetentionDuration == 0 {
			c.c.MaxRetentionDuration = intervalOfCleanUpUnmanagedClients
		}
		if _, e := c.scheduler.Cron(c.c.UnmanagedClients).Do(c.cleanUpUnmanagedClients); e != nil {
			err = errors.Join(err, e)
		}
	}

	if err != nil {
		c.Errorf("failed to register schedule tasks, error: %+v", err)
	} else {
		c.scheduler.Start()
	}
}

func (c *Cleaner) cleanUpInvalidClients() {
	key := lockKeyOfCleanUpInvalidClients
	fn := func() error {
		ctx, cancel := context.WithTimeout(c.ctx, timeoutOfCleanUpInvalidClients)
		defer cancel()

		var (
			clientsInfoKey = common.ConstClientsInfoKey

			nodes   = make(map[string]bool, 16)
			clients = make(map[string][]string, 16)

			mutex sync.Mutex
			err   error
		)

		_ = mr.MapReduceVoid[*commonpb.ClientInfo, *commonpb.ClientInfo](
			func(source chan<- *commonpb.ClientInfo) {
				var (
					keys   []string
					cursor uint64
				)

				for {
					keys, cursor, err = c.rdb.HScan(ctx, clientsInfoKey, cursor, "", countOfScan).Result()
					if err != nil {
						err = fmt.Errorf(
							"failed to execute `hscan` command, key: %s, cursor: %d, error: %+v",
							clientsInfoKey, cursor, err,
						)
						break
					}

					var field, value string
					for i := 0; i < len(keys); i += 2 {
						field = keys[i]
						value = keys[i+1]

						info := &commonpb.ClientInfo{}
						if e := protobuf.UnmarshalJSONFromString(value, info); e != nil {
							c.Errorf(
								"failed to unmarshal the value to %T, key: %s, field: %s, value: %s, error: %+v",
								info, clientsInfoKey, field, value, e,
							)
							continue
						}

						nid := info.GetNid()
						if len(nid) == 0 {
							c.Warnf("the `nid` is empty, key: %s, field: %s, value: %s", clientsInfoKey, field, value)
							continue
						}

						source <- info
					}

					if cursor == 0 {
						break
					}
				}
			},
			func(item *commonpb.ClientInfo, writer mr.Writer[*commonpb.ClientInfo], cancel func(error)) {
				if item == nil {
					return
				}

				mutex.Lock()
				defer mutex.Unlock()

				nid := item.GetNid()
				enabled, ok := nodes[nid]
				if !ok {
					nodeInfoKey := common.NodeInfoKey(nid)
					exists, e := c.rdb.Exists(ctx, nodeInfoKey).Result()
					if e != nil && !errors.Is(e, zeroredis.Nil) {
						c.Errorf("failed to execute `exists` command, key: %s, error: %+v", nodeInfoKey, e)
						return
					}

					enabled = exists == 1
					nodes[nid] = enabled
				}
				if enabled {
					return
				}

				writer.Write(item)
			},
			func(pipe <-chan *commonpb.ClientInfo, cancel func(error)) {
				for item := range pipe {
					if item == nil {
						continue
					}

					nid := item.GetNid()
					cid := item.GetCid()
					if _, ok := clients[nid]; !ok {
						clients[nid] = make([]string, 0, capacityOfClients)
					}
					clients[nid] = append(clients[nid], cid)
				}

				for nodeID, clientIDs := range clients {
					number, err := c.rdb.HDel(ctx, clientsInfoKey, clientIDs...).Result()
					if err != nil && !errors.Is(err, zeroredis.Nil) {
						c.Errorf(
							"failed to execute `hdel` command, key: %s, fields: %s, error: %+v",
							clientsInfoKey, jsonx.MarshalIgnoreError(clientIDs), err,
						)
					} else {
						c.Infof(
							"clean up invalid clients, node: %s, key: %s, number: %d", nodeID, clientsInfoKey, number,
						)
					}

					nodeClientsKey := common.NodeWithClientsKey(nodeID)
					number, err = c.rdb.HDel(ctx, nodeClientsKey, clientIDs...).Result()
					if err != nil && !errors.Is(err, zeroredis.Nil) {
						c.Errorf(
							"failed to execute `hdel` command, key: %s, fields: %s, error: %+v",
							nodeClientsKey, jsonx.MarshalIgnoreError(clientIDs), err,
						)
					} else {
						c.Infof(
							"clean up invalid clients, node: %s, key: %s, number: %d", nodeID, nodeClientsKey, number,
						)
					}
				}
			},
			mr.WithContext(ctx),
		)
		return err
	}
	err := caller.LockWithOptionDo(c.r, key, fn, redislock.WithExpire(timeoutOfCleanUpInvalidClients))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			c.Errorf("failed to clean up invalid clients, key: %s, error: %+v", key, err)
			return
		}

		c.Infof("another service is cleaning invalid clients, key: %s", key)
	} else {
		c.Infof("finish to clean up invalid clients, key: %s", key)
	}
}

func (c *Cleaner) cleanUpInvalidUsers() {
	key := lockKeyOfCleanUpInvalidUsers
	fn := func() error {
		ctx, cancel := context.WithTimeout(c.ctx, timeoutOfCleanUpInvalidUsers)
		defer cancel()

		var (
			usersInfoKey = common.ConstUsersInfoKey

			nodes = make(map[string]bool, 16)
			users = make(map[string][]string, 16)

			mutex sync.Mutex
			err   error
		)

		_ = mr.MapReduceVoid[*commonpb.UserInfo, *commonpb.UserInfo](
			func(source chan<- *commonpb.UserInfo) {
				var (
					keys   []string
					cursor uint64
				)

				for {
					keys, cursor, err = c.rdb.HScan(ctx, usersInfoKey, cursor, "", countOfScan).Result()
					if err != nil {
						err = fmt.Errorf(
							"failed to execute `hscan` command, key: %s, cursor: %d, error: %+v",
							usersInfoKey, cursor, err,
						)
						break
					}

					var field, value string
					for i := 0; i < len(keys); i += 2 {
						field = keys[i]
						value = keys[i+1]

						info := &commonpb.UserInfo{}
						if e := protobuf.UnmarshalJSONFromString(value, info); e != nil {
							c.Errorf(
								"failed to unmarshal the value to %T, key: %s, field: %s, value: %s, error: %+v",
								info, usersInfoKey, field, value, e,
							)
							continue
						}

						nid := info.GetNid()
						if len(nid) == 0 {
							c.Warnf("the `nid` is empty, key: %s, field: %s, value: %s", usersInfoKey, field, value)
							continue
						}

						source <- info
					}

					if cursor == 0 {
						break
					}
				}
			},
			func(item *commonpb.UserInfo, writer mr.Writer[*commonpb.UserInfo], cancel func(error)) {
				if item == nil {
					return
				}

				mutex.Lock()
				defer mutex.Unlock()

				nid := item.GetNid()
				enabled, ok := nodes[nid]
				if !ok {
					nodeInfoKey := common.NodeInfoKey(nid)
					exists, e := c.rdb.Exists(ctx, nodeInfoKey).Result()
					if e != nil && !errors.Is(e, zeroredis.Nil) {
						c.Errorf("failed to execute `exists` command, key: %s, error: %+v", nodeInfoKey, e)
						return
					}

					enabled = exists == 1
					nodes[nid] = enabled
				}
				if enabled {
					return
				}

				writer.Write(item)
			},
			func(pipe <-chan *commonpb.UserInfo, cancel func(error)) {
				for item := range pipe {
					if item == nil {
						continue
					}

					nid := item.GetNid()
					uid := item.GetUid()
					if _, ok := users[uid]; !ok {
						users[uid] = make([]string, 0, capacityOfUsers)
					}
					users[nid] = append(users[nid], uid)
				}

				for nodeID, userIDs := range users {
					number, e := c.rdb.HDel(ctx, usersInfoKey, userIDs...).Result()
					if e != nil && !errors.Is(e, zeroredis.Nil) {
						c.Errorf(
							"failed to execute `hdel` command, key: %s, fields: %s, error: %+v",
							usersInfoKey, jsonx.MarshalIgnoreError(userIDs), e,
						)
					} else {
						c.Infof(
							"clean up invalid users, node: %s, key: %s, number: %d", nodeID, usersInfoKey, number,
						)
					}

					nodeUsersKey := common.NodeWithUsersKey(nodeID)
					number, e = c.rdb.HDel(ctx, nodeUsersKey, userIDs...).Result()
					if e != nil && !errors.Is(e, zeroredis.Nil) {
						c.Errorf(
							"failed to execute `hdel` command, key: %s, fields: %s, error: %+v",
							nodeUsersKey, jsonx.MarshalIgnoreError(userIDs), e,
						)
					} else {
						c.Infof(
							"clean up invalid users, node: %s, key: %s, number: %d", nodeID, nodeUsersKey, number,
						)
					}
				}
			},
			mr.WithContext(ctx),
		)

		return nil
	}
	err := caller.LockWithOptionDo(c.r, key, fn, redislock.WithExpire(timeoutOfCleanUpInvalidUsers))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			c.Errorf("failed to clean up invalid users, key: %s, error: %+v", key, err)
			return
		}

		c.Infof("another service is cleaning invalid users, key: %s", key)
	} else {
		c.Infof("finish to clean up invalid users, key: %s", key)
	}
}

func (c *Cleaner) cleanUpUnmanagedClients() {
	key := lockKeyOfCleanUpUnmanagedClients
	fn := func() error {
		ctx, cancel := context.WithTimeout(c.ctx, timeoutOfCleanUpUnmanagedClients)
		defer cancel()

		commands, err := c.rdb.Pipelined(
			ctx, func(pipe goredis.Pipeliner) error {
				var (
					clientsInfoKey = common.ConstUnmanagedClientsInfoKey

					keys   []string
					cursor uint64
					err    error
				)

				for {
					keys, cursor, err = c.rdb.HScan(ctx, clientsInfoKey, cursor, "", countOfScan).Result()
					if err != nil {
						return fmt.Errorf(
							"failed to execute `hscan` command, key: %s, cursor: %d, error: %+v",
							clientsInfoKey, cursor, err,
						)
					}

					var field, value string
					for i := 0; i < len(keys); i += 2 {
						field = keys[i]
						value = keys[i+1]

						info := &commonpb.ClientInfo{}
						if e := protobuf.UnmarshalJSONFromString(value, info); e != nil {
							c.Errorf(
								"failed to unmarshal the value to %T, key: %s, field: %s, value: %s, error: %+v",
								info, clientsInfoKey, field, value, e,
							)
							continue
						}

						lastRequestedAt := info.GetLastRequestedAt().AsTime()
						if time.Since(lastRequestedAt) > c.c.MaxRetentionDuration {
							c.Infof(
								"preparing to clean up the unmanaged client, cid: %s, last_requested_at: %s, max_retention_duration: %s",
								info.GetCid(), lastRequestedAt.Format(time.DateTime), c.c.MaxRetentionDuration,
							)

							pipe.HDel(ctx, clientsInfoKey, field)
						}
					}

					if cursor == 0 {
						break
					}
				}

				return nil
			},
		)
		if err != nil {
			return fmt.Errorf("failed to execute the pipeline, commands: %d, error: %+v", len(commands), err)
		}

		c.Infof("finish to execute the pipeline, commands: %d", len(commands))
		return nil
	}
	err := caller.LockWithOptionDo(c.r, key, fn, redislock.WithExpire(timeoutOfCleanUpUnmanagedClients))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			c.Errorf("failed to clean up unmanaged clients, key: %s, error: %+v", key, err)
			return
		}

		c.Infof("another service is cleaning unmanaged clients, key: %s", key)
	} else {
		c.Infof("finish to clean up unmanaged clients, key: %s", key)
	}
}
