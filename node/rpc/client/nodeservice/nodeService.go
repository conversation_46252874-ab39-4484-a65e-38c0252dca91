// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: node.proto

package nodeservice

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type (
	ApiCallReq                      = pb.ApiCallReq
	ApiCallResp                     = pb.ApiCallResp
	ApiCallResp_CallResp            = pb.ApiCallResp_CallResp
	CreateClientReq                 = pb.CreateClientReq
	CreateClientResp                = pb.CreateClientResp
	DeleteClientReq                 = pb.DeleteClientReq
	DeleteClientResp                = pb.DeleteClientResp
	JsonToProtoReq                  = pb.JsonToProtoReq
	JsonToProtoResp                 = pb.JsonToProtoResp
	ProtoToJsonReq                  = pb.ProtoToJsonReq
	ProtoToJsonResp                 = pb.ProtoToJsonResp
	SearchClient                    = pb.SearchClient
	UpdateProtoReq                  = pb.UpdateProtoReq
	UpdateProtoReq_DependenceConfig = pb.UpdateProtoReq_DependenceConfig
	UpdateProtoReq_GitConfig        = pb.UpdateProtoReq_GitConfig
	UpdateProtoReq_ProjectConfig    = pb.UpdateProtoReq_ProjectConfig
	UpdateProtoResp                 = pb.UpdateProtoResp
	UpdateProtoResp_Commit          = pb.UpdateProtoResp_Commit
	UpdateProtoResp_ProjectInfo     = pb.UpdateProtoResp_ProjectInfo
	ViewClientReq                   = pb.ViewClientReq
	ViewClientResp                  = pb.ViewClientResp

	NodeService interface {
		// CreateClient 创建客户端
		CreateClient(ctx context.Context, in *CreateClientReq, opts ...grpc.CallOption) (*CreateClientResp, error)
		// DeleteClient 删除客户端
		DeleteClient(ctx context.Context, in *DeleteClientReq, opts ...grpc.CallOption) (*DeleteClientResp, error)
		// ViewClient 查看客户端
		ViewClient(ctx context.Context, in *ViewClientReq, opts ...grpc.CallOption) (*ViewClientResp, error)
		// ApiCall 接口调用
		ApiCall(ctx context.Context, in *ApiCallReq, opts ...grpc.CallOption) (*ApiCallResp, error)
		// UpdateProto 更新`proto`项目
		UpdateProto(ctx context.Context, in *UpdateProtoReq, opts ...grpc.CallOption) (*UpdateProtoResp, error)
		// JsonToProto `json`数据转换成`proto`字符串
		JsonToProto(ctx context.Context, in *JsonToProtoReq, opts ...grpc.CallOption) (*JsonToProtoResp, error)
		// ProtoToJson `proto`字符串转换成`json`数据
		ProtoToJson(ctx context.Context, in *ProtoToJsonReq, opts ...grpc.CallOption) (*ProtoToJsonResp, error)
	}

	defaultNodeService struct {
		cli zrpc.Client
	}
)

func NewNodeService(cli zrpc.Client) NodeService {
	return &defaultNodeService{
		cli: cli,
	}
}

// CreateClient 创建客户端
func (m *defaultNodeService) CreateClient(ctx context.Context, in *CreateClientReq, opts ...grpc.CallOption) (*CreateClientResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.CreateClient(ctx, in, opts...)
}

// DeleteClient 删除客户端
func (m *defaultNodeService) DeleteClient(ctx context.Context, in *DeleteClientReq, opts ...grpc.CallOption) (*DeleteClientResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.DeleteClient(ctx, in, opts...)
}

// ViewClient 查看客户端
func (m *defaultNodeService) ViewClient(ctx context.Context, in *ViewClientReq, opts ...grpc.CallOption) (*ViewClientResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.ViewClient(ctx, in, opts...)
}

// ApiCall 接口调用
func (m *defaultNodeService) ApiCall(ctx context.Context, in *ApiCallReq, opts ...grpc.CallOption) (*ApiCallResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.ApiCall(ctx, in, opts...)
}

// UpdateProto 更新`proto`项目
func (m *defaultNodeService) UpdateProto(ctx context.Context, in *UpdateProtoReq, opts ...grpc.CallOption) (*UpdateProtoResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.UpdateProto(ctx, in, opts...)
}

// JsonToProto `json`数据转换成`proto`字符串
func (m *defaultNodeService) JsonToProto(ctx context.Context, in *JsonToProtoReq, opts ...grpc.CallOption) (*JsonToProtoResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.JsonToProto(ctx, in, opts...)
}

// ProtoToJson `proto`字符串转换成`json`数据
func (m *defaultNodeService) ProtoToJson(ctx context.Context, in *ProtoToJsonReq, opts ...grpc.CallOption) (*ProtoToJsonResp, error) {
	client := pb.NewNodeServiceClient(m.cli.Conn())
	return client.ProtoToJson(ctx, in, opts...)
}
