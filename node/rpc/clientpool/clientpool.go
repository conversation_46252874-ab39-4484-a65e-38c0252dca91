package clientpool

import (
	"context"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/syncx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/registry"
)

const (
	initPoolSize    = 1024
	maxPoolSize     = 2 * initPoolSize
	minIdleDuration = 10 * time.Minute

	intervalOfKeepalive            = 2 * time.Second
	intervalOfEvictInvalidClients  = 30 * time.Second
	intervalOfEvictExceededClients = 45 * time.Second
	intervalOfFixUpValidClients    = time.Minute
)

var (
	ErrClientPoolNotInit = errors.New("the client pool is not initialized")
	ErrClientNotFound    = errors.New("specific client not found")

	clientPool *ClientPool
)

type (
	Config struct {
		Capacity               uint64        `json:",default=1024" zh:"池子容量"`
		MaxConsecutiveFailures uint32        `json:",default=3" zh:"最大连续失败次数"`
		MaxIdleDuration        time.Duration `json:",default=1h" zh:"最大空闲时长"`
	}
	ClientPool struct {
		logx.Logger

		ctx          context.Context
		singleFlight syncx.SingleFlight
		mutex        sync.RWMutex
		once         sync.Once
		quit         chan lang.PlaceholderType

		reg       *registry.Registry
		pool      map[string]clienttypes.IClient
		relations map[string]string

		c   Config
		nid string
	}

	SearchClient struct {
		CID string `json:"cid,omitempty,optional" zh:"客户端ID"`
	}
)

func InitClientPool(c Config, rc redis.RedisConf) (err error) {
	clientPool, err = NewClientPool(c, rc)
	return err
}

func Get(search *pb.SearchClient) (clienttypes.IClient, error) {
	if clientPool == nil {
		return nil, ErrClientPoolNotInit
	}

	return clientPool.Get(search)
}

func Set(cid string, c clienttypes.IClient) {
	if clientPool != nil {
		clientPool.Set(cid, c)
	}
}

func Del(cid string) (ci *commonpb.ClientInfo) {
	if clientPool == nil {
		return nil
	}

	return clientPool.Del(cid)
}

func All() []*commonpb.ClientInfo {
	if clientPool == nil {
		return nil
	}

	return clientPool.All()
}

func Close() {
	if clientPool == nil {
		return
	}

	clientPool.close()
}

func NewClientPool(c Config, rc redis.RedisConf) (*ClientPool, error) {
	ctx := context.Background()
	id := common.ID()
	if c.Capacity == 0 {
		c.Capacity = initPoolSize
	} else if c.Capacity > maxPoolSize {
		c.Capacity = maxPoolSize
	}

	cp := &ClientPool{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, id)),

		ctx:          ctx,
		singleFlight: syncx.NewSingleFlight(),
		quit:         make(chan lang.PlaceholderType),

		reg:       registry.NewRegistry(rc),
		pool:      make(map[string]clienttypes.IClient, c.Capacity),
		relations: make(map[string]string, c.Capacity),

		c:   c,
		nid: id,
	}

	cp.asyncHandler()

	proc.AddShutdownListener(cp.close)

	return cp, nil
}

func (cp *ClientPool) close() {
	cp.once.Do(
		func() {
			cp.Info("closing the client pool")

			close(cp.quit)

			cp.mutex.Lock()
			defer cp.mutex.Unlock()

			if err := cp.unregisterAllClients(); err != nil {
				cp.Errorf("failed to unregister all clients, error: %v", err)
			}
			for k, v := range cp.pool {
				_ = v.Close()
				delete(cp.pool, k)
			}
			for k := range cp.relations {
				delete(cp.relations, k)
			}
		},
	)
}

func (cp *ClientPool) asyncHandler() {
	threading.GoSafe(cp.keepalive)
	threading.GoSafe(cp.evictInvalidClients)
	threading.GoSafe(cp.evictExceededClients)
	threading.GoSafe(cp.fixUpValidClients)
}

func (cp *ClientPool) keepalive() {
	ticker := timewheel.NewTicker(intervalOfKeepalive)
	defer ticker.Stop()

	for {
		select {
		case <-cp.quit:
			return
		case <-ticker.C:
			cp.mutex.RLock()
			count := len(cp.pool)
			cp.mutex.RUnlock()

			if count > 0 {
				if err := cp.reg.KeepaliveAllInfoByNodeID(cp.ctx, cp.nid); err != nil {
					cp.Errorf("failed to keepalive all clients, nid: %s, error: %v", cp.nid, err)
				}
			}
		}
	}
}

func (cp *ClientPool) register(info *commonpb.ClientInfo, broadcast, keepalive bool) error {
	operation := registry.Register
	if keepalive {
		operation = registry.Keepalive
	}

	if broadcast {
		return cp.reg.RegisterClientInfoAndBroadcast(cp.ctx, info, operation)
	}

	return cp.reg.RegisterClientInfo(cp.ctx, info, operation)
}

func (cp *ClientPool) unregister(info *commonpb.ClientInfo, broadcast bool) error { //nolint: unparam
	if broadcast {
		return cp.reg.UnregisterClientInfoAndBroadcast(cp.ctx, info)
	}

	return cp.reg.UnregisterClientInfo(cp.ctx, info)
}

func (cp *ClientPool) unregisterAllClients() error {
	return cp.reg.UnregisterAllClientInfo(cp.ctx, cp.nid)
}

func (cp *ClientPool) evictInvalidClients() {
	ticker := timewheel.NewTicker(intervalOfEvictInvalidClients)
	defer ticker.Stop()

	for {
		select {
		case <-cp.quit:
			return
		case <-ticker.C:
			// 避免`map-reduce`的`worker`死锁，因此先读取当前池子的内容
			cp.mutex.RLock()
			cs := make(map[string]clienttypes.IClient, len(cp.pool))
			for _, v := range cp.pool {
				cs[v.Cid()] = v
			}
			cp.mutex.RUnlock()

			if cis, err := cp.reg.GetAllClientInfoByNodeID(cp.ctx, cp.nid); err == nil {
				_ = mr.MapReduceVoid[*commonpb.ClientInfo, any](
					func(source chan<- *commonpb.ClientInfo) {
						for _, ci := range cis {
							if ci == nil {
								continue
							}

							if _, ok := cs[ci.GetCid()]; ok {
								continue
							}

							source <- ci
						}
					},
					func(item *commonpb.ClientInfo, writer mr.Writer[any], cancel func(error)) {
						if item == nil {
							return
						}

						// 由于创建客户端不再广播，所以删除客户端也不需要广播
						if err := cp.unregister(item, false); err != nil {
							cp.Errorf("failed to unregister the client, cid: %s, error: %v", item.GetCid(), err)
						} else {
							cp.Infof("finish to unregister the client, cid: %s", item.GetCid())
						}
					},
					func(pipe <-chan any, cancel func(error)) {
					},
					mr.WithContext(cp.ctx),
				)
			} else {
				cp.Errorf("failed to get all client info by node id, nid: %s, error: %v", cp.nid, err)
			}

			_ = mr.MapReduceVoid[clienttypes.IClient, any](
				func(source chan<- clienttypes.IClient) {
					for _, c := range cs {
						enabled := c.Enabled()
						failures := c.Failures()
						idleDuration := time.Since(c.LastRequestedAt())

						// 客户端已失效 or 客户端连续失败次数大于配置值 or 客户端空闲时间大于配置值，则清理客户端
						if !enabled || // 客户端已失效
							failures > cp.c.MaxConsecutiveFailures || // 客户端连续失败次数大于配置值
							idleDuration > cp.c.MaxIdleDuration { // 客户端空闲时间大于配置值
							cp.Infof(
								"preparing to evict invalid client, cid: %s, enabled: %t, failures: %d, idle duration: %s",
								c.Cid(), enabled, failures, idleDuration.String(),
							)

							source <- c
						}
					}
				},
				func(item clienttypes.IClient, writer mr.Writer[any], cancel func(error)) {
					if item == nil {
						return
					}

					cid := item.Cid()
					create := item.GetCreateInfo()
					ci := item.GetClientInfo()

					if err := item.Close(); err != nil {
						cp.Errorf("failed to close the client, cid: %s, error: %v", cid, err)
					}

					// 由于创建客户端不再广播，所以删除客户端也不需要广播
					if err := cp.unregister(ci, false); err != nil {
						cp.Errorf("failed to unregister the client, cid: %s, error: %v", cid, err)
					}

					cp.mutex.Lock()
					defer cp.mutex.Unlock()

					delete(cp.pool, cid)
					delete(cp.relations, create.Key())

					cp.Infof("finish to evict invalid client, cid: %s", cid)
				},
				func(pipe <-chan any, cancel func(error)) {
				},
				mr.WithContext(cp.ctx),
			)
		}
	}
}

func (cp *ClientPool) evictExceededClients() {
	ticker := timewheel.NewTicker(intervalOfEvictExceededClients)
	defer ticker.Stop()

	for {
		select {
		case <-cp.quit:
			return
		case <-ticker.C:
			// 避免`map-reduce`的`worker`死锁，因此先读取当前池子的内容
			cp.mutex.RLock()
			size := len(cp.pool)
			capacity := int(cp.c.Capacity)
			cp.mutex.RUnlock()

			// 检查是否超出容量
			if size <= capacity {
				cp.Infof(
					"the number of clients is not exceeded, skip to evict exceeded clients, size: %d, max: %d",
					size, capacity,
				)
				continue
			}

			// 计算目标删除数量
			targetEvictCount := size - capacity

			// 使用插入排序直接构建待删除客户端列表（按空闲时间倒序）
			cp.mutex.RLock()
			clientsToEvict := make([]clienttypes.IClient, 0, targetEvictCount)
			eligibleCount := 0 // 统计满足条件的客户端总数

			for _, v := range cp.pool {
				if time.Since(v.LastRequestedAt()) >= minIdleDuration {
					eligibleCount++
					cp.insertClientByIdleTimeDesc(v, &clientsToEvict, targetEvictCount)
				}
			}
			cp.mutex.RUnlock()

			cp.Infof(
				"preparing to evict exceeded clients, target: %d, eligible: %d, actual: %d, size: %d, max: %d",
				targetEvictCount, eligibleCount, len(clientsToEvict), size, capacity,
			)

			// 执行删除操作
			_ = mr.MapReduceVoid[clienttypes.IClient, any](
				func(source chan<- clienttypes.IClient) {
					for _, c := range clientsToEvict {
						idleDuration := time.Since(c.LastRequestedAt())
						cp.Infof(
							"preparing to evict exceeded client, cid: %s, idle duration: %s",
							c.Cid(), idleDuration.String(),
						)
						source <- c
					}
				},
				func(item clienttypes.IClient, writer mr.Writer[any], cancel func(error)) {
					if item == nil {
						return
					}

					cid := item.Cid()
					create := item.GetCreateInfo()
					ci := item.GetClientInfo()

					if err := item.Close(); err != nil {
						cp.Errorf("failed to close the exceeded client, cid: %s, error: %v", cid, err)
					}

					// 由于创建客户端不再广播，所以删除客户端也不需要广播
					if err := cp.unregister(ci, false); err != nil {
						cp.Errorf("failed to unregister the exceeded client, cid: %s, error: %v", cid, err)
					}

					cp.mutex.Lock()
					defer cp.mutex.Unlock()

					delete(cp.pool, cid)
					delete(cp.relations, create.Key())

					cp.Infof("finish to evict exceeded client, cid: %s", cid)
				},
				func(pipe <-chan any, cancel func(error)) {
				},
				mr.WithContext(cp.ctx),
			)
		}
	}
}

// insertClientByIdleTimeDesc 将客户端按空闲时间倒序插入到待删除列表中
// 空闲时间越长的客户端排在前面，只保留前 maxSize 个客户端
func (cp *ClientPool) insertClientByIdleTimeDesc(
	client clienttypes.IClient, clients *[]clienttypes.IClient, maxSize int,
) {
	clientIdleTime := time.Since(client.LastRequestedAt())

	// 如果列表未满，直接插入
	if len(*clients) < maxSize {
		// 找到插入位置
		insertPos := len(*clients)
		for i := 0; i < len(*clients); i++ {
			if time.Since((*clients)[i].LastRequestedAt()) < clientIdleTime {
				insertPos = i
				break
			}
		}

		// 扩展切片并插入
		*clients = append(*clients, nil)
		copy((*clients)[insertPos+1:], (*clients)[insertPos:])
		(*clients)[insertPos] = client
		return
	}

	// 如果列表已满，检查是否需要替换最后一个元素
	lastClient := (*clients)[maxSize-1]
	lastIdleTime := time.Since(lastClient.LastRequestedAt())

	// 如果当前客户端的空闲时间更长，则替换
	if clientIdleTime > lastIdleTime {
		// 找到插入位置
		insertPos := maxSize - 1
		for i := 0; i < maxSize-1; i++ {
			if time.Since((*clients)[i].LastRequestedAt()) < clientIdleTime {
				insertPos = i
				break
			}
		}

		// 移动元素并插入
		copy((*clients)[insertPos+1:], (*clients)[insertPos:maxSize-1])
		(*clients)[insertPos] = client
	}
}

func (cp *ClientPool) fixUpValidClients() {
	ticker := timewheel.NewTicker(intervalOfFixUpValidClients)
	defer ticker.Stop()

	for {
		select {
		case <-cp.quit:
			return
		case <-ticker.C:
			// 避免`map-reduce`的`worker`死锁，因此先读取当前池子的内容
			cp.mutex.RLock()
			cs := make([]clienttypes.IClient, 0, len(cp.pool))
			for _, v := range cp.pool {
				cs = append(cs, v)
			}
			cp.mutex.RUnlock()

			_ = mr.MapReduceVoid[*commonpb.ClientInfo, any](
				func(source chan<- *commonpb.ClientInfo) {
					for _, c := range cs {
						if c.Enabled() {
							source <- c.GetClientInfo()
						}
					}
				},
				func(item *commonpb.ClientInfo, writer mr.Writer[any], cancel func(error)) {
					if item == nil {
						return
					}

					if err := cp.register(item, false, false); err != nil {
						cp.Errorf("failed to keepalive for the client info, cid: %s, error: %v", item.GetCid(), err)
					} else {
						cp.Infof("finish to keepalive for the client info, cid: %s", item.GetCid())
					}
				},
				func(pipe <-chan any, cancel func(error)) {
				},
				mr.WithContext(cp.ctx),
			)
		}
	}
}

// Get the specific client from client pool by `cid`
func (cp *ClientPool) Get(search *pb.SearchClient) (clienttypes.IClient, error) {
	key := search.Key()
	_, isCid := search.GetCondition().(*pb.SearchClient_Cid)

	val, err := cp.singleFlight.Do(
		key, func() (any, error) {
			var (
				cid = key
				ok  bool
			)

			cp.mutex.RLock()
			defer cp.mutex.RUnlock()

			if !isCid {
				cid, ok = cp.relations[key]
				if !ok {
					return nil, ErrClientNotFound
				}
			}
			c, ok := cp.pool[cid]

			if ok && c.Enabled() {
				return c, nil
			}

			return nil, ErrClientNotFound
		},
	)
	if err != nil {
		return nil, err
	}

	v, ok := val.(clienttypes.IClient)
	if !ok {
		return nil, errors.Errorf("the type of value[%T] is not a client", val)
	}

	return v, nil
}

// Set the client and `cid` into client pool
func (cp *ClientPool) Set(cid string, c clienttypes.IClient) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	cp.pool[cid] = c
	if create := c.GetCreateInfo(); create != nil {
		cp.relations[create.Key()] = cid
	}

	// 由于广播事件及时性得不到保障，所以这里客户端的注册不再广播
	if err := cp.register(c.GetClientInfo(), false, false); err != nil {
		cp.Errorf("failed to register the client, cid: %s, error: %v", cid, err)
	}
}

// Del the specific client from client pool by `cid`
func (cp *ClientPool) Del(cid string) (ci *commonpb.ClientInfo) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	c, ok := cp.pool[cid]
	if ok && c.Enabled() {
		create := c.GetCreateInfo()
		ci = c.GetClientInfo()
		_ = c.Close()

		// 由于创建客户端不再广播，所以删除客户端也不需要广播
		if err := cp.unregister(ci, false); err != nil {
			cp.Errorf("failed to unregister the client, cid: %s, error: %v", cid, err)
		}
		delete(cp.pool, cid)
		delete(cp.relations, create.Key())
	}

	return ci
}

// All get all clients in client pool
func (cp *ClientPool) All() []*commonpb.ClientInfo {
	val, _ := cp.singleFlight.Do(
		"all", func() (any, error) {
			cp.mutex.RLock()
			defer cp.mutex.RUnlock()

			cis := make([]*commonpb.ClientInfo, 0, len(cp.pool))
			for _, v := range cp.pool {
				if v.Enabled() {
					ci := v.GetClientInfo()
					cis = append(cis, ci)
				}
			}

			return cis, nil
		},
	)

	v, ok := val.([]*commonpb.ClientInfo)
	if !ok {
		return make([]*commonpb.ClientInfo, 0)
	}

	return v
}

// Len returns the number of clients in client pool
func (cp *ClientPool) Len() int {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	return len(cp.pool)
}
