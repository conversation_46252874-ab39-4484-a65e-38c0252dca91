package clienttypes

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

// BasicClient 自定义的`Client`的父类，由于各`Client`在不同的包中，所以公共的字段设置为导出字段
type BasicClient struct {
	logx.Logger

	Context    context.Context
	CancelFunc context.CancelFunc
	ExitCh     <-chan lang.PlaceholderType
	NodeID     string // 所属节点服务的服务ID

	id              *atomic.String // 客户端ID
	seq             *atomic.Uint32 // 序列号
	failures        *atomic.Uint32 // 连续失败次数
	disable         *atomic.Bool   // 生失效标记
	createdAt       *atomic.Time   // 创建时间
	lastRequestedAt *atomic.Time   // 最近一次业务请求时间（排除客户端自动发起的请求）
	numOfRequest    *atomic.Uint64 // 请求次数
}

func NewBasicClient(ctx context.Context, opts ...CreateClientOption) *BasicClient {
	var cancel context.CancelFunc
	ctx, cancel = context.WithCancel(ctx)

	o := &createClientOptions{}
	for _, opt := range opts {
		opt(o)
	}

	var (
		cid string
		ok  bool
	)
	if len(o.clientID) > 0 {
		cid = o.clientID
	} else if cid, ok = ctx.Value(common.ClientIDContextKey).(string); !ok || len(cid) == 0 {
		cid = common.GenerateClientID()
	}

	ctx = logx.ContextWithFields(ctx, logx.Field(logFieldKeyOfClientID, cid))
	now := time.Now().In(time.Local)

	return &BasicClient{
		Logger: logx.WithContext(ctx),

		Context:    ctx,
		CancelFunc: cancel,
		ExitCh:     make(chan lang.PlaceholderType, 1),
		NodeID:     common.ID(),

		id:              atomic.NewString(cid),
		seq:             atomic.NewUint32(0),
		failures:        atomic.NewUint32(0),
		disable:         atomic.NewBool(false),
		createdAt:       atomic.NewTime(now),
		lastRequestedAt: atomic.NewTime(now),
		numOfRequest:    atomic.NewUint64(0),
	}
}

// Cid 获取客户端ID
func (c *BasicClient) Cid() string {
	return c.id.Load()
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *BasicClient) Environment() string {
	return ""
}

// GetSeq 获取序列号（每次调用都会自增）
func (c *BasicClient) GetSeq() uint32 {
	return c.seq.Add(1)
}

// Failures 获取客户端的连续失败次数
func (c *BasicClient) Failures() uint32 {
	return c.failures.Load()
}

// ResetFailures 重置客户端的连续失败次数
func (c *BasicClient) ResetFailures() {
	c.failures.Store(0)
}

// AddFailures 客户端的连续失败次数加一
func (c *BasicClient) AddFailures() uint32 {
	return c.failures.Add(1)
}

// Enabled 客户端是否生效
func (c *BasicClient) Enabled() bool {
	return !c.disable.Load()
}

// SetDisable 把客户端设置为无效
func (c *BasicClient) SetDisable() {
	c.disable.Store(true)
}

// CreatedAt 获取客户端创建时间
func (c *BasicClient) CreatedAt() time.Time {
	return c.createdAt.Load()
}

// LastRequestedAt 获取客户端最近一次业务请求时间
func (c *BasicClient) LastRequestedAt() time.Time {
	return c.lastRequestedAt.Load()
}

// NumOfRequest 获取客户端请求次数
func (c *BasicClient) NumOfRequest() uint64 {
	return c.numOfRequest.Load()
}

// Requested 客户端完成一次请求（更新最近一次业务请求时间、增加一次请求次数）
func (c *BasicClient) Requested() {
	c.lastRequestedAt.Store(time.Now())
	c.numOfRequest.Add(1)
}

// UpdateLastRequestedAt 更新客户端最近一次业务请求时间（用于重置空闲时间）
func (c *BasicClient) UpdateLastRequestedAt(t time.Time) {
	if t.After(c.lastRequestedAt.Load()) {
		c.lastRequestedAt.Store(t)
	}
}

// GetCustomInfo 获取客户端自定义信息
func (c *BasicClient) GetCustomInfo() *structpb.Struct {
	return nil
}

func (c *BasicClient) Call(ctx context.Context, req IRequest, fn SendFunc) (resp IResponse, err error) {
	cid := c.Cid()

	if !c.Enabled() {
		return nil, errors.Errorf("the client is disable and cannot send any more requests, cid: %s", cid)
	}

	defer func() {
		var (
			name     = req.Name()
			protocol = req.Protocol()
		)

		c.Infof(
			"the request data of %s which is called by %s, cid: %s, req_key: %s, req_headers: %s, req_data: %s",
			name, protocol, cid, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(),
		)

		if err != nil {
			c.Errorf(
				"failed to call %s by %s, cid: %s, req_key: %s, req_headers: %s, req_data: %s, error: %+v",
				name, protocol, cid, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(), err,
			)
		} else if resp != nil && resp.Error() != nil {
			c.Errorf(
				"got an error from response of %s which is called by %s, cid: %s, req_key: %s, req_headers: %s, req_data: %s, %s, resp_headers: %s, resp_data: %s, error: %+v",
				name, protocol, cid, req.Key(), jsonx.MarshalIgnoreError(req.Headers()), req.Data(),
				resp.Result(), jsonx.MarshalIgnoreError(resp.Headers()), jsonx.MarshalIgnoreError(resp.Body()),
				resp.Error(),
			)
		} else if resp != nil && err == nil {
			c.Infof(
				"the response data of %s which is called by %s, cid: %s, resp_key: %s, resp_result: %s, resp_headers: %s, resp_data: %s",
				name, protocol, cid, resp.Key(), resp.Result(), jsonx.MarshalIgnoreError(resp.Headers()),
				jsonx.MarshalIgnoreError(resp.Body()),
			)
		}
	}()

	return fn(ctx, req)
}
