package bpm

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

func Test_Client(t *testing.T) {
	config := &pb.CreateClientReq{
		Type: "bpm",
		Url:  "https://bpm.52tt.com",
	}

	var err error
	config.CustomFields, err = structpb.NewStruct(map[string]any{
		"sso_uri":    "https://bpm.52tt.com",
		"account":    "sys_test_01",
		"password":   "Cs12345678",
		"device_id":  "K0U2HL6MUV77O0PR",
		"valid_code": 8874,
	})
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	c, err := NewClient(config)
	if err != nil {
		t.<PERSON><PERSON>("err: %s", err)
		t.<PERSON>ail<PERSON>ow()
	}

	callReq := &pb.ApiCallReq{
		Cid:    c.Cid(),
		Url:    "/api/api-bpm/bpm/core/bpmTaskTransfer/getMyTransOutTask",
		Method: "POST",
	}

	callReq.Body, err = structpb.NewValue(map[string]any{
		"pageNo":    1,
		"pageSize":  10,
		"sortField": "",
		"sortOrder": "asc",
		"params": map[string]any{
			"CUR_APPROVE_STATUS__S_EQ": "RUNNING",
		},
	})

	req, err := c.NewRequest(callReq)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	resp, err := c.Send(context.Background(), req)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	if resp.Status() != 200 {
		t.Errorf("resp status err: %d", resp.Status())
		t.FailNow()
	}

	t.Logf("resp body: %s", jsonx.MarshalToStringIgnoreError(resp.Body()))
}
