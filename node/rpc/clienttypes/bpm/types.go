package bpm

type (
	ClientInfo struct {
		Account    string            `json:"account" zh:"用户名"`
		Password   string            `json:"password" zh:"密码"`
		DeviceId   string            `json:"device_id" zh:"设备id"`
		ValidCode  int64             `json:"valid_code"`
		SSoUri     string            `json:"sso_uri"`
		SubEnvInfo map[string]string `json:"sub_env_info"`
	}

	AuthInfo struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
	}
)
