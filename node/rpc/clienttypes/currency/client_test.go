package currency

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

func Test_Client(t *testing.T) {
	config := &pb.CreateClientReq{
		Type: "current",
		Url:  "https://test-coin.52tt.com",
	}

	var err error
	config.CustomFields, err = structpb.NewStruct(map[string]any{
		"account":  "admin",
		"password": "admin",
		"sub_env_info": map[string]any{
			"sub_env_name": "coin-automated-testing",
		},
	})
	if err != nil {
		t.<PERSON>rrorf("err: %s", err)
		t.FailNow()
	}

	c, err := NewClient(config)
	if err != nil {
		t.Errorf("err: %s", err)
		t.<PERSON>ail<PERSON>()
	}

	// POST
	callReq := &pb.ApiCallReq{
		Cid:    c.Cid(),
		Url:    "/api/coin-config/accountiApplySeq/save",
		Method: "POST",
	}

	callReq.Body, err = structpb.NewValue(map[string]any{
		"num":      "1",
		"notes":    "test",
		"creditId": "654321",
		"status":   "SAVE",
	})

	req, err := c.NewRequest(callReq)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	resp, err := c.Send(context.Background(), req)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	if resp.Status() != 200 {
		t.Errorf("resp status err: %d", resp.Status())
		t.FailNow()
	}

	t.Logf("POST resp body: %s", jsonx.MarshalToStringIgnoreError(resp.Body()))

	// GET
	callReq = &pb.ApiCallReq{
		Cid:    c.Cid(),
		Url:    "/api/coin-config/accountiApplySeq/page",
		Method: "GET",
	}

	callReq.Body, err = structpb.NewValue(map[string]any{
		"current":  1,
		"creditId": "654321",
		"size":     10,
	})

	req, err = c.NewRequest(callReq)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	resp, err = c.Send(context.Background(), req)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	if resp.Status() != 200 {
		t.Errorf("resp status err: %d", resp.Status())
		t.FailNow()
	}

	t.Logf("GET resp body: %s", jsonx.MarshalToStringIgnoreError(resp.Body()))
}
