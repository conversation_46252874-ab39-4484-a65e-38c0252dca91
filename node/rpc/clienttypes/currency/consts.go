package currency

const (
	LoginPath = "/api/blade-auth/oauth/token"

	ConstMethodPOST = "POST"

	ConstHeaderXQwTrafficMark = "x-qw-traffic-mark" // 登陆头信息
	ConstHeaderAuthorization  = "Authorization"
	ConstHeaderTenantId       = "Tenant-Id"
	ConstHeaderBladeAuth      = "Blade-Auth"

	ConstHeaderAuthorizationValue = "Basic Y3VycmVuY3k6Y29pbkE0RjZraktzWTJxOQ=="

	ConstKeyUsername   = "username"
	ConstKeyPassword   = "password"
	ConstKeyTenantId   = "tenantId"
	ConstKeyGrantType  = "grant_type"
	ConstKeyScope      = "scope"
	ConstKeySubEnvName = "sub_env_name"
	ConstKeyType       = "type"

	ConstValueGrantTypePassword = "password"
	ConstValueScopeAll          = "all"
	ConstValueTypeAccount       = "account"
)
