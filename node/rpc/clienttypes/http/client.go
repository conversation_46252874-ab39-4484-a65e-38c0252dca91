package http

import (
	"bytes"
	"context"
	"crypto/tls"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	httppb "google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	thttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	tnethttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	productName    types.ProductName = ""
	clientTypeHTTP                   = types.ClientType("http")

	ClientReadTimeout  = time.Second * 2
	ClientWriteTimeout = time.Second * 3
)

var _ clienttypes.IClient = (*Client)(nil)

func ProductName() types.ProductName {
	return productName
}

type Client struct {
	*clienttypes.BasicClient

	transport *tnethttp.Client
	baseURL   string

	createClientInfo pb.CreateClientReq // 创建客户端时的请求信息
}

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v clienttypes.IClient, err error) {
	c := &Client{
		BasicClient: clienttypes.NewBasicClient(context.Background(), opts...),
		createClientInfo: pb.CreateClientReq{
			Type:         in.GetType(),
			Url:          in.GetUrl(),
			CustomFields: in.GetCustomFields(),
		},
	}

	if in.GetUrl() != "" {
		u, err := url.ParseRequestURI(in.GetUrl())
		if err != nil {
			return nil, err
		}

		if u.Scheme != string(constants.HTTP) && u.Scheme != string(constants.HTTPS) {
			u.Scheme = string(constants.HTTP)
		}

		c.baseURL = u.String()
	}

	c.transport = tnethttp.NewClient(
		tnethttp.ClientConf{
			BaseURL: c.baseURL,
			TlsConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	)

	return c, nil
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return clientTypeHTTP
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *Client) Environment() string {
	if c.baseURL == "" {
		return c.BasicClient.Environment()
	}

	u, err := url.Parse(c.baseURL)
	if err != nil {
		return c.BasicClient.Environment()
	}

	return strings.TrimSuffix(u.Host, ":"+strconv.Itoa(common.ConstPort80))
}

// GetCreateInfo 获取客户端的创建信息
func (c *Client) GetCreateInfo() *commonpb.CreateInfo {
	return &commonpb.CreateInfo{
		Type:         c.createClientInfo.Type,
		Url:          c.createClientInfo.Url,
		CustomFields: c.createClientInfo.CustomFields,
	}
}

// GetClientInfo 获取客户端基础信息
func (c *Client) GetClientInfo() *commonpb.ClientInfo {
	return &commonpb.ClientInfo{
		Cid:             c.Cid(),
		Nid:             c.NodeID,
		Product:         string(c.ProductName()),
		Type:            c.createClientInfo.GetType(),
		Status:          "", // TODO: 待补充
		CreatedAt:       timestamppb.New(c.CreatedAt()),
		LastCreatedAt:   nil, // TODO: 待补充
		LastRequestedAt: timestamppb.New(c.LastRequestedAt()),
		NumOfRequest:    c.NumOfRequest(),
		CustomInfo:      nil,
		LoginResp:       nil,
		UserInfo:        nil,
		CreateInfo:      c.GetCreateInfo(),
	}
}

func (c *Client) Close() error {
	if c.Enabled() {
		c.SetDisable()
	}

	if c.transport != nil {
		return c.transport.Close()
	}

	return nil
}

func (c *Client) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	var body []byte
	if in.GetBody() != nil {
		in.Headers = append(in.Headers, &httppb.HttpHeader{Key: headerKeyContentType, Value: headerValApplicationJSON})
		body, err = protobuf.MarshalJSON(in.GetBody()) // in.GetBody().MarshalJSON()
		if err != nil {
			return nil, err
		}
	}

	_url := thttp.BuildURL(c.baseURL, in.GetUrl())
	r, err := http.NewRequest(thttp.RequestMethod(in.GetMethod()), _url, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	r.Header = make(http.Header, len(in.GetHeaders()))
	for _, header := range in.GetHeaders() {
		r.Header.Add(header.GetKey(), header.GetValue())
	}

	req = &Request{
		name:    in.GetUrl(),
		seq:     c.GetSeq(),
		url:     _url,
		method:  in.GetMethod(),
		headers: r.Header,
		raw:     body,
		timeout: ClientWriteTimeout,
		handled: false,

		Request: r,
	}

	return req, err
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	var err error

	cid := c.Cid()

	if !c.Enabled() {
		return nil, errors.Errorf(
			"the client[%s] is disable and cannot send any more requests", cid,
		)
	}

	v, ok := req.(*Request)
	if !ok {
		return nil, errors.Errorf(
			"the type of request[%T] is not [%T], cid: %s", req, (*Request)(nil), cid,
		)
	} else if v.URL == nil || v.seq == 0 {
		return nil, errors.Errorf("invalid request data, the url cannot be null or seq cannot be zero, cid: %s", cid)
	} else if v.startedAt.IsZero() {
		v.startedAt = time.Now()
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		v.timeout = time.Until(deadline)
	}

	resp := &Response{
		url:    v.URL.String(),
		method: v.Method,
		seq:    v.seq,
	}

	v.Request = v.Request.WithContext(ctx)
	timeout := ClientWriteTimeout
	if v.timeout > 0 {
		timeout = v.timeout
	}
	resp.Response, err = c.transport.Send(v.Request, timeout) //nolint:bodyclose
	if err != nil {
		resp.err = err

		if resp.Response != nil {
			_ = resp.Response.Body.Close()
		}
	} else {
		resp.handle()
	}
	resp.elapsed = time.Since(v.startedAt)

	return resp, nil
}
