package http

import (
	"fmt"
	"net/url"
	"sync"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	http "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
)

var _ clienttypes.IRequest = (*Request)(nil)

type Request struct {
	*http.Request

	name      string        // 请求消息名称（唯一标识）
	seq       uint32        // 请求消息的序列号
	url       string        // 请求消息的Url
	method    string        // 请求消息的Method
	headers   http.Header   // 请求消息的Headers
	raw       []byte        // 请求消息的Body
	startedAt time.Time     // 请求开始时间
	timeout   time.Duration // 请求超时时间
	mutex     sync.Mutex
	handled   bool

	onceOfParseURL sync.Once
	uOfParseURL    *url.URL
	errOfParseURL  error
}

func (r *Request) Key() string {
	return fmt.Sprintf("url: %s, seq: %d", r.url, r.seq)
}

func (r *Request) Headers() map[string][]string {
	return r.headers
}

func (r *Request) Body() []byte {
	return r.raw
}

func (r *Request) Target() string {
	r.parseURL()
	if r.errOfParseURL != nil {
		return r.url
	}

	path := r.uOfParseURL.EscapedPath()
	if path != "" && path[0] != '/' {
		path = "/" + path
	}

	return path
}

func (r *Request) Name() string {
	return r.name
}

func (r *Request) Protocol() string {
	r.parseURL()
	if r.errOfParseURL == nil && r.uOfParseURL.Scheme != "" {
		return r.uOfParseURL.Scheme
	}

	return string(constants.HTTP)
}

func (r *Request) Host() string {
	r.parseURL()
	if r.errOfParseURL == nil && r.uOfParseURL.Hostname() != "" {
		return r.uOfParseURL.Hostname()
	}

	return ""
}

func (r *Request) Path() string {
	return r.Target()
}

func (r *Request) Data() []byte {
	return r.raw
}

func (r *Request) Timeout() time.Duration {
	return r.timeout
}

func (r *Request) StartedAt() time.Time {
	return r.startedAt
}

func (r *Request) ParseURL() (*url.URL, error) {
	r.parseURL()
	return r.uOfParseURL, r.errOfParseURL
}

func (r *Request) parseURL() {
	r.onceOfParseURL.Do(
		func() {
			r.uOfParseURL, r.errOfParseURL = url.Parse(r.url)
		},
	)
}
