package http

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/utils"

	http "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
)

const (
	headerKeyContentType     = "Content-Type"
	headerValApplicationJSON = "application/json"
)

var _ clienttypes.IResponse = (*Response)(nil)

type Response struct {
	*http.Response

	url        string        // 请求消息的Url
	method     string        // 请求消息的Method
	seq        uint32        // 响应消息的序列号
	elapsed    time.Duration // 请求发起到响应接收的耗时
	header     http.Header   // 响应消息的Header数据
	body       []byte        // 响应消息的Body数据
	status     string        // 响应消息的Status数据
	statusCode int           // 响应消息的StatusCode数据
	err        error

	bodyOnce sync.Once
	_body    any
}

func (r *Response) Key() string {
	return fmt.Sprintf("url: %s, seq: %d", r.url, r.seq)
}

func (r *Response) Data() []byte {
	return r.body
}

func (r *Response) Headers() *clienttypes.Headers {
	return &clienttypes.Headers{
		Header: r.header,
	}
}

func (r *Response) Body() any {
	r.bodyOnce.Do(
		func() {
			if len(r.body) == 0 {
				r._body = nil
				return
			}

			for k, vs := range r.header {
				if strings.EqualFold(k, headerKeyContentType) {
					for _, v := range vs {
						if strings.Contains(strings.ToLower(v), headerValApplicationJSON) {
							var m map[string]any
							err := json.Unmarshal(r.body, &m) // without using json.Number
							if err == nil {
								r._body = m
								return
							}

							logx.Errorf("failed to unmarshal the response data, data: %s, error: %v", r.body, err)
							break // 跳出`for`循环
						}
					}

					break // 跳出`for`循环
				}
			}

			r._body = utils.ByteSliceToString(r.body)
		},
	)

	return r._body
}

func (r *Response) Status() int32 {
	return int32(r.statusCode)
}

func (r *Response) Result() string {
	return fmt.Sprintf("status code: %d", r.statusCode)
}

func (r *Response) Error() error {
	return r.err
}

func (r *Response) Elapsed() time.Duration {
	return r.elapsed
}

func (r *Response) handle() {
	if r.Response == nil {
		return
	}

	r.handleHeader()
	r.handleBody()
	r.handleStatus()
}

func (r *Response) handleHeader() {
	r.header = make(http.Header)
	for k, v := range r.Response.Header {
		r.header[k] = v
	}
}

func (r *Response) handleBody() {
	var buf bytes.Buffer
	_, _ = buf.ReadFrom(r.Response.Body)
	_ = r.Response.Body.Close()
	r.body = buf.Bytes()
}

func (r *Response) handleStatus() {
	r.status = r.Response.Status
	r.statusCode = r.Response.StatusCode
}
