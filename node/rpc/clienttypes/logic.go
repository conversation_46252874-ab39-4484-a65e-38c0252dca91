package clienttypes

import (
	"context"
	"reflect"
	"strconv"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type (
	SetupHandler    func(*pb.ApiCallReq, ...Option) (*pb.ApiCallReq, error)
	RunHandler      func(*pb.ApiCallReq, ...Option) (IResponse, error)
	TeardownHandler func(*pb.ApiCallReq, IResponse, ...Option) (*pb.ApiCallResp, error)

	ReqMethodHandler       func(string) (string, error)
	ReqHeaderHandler       func([]*http.HttpHeader) ([]*http.HttpHeader, error)
	ReqBodyHandler         func(*structpb.Value) (*structpb.Value, error)
	ReqCustomFieldsHandler func(*structpb.Struct) (*structpb.Struct, error)

	RespHeaderHandler func(*Headers) ([]*http.HttpHeader, error)
	RespBodyHandler   func(any) (*structpb.Value, error)
	RespStatusHandler func(int32) (int32, error)

	Option func(*handler)

	handler struct {
		setup    SetupHandler
		run      RunHandler
		teardown TeardownHandler

		reqMethod       ReqMethodHandler
		reqHeader       ReqHeaderHandler
		reqBody         ReqBodyHandler
		reqCustomFields ReqCustomFieldsHandler

		respHeader RespHeaderHandler
		respBody   RespBodyHandler
		respStatus RespStatusHandler
	}

	CommonLogic struct {
		ctx context.Context
		c   IClient
		logx.Logger
	}
)

func WithSetupHandler(f SetupHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.setup = f
		}
	}
}

func WithRunHandler(f RunHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.run = f
		}
	}
}

func WithTeardownHandler(f TeardownHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.teardown = f
		}
	}
}

func WithReqMethodHandler(f ReqMethodHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.reqMethod = f
		}
	}
}

func WithReqHeaderHandler(f ReqHeaderHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.reqHeader = f
		}
	}
}

func WithReqBodyHandler(f ReqBodyHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.reqBody = f
		}
	}
}

func WithReqCustomFieldsHandler(f ReqCustomFieldsHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.reqCustomFields = f
		}
	}
}

func WithRespHeaderHandler(f RespHeaderHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.respHeader = f
		}
	}
}

func WithRespBodyHandler(f RespBodyHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.respBody = f
		}
	}
}

func WithRespStatusHandler(f RespStatusHandler) Option {
	return func(h *handler) {
		if f != nil {
			h.respStatus = f
		}
	}
}

func NewCommonLogic(ctx context.Context, c IClient) *CommonLogic {
	return &CommonLogic{
		ctx:    ctx,
		c:      c,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CommonLogic) newDefaultHandler() *handler {
	return &handler{
		setup:      l.SetupHandler,
		run:        l.RunHandler,
		teardown:   l.TeardownHandler,
		respHeader: internalRespHeaderHandler,
		respBody:   internalRespBodyHandler,
		respStatus: internalRespStatusHandler,
	}
}

func (l *CommonLogic) Call(in *pb.ApiCallReq, options ...Option) (out *pb.ApiCallResp, err error) {
	h := l.newDefaultHandler()
	for _, option := range options {
		option(h)
	}

	// setup handler
	if h.setup != nil {
		in, err = h.setup(in, options...)
		if err != nil {
			return nil, err
		}
	}

	// run handler
	resp, err := h.run(in, options...)
	if err != nil {
		return nil, err
	}

	// teardown handler
	return h.teardown(in, resp, options...)
}

func (l *CommonLogic) SetupHandler(in *pb.ApiCallReq, options ...Option) (out *pb.ApiCallReq, err error) {
	return in, nil
}

func (l *CommonLogic) RunHandler(in *pb.ApiCallReq, options ...Option) (resp IResponse, err error) {
	h := l.newDefaultHandler()
	for _, option := range options {
		option(h)
	}

	if h.reqMethod != nil {
		in.Method, err = h.reqMethod(in.GetMethod())
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to handle the method of request, request: %s",
				protobuf.MarshalJSONToStringIgnoreError(in),
			)
		}
	}

	if h.reqHeader != nil {
		in.Headers, err = h.reqHeader(in.GetHeaders())
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to handle the header of request, request: %s",
				protobuf.MarshalJSONToStringIgnoreError(in),
			)
		}
	}

	if h.reqBody != nil {
		in.Body, err = h.reqBody(in.GetBody())
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to handle the body of request, request: %s",
				protobuf.MarshalJSONToStringIgnoreError(in),
			)
		}
	}

	if h.reqCustomFields != nil {
		in.CustomFields, err = h.reqCustomFields(in.GetCustomFields())
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to handle the custom fields of request, request: %s",
				protobuf.MarshalJSONToStringIgnoreError(in),
			)
		}
	}

	req, err := l.c.NewRequest(in)
	if err != nil {
		return nil, err
	}

	ret, err := limiter.Allow(l.ctx, l.c.ProductName(), l.c.Environment(), req.Target())
	if err != nil {
		return nil, err
	} else if ret != nil {
		// set the result of rate limit to trailer
		limiter.SetToTrailer(l.ctx, *ret)

		if ret.Allowed < 1 {
			return nil, limiter.LimitExceededErr
		}
	}

	resp, err = l.c.Send(l.ctx, req)
	if rv := reflect.ValueOf(resp); resp != nil && rv.IsValid() && !rv.IsNil() {
		if bm, ok := resp.(BusinessMetric); ok && bm != nil {
			var (
				protocol   = req.Protocol()
				grpcStatus = ""
				httpStatus = ""
			)

			switch protocol {
			case string(constants.GRPC):
				grpcStatus = strconv.FormatInt(int64(resp.Status()), 10)
			case string(constants.HTTP), string(constants.HTTPS):
				httpStatus = strconv.FormatInt(int64(resp.Status()), 10)
			}

			// upload the business metric
			metrics.BusinessMetricHandler.Inc(
				string(l.c.ProductName()), protocol, req.Host(), req.Path(),
				grpcStatus, httpStatus, strconv.FormatInt(int64(bm.BusinessStatus()), 10),
			)
		}
	}

	return resp, err
}

func (l *CommonLogic) TeardownHandler(
	in *pb.ApiCallReq, resp IResponse, options ...Option,
) (out *pb.ApiCallResp, err error) {
	err = resp.Error()
	if err != nil {
		return nil, errorx.Newf(
			errorx.CallExternalAPIFailure,
			"get an error from the response, cid: %s, resp key: %s, resp result: %s, resp error: %v",
			in.GetCid(), resp.Key(), resp.Result(), err,
		)
	}

	h := l.newDefaultHandler()
	for _, option := range options {
		option(h)
	}

	out = &pb.ApiCallResp{
		ClientInfo:   l.c.GetClientInfo(),
		CustomFields: l.c.GetCustomInfo(),
		CallResp:     &pb.ApiCallResp_CallResp{},
	}

	out.CallResp.Header, err = h.respHeader(resp.Headers())
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to handle the header of response, cid: %s, resp key: %s, resp result: %s",
			in.GetCid(), resp.Key(), resp.Result(),
		)
	}

	out.CallResp.Body, err = h.respBody(resp.Body())
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to handle the body of response, cid: %s, resp key: %s, resp result: %s",
			in.GetCid(), resp.Key(), resp.Result(),
		)
	}

	out.CallResp.Status, err = h.respStatus(resp.Status())
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to handle the status of response, cid: %s, resp key: %s, resp result: %s",
			in.GetCid(), resp.Key(), resp.Result(),
		)
	}

	return out, nil
}

func internalRespHeaderHandler(in *Headers) (out []*http.HttpHeader, err error) {
	if in == nil {
		return
	}

	out = make([]*http.HttpHeader, 0, len(in.Header)+len(in.Trailer))
	for _, m := range []map[string][]string{in.Header, in.Trailer} {
		for k, vs := range m {
			for _, v := range vs {
				out = append(
					out, &http.HttpHeader{
						Key:   k,
						Value: v,
					},
				)
			}
		}
	}

	return
}

func internalRespBodyHandler(in any) (out *structpb.Value, err error) {
	return protobuf.NewValue(in)
}

func internalRespStatusHandler(in int32) (out int32, err error) {
	return in, nil
}
