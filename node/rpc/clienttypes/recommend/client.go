package recommend

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"time"

	"github.com/devfeel/mapper"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/dynamicpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/grpcpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	productName         types.ProductName = "recommend"
	clientTypeRecommend                   = types.ClientType(productName)

	defaultBranch               = "release/tt_rel_prod" // 默认分支
	defaultClientVersion uint32 = 104464387             // 默认版本: 6.58.3
)

var (
	_ clienttypes.IClient = (*Client)(nil)

	errEmptyURL             = errors.New("the url cannot be empty")
	errWrongScheme          = errors.Errorf("the scheme of url must be %s", constants.GRPC)
	errEmptyTarget          = errors.New("the url cannot be empty while creating grpc request")
	errEmptyMethod          = errors.New("the method cannot be empty while creating grpc request")
	errProtoManagerNotFound = errors.Errorf("the proto manager not found, product name: %s", productName)

	emptyReqBody = []byte("{}")

	grpcPool = grpcpool.NewGRPCPool()
)

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v clienttypes.IClient, err error) {
	ci, err := parseCreateClientReq(in)
	if err != nil {
		return nil, errors.Errorf("failed to create %q client, error: %+v", productName, err)
	}

	pm, ok := proto.GetProtoManager(productName)
	if !ok {
		return nil, errProtoManagerNotFound
	}

	c := &Client{
		BasicClient: clienttypes.NewBasicClient(context.Background(), opts...),

		createClientInfo: &pb.CreateClientReq{
			Type:         in.GetType(),
			Url:          in.GetUrl(),
			CustomFields: in.GetCustomFields(),
		},
		clientInfo: ci,

		pm: pm,
	}

	return c, nil
}

func defaultClientInfo() ClientInfo {
	return ClientInfo{
		Branch:        defaultBranch,
		PlatformType:  tt.ConstPlatformTypeMobile,
		ClientVersion: "",
		ClientType:    tt.ConstClientTypeAndroid,
		AppID:         tt.ConstAppIDTTNormal,
		MarketID:      tt.ConstMarketIDNone,
	}
}

func parseCreateClientReq(in *pb.CreateClientReq) (ci ClientInfo, err error) {
	ci = defaultClientInfo()

	if in.GetUrl() != "" {
		ci.Url = in.GetUrl()

		u, err := url.ParseRequestURI(in.GetUrl())
		if err != nil {
			return ci, err
		}

		if u.Scheme != string(constants.GRPC) {
			return ci, errWrongScheme
		}

		host := u.Host
		if host == "" {
			host = u.Path
		}
		if u.Port() == "" {
			host = fmt.Sprintf("%s:%d", host, common.ConstPort80)
		}
		ci.grpcInfo.Target = host
	}

	customFields := in.GetCustomFields()
	if customFields != nil {
		if err = mapper.MapperMap(customFields.AsMap(), &ci); err != nil {
			return ci, err
		}
	}

	return ci, nil
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return types.ClientType(c.createClientInfo.GetType())
}

// GetCreateInfo 获取客户端的创建信息
func (c *Client) GetCreateInfo() *commonpb.CreateInfo {
	return &commonpb.CreateInfo{
		Type:         c.createClientInfo.GetType(),
		Url:          c.createClientInfo.GetUrl(),
		CustomFields: c.createClientInfo.GetCustomFields(),
	}
}

// GetClientInfo 获取客户端基础信息
func (c *Client) GetClientInfo() *commonpb.ClientInfo {
	return &commonpb.ClientInfo{
		Cid:             c.Cid(),
		Nid:             c.NodeID,
		Product:         string(c.ProductName()),
		Type:            c.createClientInfo.GetType(),
		Status:          "", // TODO: 待补充
		CreatedAt:       timestamppb.New(c.CreatedAt()),
		LastCreatedAt:   nil, // TODO: 待补充
		LastRequestedAt: timestamppb.New(c.LastRequestedAt()),
		NumOfRequest:    c.NumOfRequest(),
		CustomInfo:      c.GetCustomInfo(),
		LoginResp:       nil,
		UserInfo:        c.GetUserInfo(),
		CreateInfo:      c.GetCreateInfo(),
	}
}

// GetCustomInfo 获取客户端自定义信息
func (c *Client) GetCustomInfo() *structpb.Struct {
	m := map[string]any{
		keyOfBranch:        c.clientInfo.Branch,
		keyOfClientVersion: c.clientInfo.ClientVersion,
	}

	v, err := protobuf.NewStruct(m)
	if err != nil {
		c.Errorf("failed to create custom info, data: %s, error: %+v", jsonx.MarshalIgnoreError(m), err)
	}

	return v
}

func (c *Client) GetUserInfo() *commonpb.UserInfo {
	return &commonpb.UserInfo{
		Cid: c.Cid(),
		Nid: c.NodeID,
		Uid: "",
	}
}

func (c *Client) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.CancelFunc != nil {
				c.CancelFunc()
			}
		},
	)

	return err
}

func (c *Client) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	var (
		method       = in.GetMethod()
		headers      = in.GetHeaders()
		customFields = in.GetCustomFields()

		setNoTLS bool
		info     grpcInfo
		conf     tgrpc.ClientConf
		body     []byte
	)

	defer func() {
		if err == nil {
			grpcPool.Register(info.Target, conf)
		}
	}()

	if customFields != nil {
		m := customFields.AsMap()
		if err = mapper.MapperMap(m, &info); err != nil {
			c.Errorf("failed to parse custom fields, custom fields: %s, error: %+v", jsonx.MarshalIgnoreError(m), err)
		} else if _, ok := m[keyOfNoTLS]; ok {
			setNoTLS = true
		}
	}
	if info.Target == "" {
		info.Target = c.clientInfo.Target
	}
	if info.Authority == "" {
		info.Authority = c.clientInfo.Authority
	}
	if info.UserAgent == "" {
		info.UserAgent = c.clientInfo.UserAgent
	}
	if !setNoTLS {
		info.NoTLS = c.clientInfo.NoTLS
	}
	conf = tgrpc.ClientConf{
		Authority: info.Authority,
		UserAgent: info.UserAgent,
		NoTLS:     info.NoTLS,
	}

	if info.Target == "" {
		return nil, errEmptyTarget
	}
	if method == "" {
		return nil, errEmptyMethod
	}

	md, err := c.pm.FindMethodDescriptorByName(method, protobuf.WithProductBranch(c.clientInfo.Branch))
	if err != nil {
		return nil, err
	}

	bodyVal := in.GetBody()
	if bodyVal != nil {
		if _, ok := bodyVal.GetKind().(*structpb.Value_NullValue); !ok {
			body, err = protobuf.MarshalJSON(bodyVal) // in.GetBody().MarshalJSON()
			if err != nil {
				return nil, err
			}
		}
	}
	if body == nil {
		body = emptyReqBody
	}

	hs := make(metadata.MD, len(headers)+3)
	for _, h := range headers {
		hs.Append(h.GetKey(), h.GetValue())
	}

	r := &Request{
		c: conf,

		name:    method,
		seq:     c.GetSeq(),
		raw:     body,
		host:    info.Target,
		branch:  c.clientInfo.Branch,
		timeout: waitRespTimeout,

		methodDescriptor: md,
		inputDescriptor:  md.Input(),
		outputDescriptor: md.Output(),
		headers:          hs,
	}
	r.r = tgrpc.NewRequest(md, c.grpcRequestSupplier(r), tgrpc.WithCallOptions(grpc.UseCompressor(gzip.Name)))

	return r, nil
}

func (c *Client) grpcRequestSupplier(req *Request) tgrpc.RequestSupplier {
	cid := c.Cid()

	return func(msg *dynamicpb.Message) error {
		// JSON -> PB Message
		if err := c.pm.UnmarshalMessage(msg, req.raw); err != nil {
			return errors.Wrapf(
				err,
				"failed to unmarshal request data to message[%s], cid: %s, method: %s, seq: %d",
				req.inputDescriptor.FullName(), cid, req.methodDescriptor.FullName(), req.seq,
			)
		}

		c.Infof(
			"the request data of %s, cid: %s, data: %s",
			req.methodDescriptor.FullName(), cid, protobuf.MarshalJSONToStringIgnoreError(msg),
		)

		return io.EOF
	}
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	return c.Call(ctx, req, c.send)
}

func (c *Client) send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	cid := c.Cid()
	logger := logx.WithContext(ctx)

	v, ok := req.(*Request)
	if !ok {
		return nil, errors.Errorf("invalid request type, expected %T, but got %T, cid: %s", (*Request)(nil), req, cid)
	}

	transport, err := grpcPool.Get(v.host, v.c)
	if err != nil {
		return nil, err
	} else if transport == nil {
		return nil, errors.Errorf(
			"grpc transport is null, cid: %s, method: %s, seq: %d",
			cid, v.methodDescriptor.FullName(), v.seq,
		)
	} else if v.methodDescriptor.IsStreamingClient() || v.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, cid: %s, method: %s, seq: %d",
			cid, v.methodDescriptor.FullName(), v.seq,
		)
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		v.timeout = time.Until(deadline)
	}

	v.headers.Set(reqHeaderKeyOfClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	v.headers.Set(
		reqHeaderKeyOfClientVersion,
		strconv.FormatUint(uint64(tt.GetClientVersionByString(c.clientInfo.ClientVersion)), 10),
	)
	v.headers.Set(reqHeaderKeyOfDeviceID, ToDeviceIDBase64(c.clientInfo.DeviceID))
	v.headers.Set(reqHeaderKeyOfMarketID, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	if c.clientInfo.SubEnvFlag != "" {
		v.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	r := &Response{
		ctx:              ctx,
		seq:              v.seq,
		methodDescriptor: v.methodDescriptor,
		outputDescriptor: v.outputDescriptor,
	}
	r.r = tgrpc.NewResponse(v.methodDescriptor, r)

	if v.startedAt.IsZero() {
		v.startedAt = time.Now()
	}

	timeout := waitRespTimeout
	if v.timeout > 0 {
		timeout = v.timeout
	}
	invokeCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	err = transport.InvokeRPC(invokeCtx, v.r, v.headers, r.r)
	if err != nil && !errors.Is(err, r.err) {
		logger.Errorf(
			"failed to invoke rpc, cid: %s, method: %s, seq: %d, error: %+v",
			cid, v.methodDescriptor.FullName(), v.seq, err,
		)
		r.err = err
	}
	r.elapsed = time.Since(v.startedAt)

	logger.Infof(
		"the response data of %q which is invoked by grpc, cid: %s, data: %s",
		v.methodDescriptor.FullName(), cid, jsonx.MarshalIgnoreError(r.body),
	)

	return r, nil
}
