package recommend

import (
	"encoding/base64"
	"encoding/hex"
	"regexp"
	"strings"

	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/utils"
)

const (
	iosUDIDFirstIndex = 8
	iosUDIDLen        = 8 + 16
)

var (
	androidSerialPattern = regexp.MustCompile("^[0-9a-zA-Z]{32}$")
	iosUDIDPattern       = regexp.MustCompile("^[0-9A-Z]{8}-[0-9A-Z]{16}$")
)

// ToDeviceID 把字符串转为设备ID
// 1. 符合`Android`设备ID格式的字符串，转为小写后直接返回
// 2. 符合`iOS`设备ID格式的字符串，转为大写后直接返回
// 3. 其他字符串，进行md5编码后返回（返回长度为32的字符串）
func ToDeviceID(in string) string {
	if androidSerialPattern.MatchString(in) {
		return strings.ToLower(in)
	}
	if iosUDIDPattern.MatchString(in) {
		return strings.ToUpper(in)
	}

	return hash.Md5Hex(utils.StringToByteSlice(in))
}

// ToDeviceIDHex 把字符串转为`hex`编码的设备ID
func ToDeviceIDHex(in string) []byte {
	deviceID := ToDeviceID(in)
	deviceID = strings.ReplaceAll(deviceID, "-", "")

	s, err := hex.DecodeString(deviceID)
	if err != nil {
		return utils.StringToByteSlice(deviceID)
	}

	return s
}

// ToDeviceIDBase64 把字符串转为`base64`编码的设备ID
func ToDeviceIDBase64(in string) string {
	return base64.StdEncoding.EncodeToString(ToDeviceIDHex(in))
}

// ParseFromDeviceIDBase64 从`base64`编码的设备ID解析出字符串
func ParseFromDeviceIDBase64(in string) string {
	s, err := base64.StdEncoding.DecodeString(in)
	if err != nil {
		return ""
	}

	out := hex.EncodeToString(s)
	if len(out) == iosUDIDLen {
		return strings.ToUpper(out[:iosUDIDFirstIndex] + "-" + out[iosUDIDFirstIndex:])
	}

	return out
}
