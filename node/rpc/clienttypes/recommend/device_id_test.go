package recommend

import "testing"

func TestToDeviceID(t *testing.T) {
	type args struct {
		in string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "android serial",
			args: args{
				in: "42bcc6ceb31618e0d195a00a5e4b66fc",
			},
			want: "42bcc6ceb31618e0d195a00a5e4b66fc",
		},
		{
			name: "ios udid",
			args: args{
				in: "00008130-000168223C93803A",
			},
			want: "00008130-000168223C93803A",
		},
		{
			name: "other string",
			args: args{
				in: "allen",
			},
			want: "a34c3d45b6018d3fd5560b103c2a00e2",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := ToDeviceID(tt.args.in)
				if got != tt.want {
					t.Errorf("ToDeviceID() = %v, want %v", got, tt.want)
				}

				t.Logf("DeviceID: %s", got)
			},
		)
	}
}

func TestToDeviceIDBase64(t *testing.T) {
	type args struct {
		in string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "android serial",
			args: args{
				in: "42bcc6ceb31618e0d195a00a5e4b66fc",
			},
			want: "QrzGzrMWGODRlaAKXktm/A==",
		},
		{
			name: "ios udid",
			args: args{
				in: "00008130-000168223C93803A",
			},
			want: "AACBMAABaCI8k4A6",
		},
		{
			name: "other string",
			args: args{
				in: "allen",
			},
			want: "o0w9RbYBjT/VVgsQPCoA4g==",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := ToDeviceIDBase64(tt.args.in)
				if got != tt.want {
					t.Errorf("ToDeviceIDBase64() = %v, want %v", got, tt.want)
				}

				t.Logf("DeviceID: %s", got)
			},
		)
	}
}

func TestParseFromDeviceIDBase64(t *testing.T) {
	type args struct {
		in string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "android serial",
			args: args{
				in: "QrzGzrMWGODRlaAKXktm/A==",
			},
			want: "42bcc6ceb31618e0d195a00a5e4b66fc",
		},
		{
			name: "ios udid",
			args: args{
				in: "AACBMAABaCI8k4A6",
			},
			want: "00008130-000168223C93803A",
		},
		{
			name: "other string",
			args: args{
				in: "o0w9RbYBjT/VVgsQPCoA4g==",
			},
			want: "a34c3d45b6018d3fd5560b103c2a00e2",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := ParseFromDeviceIDBase64(tt.args.in)
				if got != tt.want {
					t.Errorf("ParseFromDeviceIDBase64() = %v, want %v", got, tt.want)
				}

				t.Logf("got: %s", got)
			},
		)
	}
}
