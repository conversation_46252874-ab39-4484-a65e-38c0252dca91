package recommend

import (
	"fmt"
	"time"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
)

var _ clienttypes.IRequest = (*Request)(nil)

type Request struct {
	c tgrpc.ClientConf
	r *tgrpc.Request

	name      string        // 请求消息名称（唯一标识）
	seq       uint32        // 请求消息的序列号
	raw       []byte        // 请求消息的Body
	startedAt time.Time     // 请求开始时间
	host      string        // 请求目标地址（域名）
	branch    string        // 接口所属分支
	timeout   time.Duration // 请求超时时间

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	inputDescriptor  protoreflect.MessageDescriptor // 请求描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述
	headers          metadata.MD                    // 请求消息的Headers
}

func (r *Request) Key() string {
	return fmt.Sprintf("method: %s, seq: %d", r.methodDescriptor.FullName(), r.seq)
}

func (r *Request) Headers() map[string][]string {
	return r.headers.Copy()
}

func (r *Request) Body() []byte {
	return r.raw
}

func (r *Request) Target() string {
	return string(r.methodDescriptor.FullName())
}

func (r *Request) Name() string {
	return r.name
}

func (r *Request) Protocol() string {
	return string(constants.GRPC)
}

func (r *Request) Host() string {
	return r.host
}

func (r *Request) Path() string {
	return string(r.methodDescriptor.FullName())
}

func (r *Request) Data() []byte {
	return r.raw
}

func (r *Request) Timeout() time.Duration {
	return r.timeout
}
