package recommend

import (
	"sync"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type (
	Client struct {
		*clienttypes.BasicClient

		createClientInfo *pb.CreateClientReq // 创建客户端时的请求信息
		clientInfo       ClientInfo          // 客户端信息

		pm *protobuf.ProtoManager // ProtoBuf管理器

		closeOnce sync.Once
	}

	ClientInfo struct {
		Url           string          `json:"url" zh:"服务地址"`
		Branch        string          `json:"branch,default=master" zh:"分支"`
		PlatformType  tt.PlatformType `json:"platform_type,omitempty,optional,default=1" zh:"平台类型（移动端，Web端，PC端，Pad端，QQ小程序，微信小程序）"`
		ClientVersion string          `json:"client_version,omitempty,optional" zh:"客户端版本"`
		ClientType    tt.ClientType   `json:"client_type,omitempty,optional,default=0" zh:"客户端类型（TT：安卓，IOS，WEB，PC；提提电竞：安卓App，安卓H5，IOS App，IOS H5）"`
		AppID         tt.AppID        `json:"app_id,omitempty,optional,default=0" zh:"App ID"`
		MarketID      tt.MarketID     `json:"market_id,omitempty,optional,default=0" zh:"Market ID"`
		DeviceID      string          `json:"device_id,omitempty,optional" zh:"设备ID"`
		SubEnvFlag    string          `json:"sub_env_flag,omitempty,optional" zh:"子环境标识"`

		grpcInfo
	}
	grpcInfo struct {
		Target    string `json:"target,omitempty,optional" zh:"gRPC Target Endpoint"`
		Authority string `json:"authority,omitempty,optional" zh:"gRPC Authority"`
		UserAgent string `json:"user_agent,omitempty,optional" zh:"gRPC User-Agent"`
		NoTLS     bool   `json:"no_tls,omitempty,optional" zh:"gRPC NoTLS"`
	}
)
