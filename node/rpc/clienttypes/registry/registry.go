package registry

import (
	"strings"
	"sync"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

var r = &Registry{
	cr: &clientRegistry{
		cache: make(map[types.ClientType]clienttypes.NewClientFunc),
	},
	lr: &logicRegistry{
		cache: make(map[types.ProductName]map[types.LogicName]clienttypes.NewLogicFunc),
	},
}

type (
	clientRegistry struct {
		cache       map[types.ClientType]clienttypes.NewClientFunc
		clientTypes []types.ClientType

		lock sync.RWMutex
		once sync.Once
	}

	logicRegistry struct {
		cache map[types.ProductName]map[types.LogicName]clienttypes.NewLogicFunc
		lock  sync.RWMutex
	}

	Registry struct {
		cr *clientRegistry
		lr *logicRegistry
	}
)

func RegisterClientFunc(clientType types.ClientType, fn clienttypes.NewClientFunc) {
	r.cr.lock.Lock()
	defer r.cr.lock.Unlock()

	r.cr.cache[clientType] = fn
}

func CheckProductName(clientType types.ClientType) (clienttypes.NewClientFunc, error) {
	r.cr.lock.RLock()
	defer r.cr.lock.RUnlock()

	fn, ok := r.cr.cache[clientType]
	if !ok {
		return nil, errors.Errorf("the client type[%s] is not currently supported", clientType)
	}

	return fn, nil
}

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (clienttypes.IClient, error) {
	fn, err := CheckProductName(types.ClientType(in.GetType()))
	if err != nil {
		return nil, err
	}

	return fn(in, opts...)
}

func GetAllClientTypes() (clientTypes []types.ClientType) {
	r.cr.once.Do(
		func() {
			r.cr.lock.RLock()
			r.cr.clientTypes = make([]types.ClientType, 0, len(r.cr.cache))
			for k := range r.cr.cache {
				r.cr.clientTypes = append(r.cr.clientTypes, k)
			}
			r.cr.lock.RUnlock()
		},
	)

	clientTypes = make([]types.ClientType, len(r.cr.clientTypes))
	copy(clientTypes, r.cr.clientTypes)
	return
}

func RegisterLogicFunc(productName types.ProductName, logicName types.LogicName, fn clienttypes.NewLogicFunc) {
	r.lr.lock.Lock()
	defer r.lr.lock.Unlock()

	_, ok := r.lr.cache[productName]
	if !ok {
		r.lr.cache[productName] = make(map[types.LogicName]clienttypes.NewLogicFunc)
	}
	r.lr.cache[productName][logicName] = fn
}

func GetLogicFunc(productName types.ProductName, logicName types.LogicName) (clienttypes.NewLogicFunc, bool) {
	r.lr.lock.RLock()
	defer r.lr.lock.RUnlock()

	m, ok := r.lr.cache[productName]
	if !ok {
		return nil, ok
	}

	l, ok := m[logicName]
	return l, ok
}

func SplitURL(url string) (product types.ProductName, logic types.LogicName, method string) {
	url = strings.TrimPrefix(url, clienttypes.Slash)

	ss := strings.Split(url, clienttypes.Slash)
	l := len(ss)
	if l > 0 && strings.EqualFold(ss[0], clienttypes.CustomLogicURL) {
		ss = ss[1:]
	}

	if l == 0 {
		return
	}
	if l >= 1 {
		product = types.ProductName(ss[0])
	}
	if l >= 2 {
		logic = types.LogicName(ss[1])
	}
	if l >= 3 {
		method = ss[2]
	}

	return
}
