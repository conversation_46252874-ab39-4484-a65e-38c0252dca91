package sso

import (
	"context"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	httpcli "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/http"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

const (
	refreshTokenInterval = 9 * time.Minute
)

type Client struct {
	*httpcli.Client

	closeOnce sync.Once
	refreshDo func()
}

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v *Client, err error) {
	c := &Client{}
	ic, err := httpcli.NewClient(in, opts...)
	if err != nil {
		return nil, err
	}

	c.Client = ic.(*httpcli.Client)
	return c, nil
}

// GetCreateInfo 获取客户端的创建信息
func (c *Client) GetCreateInfo() *commonpb.CreateInfo {
	return c.Client.GetCreateInfo()
}

// GetClientInfo 获取客户端基础信息
func (c *Client) GetClientInfo() *commonpb.ClientInfo {
	return c.Client.GetClientInfo()
}

func (c *Client) Close() error {
	c.closeOnce.Do(
		func() {
			// 发送 Context.Done() 信号
			if c.CancelFunc != nil {
				c.CancelFunc()
			}
		},
	)

	// 关闭http客户端
	return c.Client.Close()
}

func (c *Client) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	return c.Client.NewRequest(in)
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	return c.Client.Send(ctx, req)
}

// Keepalive 定时执行`refreshDo`
func (c *Client) Keepalive(refreshDo func()) {
	if refreshDo == nil {
		return
	}

	c.refreshDo = refreshDo

	tickDo := func() {
		refreshTokenTicker := timewheel.NewTicker(refreshTokenInterval)
		defer refreshTokenTicker.Stop()

		for {
			select {
			case <-refreshTokenTicker.C:
				// 定时发送刷新Token请求
				threading.GoSafe(refreshDo)
			case <-c.Context.Done():
				return
			case <-c.ExitCh:
				return
			}
		}
	}

	// 持续发送心跳
	threading.GoSafe(tickDo)
}
