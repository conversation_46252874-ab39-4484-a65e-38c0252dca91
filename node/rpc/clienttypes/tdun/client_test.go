package tdun

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

func Test_Client(t *testing.T) {
	config := &pb.CreateClientReq{
		Type: "tdun",
		Url:  "http://urrc-test-tc.52tt.com",
	}

	var err error
	config.CustomFields, err = structpb.NewStruct(map[string]any{
		"account":  "ningyuejun10",
		"password": "123456",
		"sub_env_info": map[string]any{
			"sub_env_name": "urrc-220908152001511441",
		},
		"tenant_id": "000000",
	})
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	c, err := NewClient(config)
	if err != nil {
		t.<PERSON>("err: %s", err)
		t.<PERSON>ail<PERSON><PERSON>()
	}

	callReq := &pb.ApiCallReq{
		Cid:    c.Cid(),
		Url:    "/api/urrc-main-data/appscene/list",
		Method: "GET",
	}

	callReq.Body, err = structpb.NewValue(map[string]any{
		"sceneName": **********,
		"current":   "1",
		"size":      "20",
	})

	req, err := c.NewRequest(callReq)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	resp, err := c.Send(context.Background(), req)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	if resp.Status() != 200 {
		t.Errorf("resp status err: %d", resp.Status())
		t.FailNow()
	}

	t.Logf("resp body: %s", jsonx.MarshalToStringIgnoreError(resp.Body()))
}
