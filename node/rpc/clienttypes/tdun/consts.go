package tdun

const (
	LoginPath = "/api/blade-auth/oauth/token"

	ConstMethodPOST = "POST"

	ConstHeaderXQwTrafficMark = "x-qw-traffic-mark" // 登陆头信息
	ConstHeaderAuthorization  = "Authorization"
	ConstHeaderTenantId       = "Tenant-Id"
	ConstHeaderBladeAuth      = "Blade-Auth"

	ConstHeaderAuthorizationValue = "Basic dXJyYzpNNTlaR25IR1gyTkRkeUFy"

	ConstKeyUsername   = "username"
	ConstKeyPassword   = "password"
	ConstKeyTenantId   = "tenantId"
	ConstKeyGrantType  = "grant_type"
	ConstKeyScope      = "scope"
	ConstKeySubEnvName = "sub_env_name"

	ConstValueGrantTypePassword = "password"
	ConstValueScopeAll          = "all"
)
