package tt

import (
	"context"
	"encoding/hex"
	es "errors"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/devfeel/mapper"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/utils"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/atomic"
	"golang.org/x/exp/slices"
	httppb "google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	thttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	tnethttp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"
	ttcp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/tcp"
	twebsocket "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/websocket"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	productName  types.ProductName = "tt"
	clientTypeTT                   = types.ClientType(productName)
	// Deprecated: Use clientTypeTT instead.
	clientTypeTCP = types.ClientType(productName + "_tcp")
	// Deprecated: Use clientTypeTT instead.
	clientTypeWebSocket = types.ClientType(productName + "_websocket")
	// Deprecated: Use clientTypeTT instead.
	clientTypeGRPC = types.ClientType(productName + "_grpc")
	currentPrefix  = "current_"

	defaultBranch = "master"

	ClientQueueLen     = 100
	ClientIdleTimeout  = 5 * time.Minute
	ClientReadTimeout  = 3 * time.Second
	ClientWriteTimeout = 2 * time.Second
	ClientDialTimeout  = 5 * time.Second

	waitRespTimeout       = 5 * time.Second
	heartbeatInterval     = 3 * time.Minute
	refreshTokenInterval  = 9 * time.Minute
	refreshConfigInterval = 6 * time.Minute

	waitLoginTimeout  = 10 * time.Second
	loginRetryTimeout = 3 * time.Second
)

var (
	_ clienttypes.IClient = (*Client)(nil)
	_ clienttypes.Auth    = (*Client)(nil)

	pushCmds                = []uint32{cmdOfNotify, cmdOfKickOut, cmdOfPush, cmdOfTransmissionPush}
	builtinCallCmds         = []uint32{cmdOfAuth, cmdOfHeartbeat, cmdOfRefreshToken, cmdOfRefreshConfig}
	setClientVersionMethods = []string{methodFullNameOfCppCheckSyncKey, methodFullNameOfGoCheckSyncKey}

	respChPool = &sync.Pool{New: func() any { return make(chan clienttypes.IResponse, 1) }}
)

func ProductName() types.ProductName {
	return productName
}

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v clienttypes.IClient, err error) {
	ci, ai, err := parseCreateClientReq(in)
	if err != nil {
		return nil, errors.Errorf("failed to create %q client, error: %v", productName, err)
	}

	pm, ok := proto.GetProtoManager(productName)
	if !ok {
		return nil, errProtoManagerNotFound
	}

	c := &Client{
		client: &client[ClientInfo]{
			BasicClient: clienttypes.NewBasicClient(context.Background(), opts...),

			createClientInfo: &pb.CreateClientReq{
				Type:         in.GetType(),
				Url:          in.GetUrl(),
				CustomFields: in.GetCustomFields(),
			},
			clientInfo: ci,

			pm: pm,
		},

		authInfo:  ai,
		loginResp: make(map[string]any),
	}
	c.tcpOrWsProtocol = NewProtocol(c)
	c.tcpOrWsPendingRequests = NewTcpOrWsPendingRequests(c)
	defer func() {
		if err != nil && c != nil {
			_ = c.Close()
		}
	}()

	// 创建`transports`
	if err = c.newTransports(); err != nil {
		return nil, err
	}

	// 定时清理超时的请求数据
	threading.GoSafe(c.tcpOrWsPendingRequests.handle)

	return c, nil
}

// parseCreateClientReq 解析创建客户端请求消息
func parseCreateClientReq(in *pb.CreateClientReq) (ci ClientInfo, ai AuthInfo, err error) {
	if in.GetUrl() == "" {
		return ci, ai, errEmptyURL
	}

	ci, err = getClientInfoFromCreateClientReq(in)
	if err != nil {
		return ci, ai, err
	}

	if ci.TcpURL == "" && ci.WebsocketURL == "" && ci.ClientType != ConstClientTypePCLFG {
		return ci, ai, errEmptyTcpOrWsURL
	} else if ci.GrpcURL == "" && ci.ClientType == ConstClientTypePCLFG {
		return ci, ai, errEmptyGrpcURL
	} else if ci.Username == "" {
		return ci, ai, errEmptyUsername
	} else if ci.Password == "" {
		return ci, ai, errEmptyPassword
	}

	md5Password := ci.Password
	if !ci.IsMD5Pwd {
		md5Password = hash.Md5Hex(utils.StringToByteSlice(ci.Password))
	}
	ai = AuthInfo{
		UserName:    ci.Username,
		Password:    ci.Password,
		MD5Password: md5Password,
	}

	return ci, ai, nil
}

func getClientInfoFromCreateClientReq(in *pb.CreateClientReq) (ci ClientInfo, err error) {
	ci = ClientInfo{basicClientInfo: defaultBasicClientInfo()}

	urls := strings.Split(in.GetUrl(), common.ConstAddressSeparator)
	for _, url_ := range urls {
		u, err := url.ParseRequestURI(url_)
		if err != nil {
			return ci, err
		}

		// 服务端地址取第一个
		url__ := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
		if ci.Url == "" {
			ci.Url = url__
		}

		// 用户名和密码取第一个
		if u.User != nil && ci.Username == "" && ci.Password == "" {
			ci.Username = u.User.Username()
			ci.Password, _ = u.User.Password()
		}

		switch u.Scheme {
		case string(constants.TCP), string(constants.GRPC):
			host := u.Host
			if host == "" {
				host = u.Path
			}
			if u.Port() == "" {
				host = fmt.Sprintf("%s:%d", host, common.ConstPort80)
			}

			if u.Scheme == string(constants.TCP) {
				ci.TcpURL = host
			} else {
				ci.GrpcURL = host
			}
		case string(constants.WS), string(constants.WSS):
			ci.WebsocketURL = url__
		case string(constants.HTTP), string(constants.HTTPS):
			ci.HttpURL = url__
		}
	}

	customFields := in.GetCustomFields()
	if customFields != nil {
		decoder, err := mapstructure.NewDecoder(
			&mapstructure.DecoderConfig{
				WeaklyTypedInput: true,
				Result:           &ci,
			},
		)
		if err != nil {
			return ci, err
		}
		if err = decoder.Decode(customFields.AsMap()); err != nil {
			return ci, err
		}
	}

	if ci.ClientType == ConstClientTypePCLFG {
		ci.AppID = ConstAppIDTTPCLFG
	}

	return ci, nil
}

func (c *Client) getUidByCmd(cmd uint32) uint32 {
	if cmd == cmdOfAuth {
		return 0
	}

	return c.authInfo.Uid
}

func (c *Client) deviceID() []byte {
	if c.clientInfo.ClientType == ConstClientTypePCTT {
		return utils.StringToByteSlice(c.authInfo.Account)
	}

	return hash.Md5(utils.StringToByteSlice(c.authInfo.UserName))
}

func (c *Client) newTransports() error {
	if c.clientInfo.TcpURL == "" && c.clientInfo.WebsocketURL == "" && c.clientInfo.ClientType != ConstClientTypePCLFG {
		// 非PC极速版通过`tcp`或者`websocket`进行登录
		return errEmptyTcpOrWsURL
	}
	if c.clientInfo.GrpcURL == "" && c.clientInfo.ClientType == ConstClientTypePCLFG {
		// PC极速版通过`grpc`进行登录
		return errEmptyGrpcURL
	}

	if c.clientInfo.TcpURL != "" {
		c.newTCPTransport()
	}
	if c.clientInfo.WebsocketURL != "" {
		c.newWebsocketTransport()
	}
	if c.clientInfo.GrpcURL != "" {
		c.newGRPCTransport()
	}
	if c.clientInfo.HttpURL != "" {
		c.newHTTPTransport()
	}

	return nil
}

// newTCPTransport 创建`TCP Transport`
func (c *Client) newTCPTransport() {
	if c.clientInfo.TcpURL != "" {
		c.tcpURL = c.clientInfo.TcpURL
	}

	if c.tcpTransport != nil {
		c.Info("tcp transport has been created")
		return
	}

	c.tcpTransport = ttcp.NewClient(
		c.tcpURL, c, ttcp.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  ClientIdleTimeout,
			ReadTimeout:  ClientReadTimeout,
			WriteTimeout: ClientWriteTimeout,
			DialTimeout:  ClientDialTimeout,
		},
	)
}

// newWebsocketTransport 创建`Websocket Transport`
func (c *Client) newWebsocketTransport() {
	if c.clientInfo.WebsocketURL != "" {
		c.wsURL = c.clientInfo.WebsocketURL
	}

	if c.wsTransport != nil {
		c.Info("websocket transport has been created")
		return
	}

	c.wsTransport = twebsocket.NewClient(
		c.wsURL, c, twebsocket.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  ClientIdleTimeout,
			ReadTimeout:  ClientReadTimeout,
			WriteTimeout: ClientWriteTimeout,
			DialTimeout:  ClientDialTimeout,
		},
	)
}

// newGRPCTransport 创建`gRPC Transport`
func (c *Client) newGRPCTransport() {
	if c.clientInfo.GrpcURL != "" {
		c.grpcURL = c.clientInfo.GrpcURL
	}
	if c.clientInfo.GrpcAuthority != "" {
		c.grpcAuthority = c.clientInfo.GrpcAuthority
	}

	afterLogin := false
	if c.refreshInfo != nil && c.refreshInfo.Endpoints != nil {
		afterLogin = true
		if es := c.refreshInfo.Endpoints.Load(); es != nil && len(*es) > 0 {
			endpoint := (*es)[0]
			// `c.clientInfo.GrpcURL` has a higher priority than `endpoint.Address`
			if c.clientInfo.GrpcURL == "" {
				c.grpcURL = endpoint.Address
			}
			// `c.clientInfo.GrpcAuthority` has a higher priority than `endpoint.TlsConfig.Authority`
			if c.clientInfo.GrpcAuthority == "" {
				c.grpcAuthority = endpoint.TlsConfig.Authority
			}
		}
	}

	if c.grpcURL == "" {
		c.Warn("the grpc url and endpoints are both empty")
		return
	} else if !afterLogin && c.grpcAuthority == "" && c.clientInfo.ClientType != ConstClientTypePCLFG {
		c.Debug("the grpc authority is not set, try to create grpc transport after logging in")
		return
	}

	if c.grpcTransport != nil && c.grpcTransport.Load() != nil {
		c.Info("grpc transport has been created")
		return
	}

	c.Debugf("create grpc transport by url and authority, url: %s, authority: %s", c.grpcURL, c.grpcAuthority)
	c.grpcTransport = atomic.NewPointer(tgrpc.NewClient(c.grpcURL, tgrpc.ClientConf{Authority: c.grpcAuthority}))
}

// newHTTPTransport 创建`HTTP Transport`
func (c *Client) newHTTPTransport() {
	if c.clientInfo.HttpURL != "" {
		c.httpBaseURL = c.clientInfo.HttpURL
	}

	if c.httpTransport != nil {
		c.Info("http transport has been created")
		return
	}

	c.httpTransport = tnethttp.NewClient(tnethttp.ClientConf{BaseURL: c.httpBaseURL})
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return types.ClientType(c.createClientInfo.GetType())
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *Client) Environment() string {
	var _url string
	urls := strings.Split(c.createClientInfo.GetUrl(), common.ConstAddressSeparator)
	if len(urls) > 0 && urls[0] != "" {
		u, err := url.Parse(urls[0])
		if err == nil {
			_url = u.Host
		}
	} else if c.clientInfo.TcpURL != "" {
		_url = c.clientInfo.TcpURL
	} else if c.clientInfo.WebsocketURL != "" {
		_url = c.clientInfo.WebsocketURL
	}

	return strings.TrimSuffix(_url, ":"+strconv.Itoa(common.ConstPort80))
}

// Branch 获取客户端使用的分支名称
func (c *Client) Branch() string {
	return c.clientInfo.Branch
}

// GetCreateInfo 获取客户端的创建信息
func (c *Client) GetCreateInfo() *commonpb.CreateInfo {
	return &commonpb.CreateInfo{
		Type:         c.createClientInfo.GetType(),
		Url:          c.createClientInfo.GetUrl(),
		CustomFields: c.createClientInfo.GetCustomFields(),
	}
}

// GetClientInfo 获取客户端基础信息
func (c *Client) GetClientInfo() *commonpb.ClientInfo {
	return &commonpb.ClientInfo{
		Cid:             c.Cid(),
		Nid:             c.NodeID,
		Product:         string(c.ProductName()),
		Type:            c.createClientInfo.GetType(),
		Status:          "", // TODO: 待补充
		CreatedAt:       timestamppb.New(c.CreatedAt()),
		LastCreatedAt:   nil, // TODO: 待补充
		LastRequestedAt: timestamppb.New(c.LastRequestedAt()),
		NumOfRequest:    c.NumOfRequest(),
		CustomInfo:      c.GetCustomInfo(),
		LoginResp:       c.GetLoginResp(),
		UserInfo:        c.GetUserInfo(),
		CreateInfo:      c.GetCreateInfo(),
	}
}

// GetCustomInfo 获取客户端自定义信息
func (c *Client) GetCustomInfo() *structpb.Struct {
	m := c.createClientInfo.GetCustomFields().AsMap()
	if _, ok := m[keyOfBranch]; !ok {
		m[keyOfBranch] = c.clientInfo.Branch
	}
	if _, ok := m[keyOfAccount]; !ok {
		m[keyOfAccount] = c.authInfo.Account
	}
	if _, ok := m[keyOfClientVersion]; !ok {
		m[keyOfClientVersion] = c.clientInfo.ClientVersion
	}

	if c.refreshInfo != nil {
		if c.refreshInfo.UnionToken != nil {
			m[currentPrefix+keyOfUnionToken] = c.refreshInfo.UnionToken.Load()
		}
		if c.refreshInfo.UnionTokenExpiresIn != nil {
			m[currentPrefix+keyOfUnionTokenExpiresIn] = c.refreshInfo.UnionTokenExpiresIn.Load()
		}
		if c.refreshInfo.WebSecureToken != nil {
			m[currentPrefix+keyOfWebSecureToken] = c.refreshInfo.WebSecureToken.Load()
		}
	}

	v, err := protobuf.NewStruct(m)
	if err != nil {
		c.Errorf("failed to create custom info, data: %s, error: %v", jsonx.MarshalIgnoreError(m), err)
	}

	return v
}

// GetLoginResp 获取模拟登录的响应信息
func (c *Client) GetLoginResp() *structpb.Struct {
	v, err := protobuf.NewStruct(c.loginResp)
	if err != nil {
		c.Errorf(
			"failed to new a json object, data: %s, error: %v",
			jsonx.MarshalToStringIgnoreError(c.loginResp), err,
		)
	}

	return v
}

func (c *Client) GetUserInfo() *commonpb.UserInfo {
	return &commonpb.UserInfo{
		Cid: c.Cid(),
		Nid: c.NodeID,
		Uid: c.authInfo.UserName, // 用户名
	}
}

func (c *Client) GetAuthInfo() AuthInfo {
	return c.authInfo
}

func (c *Client) GetSyncKeys() []SyncKey {
	if c.refreshInfo != nil && c.refreshInfo.SyncKeys != nil {
		return *c.refreshInfo.SyncKeys.Load()
	}

	return []SyncKey{}
}

func (c *Client) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.loggedIn {
				_ = c.Logout(context.TODO())
			}

			if c.CancelFunc != nil {
				c.CancelFunc()
			}

			if c.tcpTransport != nil {
				err = es.Join(err, c.tcpTransport.Close())
			}

			if c.wsTransport != nil {
				err = es.Join(err, c.wsTransport.Close())
			}

			if c.grpcTransport != nil {
				if v := c.grpcTransport.Load(); v != nil {
					err = es.Join(err, v.Close())
				}
			}

			if c.httpTransport != nil {
				err = es.Join(err, c.httpTransport.Close())
			}
		},
	)

	return err
}

func (c *Client) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	if thttp.IsHTTPMethod(in.GetMethod()) {
		return c.newHTTPRequest(in)
	} else {
		return c.newTCPOrWSOrGRPCRequest(in)
	}
}

func (c *Client) newTCPOrWSOrGRPCRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	var (
		method       = in.GetMethod()
		headers      = in.GetHeaders()
		customFields = in.GetCustomFields()
		body         []byte

		md protoreflect.MethodDescriptor
		id protoreflect.MessageDescriptor
		od protoreflect.MessageDescriptor

		cmd uint32
	)

	if method == "" {
		return nil, errEmptyMethod
	}

	// TCP or Websocket or gRPC
	md, err = c.pm.FindMethodDescriptorByName(method, protobuf.WithProductBranch(c.clientInfo.Branch))
	if err != nil {
		return nil, err
	}
	id = md.Input()
	od = md.Output()

	lc := GetLogicCommandFromMethodDescriptor(md)
	cmd = lc.ID
	if lc.Deprecated {
		c.Warnf("the method[%s] has been deprecated", method)
	}

	bodyVal := in.GetBody()
	if bodyVal != nil {
		if _, ok := bodyVal.GetKind().(*structpb.Value_NullValue); !ok {
			body, err = protobuf.MarshalJSON(bodyVal) // in.GetBody().MarshalJSON()
			if err != nil {
				return nil, err
			}
		}
	}
	if body == nil {
		body = emptyReqBody
	}

	r := &Request{
		name:    method,
		cmd:     cmd,
		seq:     c.GetSeq(),
		raw:     body,
		branch:  c.Branch(),
		timeout: waitRespTimeout,
		handled: false,
	}

	if c.callByGRPC(cmd, md) {
		return c.newGRPCRequest(r, md, id, od, headers, customFields)
	}

	return c.newTCPOrWSRequest(r, md, id, od, headers, customFields)
}

func (c *Client) newTCPOrWSRequest(
	r *Request, md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
	headers []*httppb.HttpHeader, _ *structpb.Struct,
) (req *TCPOrWSRequest, err error) {
	r.host = c.tcpURL

	// JSON -> PB（bytes）
	data := r.raw
	data = c.tryToSetBaseReq(data)
	data = c.tryToSetClientVersion(md, data)
	r.raw, err = c.pm.UnmarshalPB(string(id.FullName()), data, protobuf.WithProductBranch(c.clientInfo.Branch))
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to unmarshal the request data, method: %s, cmd: %d, seq: %d",
			md.FullName(), r.cmd, r.seq,
		)
	}

	hs := make(map[string]string, len(headers))
	for _, h := range headers {
		hs[h.GetKey()] = h.GetValue()
	}

	if !slices.Contains(builtinCallCmds, r.cmd) {
		body, _ := c.pm.MarshalJSONPB(string(id.FullName()), r.raw)
		c.Infof(
			"the request data of %s, cid: %s, key: %s, headers: %s, body: %s",
			md.FullName(), c.Cid(), c.authInfo.UserName, jsonx.MarshalIgnoreError(hs), body,
		)
	}

	return &TCPOrWSRequest{
		Request:          r,
		methodDescriptor: md,
		inputDescriptor:  id,
		outputDescriptor: od,
		headers:          hs,
		data:             data,
	}, nil
}

func (c *Client) newGRPCRequest(
	r *Request, md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
	headers []*httppb.HttpHeader, customFields *structpb.Struct,
) (req *GRPCRequest, err error) { //nolint:unparam
	r.host = c.grpcURL

	hs := make(metadata.MD, len(headers)+3)
	for _, h := range headers {
		hs.Append(h.GetKey(), h.GetValue())
	}

	req = &GRPCRequest{
		Request:             r,
		methodDescriptor:    md,
		inputDescriptor:     id,
		outputDescriptor:    od,
		headers:             hs,
		data:                r.raw,
		supportCommonStatus: true,
	}

	fields := customFields.GetFields()
	if fields != nil {
		if v, ok := fields[reqHeaderKeyOfXTTSupportCommonStatus]; ok {
			req.supportCommonStatus = cast.ToBool(v.AsInterface())
		}
	}

	callOptions := make([]grpc.CallOption, 0, 1)
	if c.callWithGZIP(req.methodDescriptor) {
		callOptions = append(callOptions, grpc.UseCompressor(gzip.Name))
	}
	req.r = tgrpc.NewRequest(
		req.methodDescriptor, c.grpcRequestSupplier(req), tgrpc.WithCallOptions(callOptions...),
	)

	return req, nil
}

func (c *Client) newHTTPRequest(in *pb.ApiCallReq) (req *HTTPRequest, err error) {
	var (
		_url    = thttp.BuildURL(c.httpBaseURL, in.GetUrl())
		method  = thttp.RequestMethod(in.GetMethod())
		headers = in.GetHeaders()
		body    []byte
	)

	bodyVal := in.GetBody()
	if bodyVal != nil {
		body, err = protobuf.MarshalJSON(bodyVal) // in.GetBody().MarshalJSON()
		if err != nil {
			return nil, err
		}
	}

	r := &Request{
		name:    method,
		seq:     c.GetSeq(),
		raw:     body,
		branch:  c.Branch(),
		timeout: waitRespTimeout,
		handled: false,
	}

	hs := make(tnethttp.Header, len(headers))
	for _, h := range headers {
		hs.Add(h.GetKey(), h.GetValue())
	}

	req = &HTTPRequest{
		Request: r,
		url:     _url,
		method:  method,
		headers: hs,
	}
	req.r = tnethttp.NewRequest(
		tnethttp.SetURL(_url), tnethttp.SetMethod(method), tnethttp.SetHeaders(hs), tnethttp.SetBody(body),
	)

	return req, nil
}

func (c *Client) getPendingKey(cmd, seq uint32) string {
	return fmt.Sprintf("%d:%d", cmd, seq)
}

func (c *Client) keepalive() {
	heartbeatTicker := timewheel.NewTicker(heartbeatInterval)
	defer heartbeatTicker.Stop()

	refreshTokenTicker := timewheel.NewTicker(refreshTokenInterval)
	defer refreshTokenTicker.Stop()

	refreshConfigTicker := timewheel.NewTicker(refreshConfigInterval)
	defer refreshConfigTicker.Stop()

	for {
		select {
		case <-heartbeatTicker.C:
			// 定时发送心跳同步请求
			threading.GoSafe(c.Heartbeat)
		case <-refreshTokenTicker.C:
			// 定时发送刷新Token请求
			threading.GoSafe(c.RefreshToken)
		case <-refreshConfigTicker.C:
			// 定时发送刷新Transport配置请求
			threading.GoSafe(c.RefreshConfig)
		case <-c.Context.Done():
			return
		case <-c.ExitCh:
			return
		}
	}
}

// Login 模拟登录
func (c *Client) Login(ctx context.Context) (err error) {
	defer func() {
		if err != nil {
			_ = c.Close()
		} else {
			c.loggedIn = true
		}
	}()

	var (
		cid = c.Cid()
		key = c.authInfo.UserName

		in *pb.ApiCallReq
	)

	in, err = c.makeLoginReqData()

	c.Infof(
		"the request data of auth login, cid: %s, key: %s, create client info: %s, headers: %s, body: %s",
		cid, key,
		protobuf.MarshalJSONToStringIgnoreError(c.createClientInfo),
		jsonx.MarshalIgnoreError(in.GetHeaders()), protobuf.MarshalJSONIgnoreError(in.GetBody()),
	)

	req, err := c.NewRequest(in)
	if err != nil {
		return errors.Wrapf(err, "failed to create login request, cid: %s, key: %s", cid, key)
	}

	if v, ok := req.(*TCPOrWSRequest); ok {
		v.timeout = waitLoginTimeout
		v.everyRetryTimeout = loginRetryTimeout
	}

	select {
	case <-c.Context.Done():
		err = c.Context.Err()
	default:
		response, err := c.Send(ctx, req)
		if err != nil {
			return errors.Wrapf(err, "failed to send login request, cid: %s, key: %s", cid, key)
		}

		var respBody any
		switch resp := response.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				return resp.err
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				return errors.Errorf(
					"failed to auth login, cid: %s, key: %s, ret: %d(%s), svrRet: %d(%s)",
					cid, key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			}

			respBody = resp.body
		case *GRPCResponse:
			if resp.err != nil {
				return resp.err
			} else if resp.status.Code() != codes.OK {
				return errors.Errorf(
					"failed to auth login, cid: %s, key: %s, code: %d, msg: %s",
					cid, key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				return errors.Errorf(
					"failed to auth login, cid: %s, key: %s, tt_code: %d, tt_msg %s",
					cid, key, resp.ttCode, resp.ttMessage,
				)
			}

			respBody = resp.body
		default:
			return errors.Errorf(
				"unknown the type of auth login response, expected %T or %T, but got %T, cid: %s, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), response, cid, key,
			)
		}

		c.Infof(
			"the response data of auth login, cid: %s, key: %s, data: %s", cid, key, jsonx.MarshalIgnoreError(respBody),
		)

		err = c.parseLoginRespData(respBody)
		if err == nil {
			// 创建GRPC客户端
			c.newGRPCTransport()
			// 创建HTTP客户端
			c.newHTTPTransport()

			// 持续发送心跳
			threading.GoSafe(c.keepalive)
		}
	}

	return err
}

// makeLoginReqData 构造登录请求数据
func (c *Client) makeLoginReqData() (*pb.ApiCallReq, error) {
	var (
		method string
		data   map[string]any
	)

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		method = methodFullNameOfSignIn
		data = map[string]any{
			keyOfPasswordCredential: map[string]any{
				keyOfIdentifier: c.authInfo.UserName,
				keyOfPassword:   c.authInfo.MD5Password,
			},
		}
	} else {
		method = methodFullNameOfAuth
		data = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    uint32(c.clientInfo.AppID),
				keyOfMarketID: uint32(c.clientInfo.MarketID),
			},
			keyOfUserPhone:        c.authInfo.UserName,
			keyOfPwd:              c.authInfo.MD5Password,
			keyOfLoginType:        uint32(ConstLoginTypeManual),
			keyOfLoginAccountType: uint32(ConstLoginAccountTypeTTFuzzy),
		}
		if c.clientInfo.ClientType == ConstClientTypePCTT {
			// 对于PC端，`signature`字段为数字版本号AES加密
			data[keyOfSignature], _ = c.tcpOrWsProtocol.Encrypt(
				utils.StringToByteSlice(strconv.FormatInt(int64(c.clientVersion()), 10)),
				ConstCryptAlgorithmAesDecryptWithPrivateKey,
			)
		}
	}

	headers := security.HandleHTTPPBHeaders(nil)

	body, err := protobuf.NewValue(data)
	if err != nil {
		return nil, err
	}

	return &pb.ApiCallReq{
		Cid:     c.Cid(),
		Method:  method,
		Headers: headers,
		Body:    body,
	}, nil
}

// parseLoginRespData 解析登录响应数据
func (c *Client) parseLoginRespData(data any) error {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName

		ok bool
	)

	c.loginResp, ok = data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid type of auth response data, expected: map[string]any, but got: %T, cid: %s, key: %s",
			data, cid, key,
		)
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		return c.parseSignInRespData()
	} else {
		return c.parseAuthRespData()
	}
}

func (c *Client) parseAuthRespData() error {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	authInfo, ok := c.loginResp[keyOfAuthInfo]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, cid: %s, key: %s, data: %s",
			keyOfAuthInfo, cid, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	var ai AuthInfo
	m, ok := authInfo.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(authInfo, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", ai, cid, key, authInfo,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", ai, cid, key, m,
			)
		}
	}

	ai.UserName = c.authInfo.UserName
	ai.Password = c.authInfo.Password
	ai.MD5Password = c.authInfo.MD5Password
	c.authInfo = ai

	// 设置需要定时刷新的对象
	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}
	c.refreshInfo.UnionToken.Store(c.authInfo.UnionToken)
	c.refreshInfo.UnionTokenExpiresIn.Store(c.authInfo.UnionTokenExpiresIn)
	c.refreshInfo.WebSecureToken.Store(c.authInfo.WebSecureToken)
	c.refreshInfo.SyncKeys.Store(&[]SyncKey{})
	c.refreshInfo.RefreshToken.Store("")
	c.refreshInfo.RefreshTokenExpiresIn.Store(0)

	if ok {
		transportConfig, ok1 := m[keyOfTransportConfigV2]
		if !ok1 {
			c.Warnf(
				"the field[%s] is missing from auth info, cid: %s, key: %s, data: %v",
				keyOfTransportConfigV2, cid, key, m,
			)
			return nil
		}

		c.grpcInfo = GRPCInfo{}
		// 不能使用`mapper.MapperMap`这个方法，因为`GRPCInfo`中存在切片类型字段
		if err := mapstructure.Decode(transportConfig, &c.grpcInfo); err != nil {
			c.Errorf(
				"failed to copy data to struct[%T], cid: %s, key: %s, error: %v",
				c.grpcInfo, cid, key, err,
			)
		}

		// 设置需要定时刷新的对象
		c.refreshInfo.Checksum.Store("")
		c.refreshInfo.GRPCBlackList.Store(&c.grpcInfo.GRPCBlackList)
		c.refreshInfo.GZIPWhiteList.Store(&c.grpcInfo.GZIPWhiteList)
		c.refreshInfo.Endpoints.Store(&c.grpcInfo.Endpoints)
	}

	return nil
}

func (c *Client) parseSignInRespData() error {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	userInfo, ok := c.loginResp[keyOfUserInfo]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, cid: %s, key: %s, data: %s",
			keyOfUserInfo, cid, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	secureTokens, ok := c.loginResp[keyOfSecureTokens]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the login response, cid: %s, key: %s, data: %s",
			keyOfSecureTokens, cid, key, jsonx.MarshalIgnoreError(c.loginResp),
		)
	}

	var ai AuthInfo
	m, ok := userInfo.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(userInfo, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", ai, cid, key, userInfo,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &ai); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", ai, cid, key, m,
			)
		}
	}

	ai.UserName = c.authInfo.UserName
	ai.Password = c.authInfo.Password
	ai.MD5Password = c.authInfo.MD5Password
	c.authInfo = ai

	var sts SecureTokens
	m, ok = secureTokens.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(secureTokens, &sts); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", sts, cid, key, secureTokens,
			)
		}
	} else {
		if err := mapper.MapperMap(m, &sts); err != nil {
			return errors.Wrapf(
				err, "failed to copy data to struct[%T], cid: %s, key: %s, data: %v", sts, cid, key, m,
			)
		}
	}

	// 设置需要定时刷新的对象
	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}
	c.refreshInfo.UnionToken.Store(sts.AccessToken)
	c.refreshInfo.UnionTokenExpiresIn.Store(int64(sts.ExpiresIn))
	c.refreshInfo.WebSecureToken.Store("")
	c.refreshInfo.SyncKeys.Store(&[]SyncKey{})
	c.refreshInfo.RefreshToken.Store(sts.RefreshToken)
	c.refreshInfo.RefreshTokenExpiresIn.Store(int64(sts.RefreshTokenExpiresIn))
	c.refreshInfo.Checksum.Store("")
	c.refreshInfo.GRPCBlackList.Store(&GRPCBlackList{})
	c.refreshInfo.GZIPWhiteList.Store(&GZIPWhiteList{})
	c.refreshInfo.Endpoints.Store(&[]Endpoint{})

	return nil
}

// Logout 模拟登出
func (c *Client) Logout(_ context.Context) error {
	c.Infof("auth logout, cid: %s, key: %s", c.Cid(), c.authInfo.UserName)
	return nil
}

// Heartbeat 心跳
func (c *Client) Heartbeat() {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName

		failure = true
	)
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	in, err := c.makeHeartbeatReqData()
	if err != nil {
		return
	}

	c.Infof(
		"the request data of sync heartbeat, cid: %s, key: %s, headers: %s, body: %s",
		cid, key, jsonx.MarshalIgnoreError(in.GetHeaders()), protobuf.MarshalJSONIgnoreError(in.GetBody()),
	)

	req, err := c.NewRequest(in)
	if err != nil {
		c.Errorf("failed to new heartbeat request, cid: %s, key: %s, error: %v", cid, key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		timeout := req.Timeout()
		if timeout == 0 {
			timeout = waitRespTimeout
		}
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		response, err := c.Send(ctx, req)
		if err != nil {
			c.Errorf("failed to send heartbeat request, cid: %s, key: %s, error: %v", cid, key, err)
			return
		}

		switch resp := response.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of sync heartbeat response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to sync heartbeat, cid: %s, key: %s, ret: %d(%s), svrRet: %d(%s)",
					cid, key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false

				c.Infof(
					"the response data of sync heartbeat, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of sync heartbeat response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to sync heartbeat, cid: %s, key: %s, code: %d, msg: %s",
					cid, key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf(
					"failed to sync heartbeat, cid: %s, key: %s, tt_code: %d, tt_msg: %s",
					cid, key, resp.ttCode, resp.ttMessage,
				)
			} else {
				failure = false

				c.Infof(
					"the response data of sync heartbeat, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		default:
			c.Errorf(
				"unknown the type of sync heartbeat response, expected %T or %T, but got %T, cid: %s, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), response, cid, key,
			)
		}
	}
}

// makeHeartbeatReqData 构造同步心跳请求数据
func (c *Client) makeHeartbeatReqData() (*pb.ApiCallReq, error) {
	data := map[string]any{
		keyOfBaseReq: map[string]any{
			keyOfAppID:    c.clientInfo.AppID,
			keyOfMarketID: c.clientInfo.MarketID,
		},
		keyOfCurrVer:     c.clientInfo.ClientVersion,
		keyOfCurrVerCode: c.clientVersion(),
	}
	if c.clientInfo.ClientType == ConstClientTypePCTT {
		// 对于PC端，`curr_ver`字段为数字版本号AES加密
		data[keyOfCurrVer], _ = c.tcpOrWsProtocol.Encrypt(
			utils.StringToByteSlice(strconv.FormatInt(int64(c.clientVersion()), 10)),
			ConstCryptAlgorithmAesDecryptWithPrivateKey,
		)
	}
	body, err := protobuf.NewValue(data)
	if err != nil {
		return nil, err
	}

	return &pb.ApiCallReq{
		Cid:     c.Cid(),
		Method:  methodFullNameOfHeartbeat,
		Headers: security.HandleHTTPPBHeaders(nil),
		Body:    body,
	}, nil
}

// parseHeartbeatRespData 解析同步心跳响应数据
func (c *Client) parseHeartbeatRespData(data any) error {
	cid := c.Cid()
	key := c.authInfo.UserName

	r, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid heartbeat response data type, expected: map[string]any, but got: %T, cid: %s, key: %s",
			data, cid, key,
		)
	}

	var resp CheckSyncKeyResp
	// 不能使用`mapper.MapperMap`这个方法，因为`CheckSyncKeyResp`中存在切片类型字段
	if err := mapstructure.Decode(r, &resp); err != nil {
		return errors.Errorf(
			"failed to decode the heartbeat response data, cid: %s, key: %s, data: %s, error: %+v",
			cid, key, jsonx.MarshalIgnoreError(r), err,
		)
	}

	if c.refreshInfo == nil {
		c.refreshInfo = newRefreshInfo()
	}

	var oldToken_ string
	if c.refreshInfo.WebSecureToken == nil {
		c.refreshInfo.WebSecureToken = atomic.NewString(resp.WebSecureToken)
	} else {
		oldToken_ = c.refreshInfo.WebSecureToken.Swap(resp.WebSecureToken)
	}
	c.Infof("web secure token, cid: %s, key: %s, old: %s, new: %s", cid, key, oldToken_, resp.WebSecureToken)

	var oldKeys_ []SyncKey
	if c.refreshInfo.SyncKeys == nil {
		c.refreshInfo.SyncKeys = atomic.NewPointer(&resp.SyncKeys)
	} else if old := c.refreshInfo.SyncKeys.Swap(&resp.SyncKeys); old != nil {
		oldKeys_ = *old
	}
	c.Infof(
		"sync key list, cid: %s, key: %s, old: %s, new: %s",
		cid, key, jsonx.MarshalIgnoreError(oldKeys_), jsonx.MarshalIgnoreError(resp.SyncKeys),
	)

	return nil
}

// RefreshToken 刷新Token
func (c *Client) RefreshToken() {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName

		failure = true
	)
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	in, err := c.makeRefreshTokenReqData()
	if err != nil {
		return
	}

	c.Infof(
		"the request data of refresh token, cid: %s, key: %s, headers: %s, body: %s",
		cid, key, jsonx.MarshalIgnoreError(in.GetHeaders()), protobuf.MarshalJSONIgnoreError(in.GetBody()),
	)

	req, err := c.NewRequest(in)
	if err != nil {
		c.Errorf("failed to new refresh token request, cid: %s, key: %s, error: %v", cid, key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		timeout := req.Timeout()
		if timeout == 0 {
			timeout = waitRespTimeout
		}
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		response, err := c.Send(ctx, req)
		if err != nil {
			c.Errorf(
				"failed to send refresh token request, cid: %s, key: %s, error: %v",
				cid, key, err,
			)
			return
		}

		switch resp := response.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of refresh token response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to refresh token, cid: %s, key: %s, ret: %d(%s), svrRet: %d(%s)",
					cid, key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false

				c.Infof(
					"the response data of refresh token, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of refresh token response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to refresh token, cid: %s, key: %s, code: %d, msg: %s",
					cid, key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf(
					"failed to refresh token, cid: %s, key: %s, tt_code: %d, tt_msg: %s",
					cid, key, resp.ttCode, resp.ttMessage,
				)
			} else {
				failure = false

				c.Infof(
					"the response data of refresh token, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		default:
			c.Errorf(
				"unknown the type of refresh token response, expected: %T or %T, but got %T, cid: %s, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), response, cid, key,
			)
		}
	}
}

// makeRefreshTokenReqData 构造刷新Token请求数据
func (c *Client) makeRefreshTokenReqData() (*pb.ApiCallReq, error) {
	var (
		method string
		data   map[string]any
	)

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		method = methodFullNameOfExchangeToken
		data = map[string]any{
			keyOfRefreshToken: c.refreshInfo.RefreshToken.Load(),
		}
	} else {
		method = methodFullNameOfRefreshToken
		data = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    c.clientInfo.AppID,
				keyOfMarketID: c.clientInfo.MarketID,
			},
		}
	}

	body, err := protobuf.NewValue(data)
	if err != nil {
		return nil, err
	}

	return &pb.ApiCallReq{
		Cid:     c.Cid(),
		Method:  method,
		Headers: security.HandleHTTPPBHeaders(nil),
		Body:    body,
	}, nil
}

// parseRefreshTokenRespData 解析刷新Token响应数据
func (c *Client) parseRefreshTokenRespData(data any) error {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	m, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid refresh token response data type, expected: map[string]any, but got: %T, cid: %s, key: %s",
			data, cid, key,
		)
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		c.parseExchangeTokenRespData(m)
	} else {
		c.parseRefreshUnionTokenRespData(m)
	}

	return nil
}

func (c *Client) parseRefreshUnionTokenRespData(data map[string]any) {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	if v, ok := data[keyOfUnionToken]; ok {
		new_ := cast.ToString(v)
		old_ := c.refreshInfo.UnionToken.Swap(new_)
		c.authInfo.UnionToken = new_
		c.Infof("refresh union token, cid: %s, key: %s, old: %s, new: %s", cid, key, old_, new_)
	}
	if v, ok := data[keyOfUnionTokenExpiresIn]; ok {
		// TODO: 待确定，一个是`expires at`，一个是`expires in`，可能需要做时间戳处理
		new_ := cast.ToInt64(v)
		old_ := c.refreshInfo.UnionTokenExpiresIn.Swap(new_)
		c.authInfo.UnionTokenExpiresIn = new_
		c.Infof("refresh union token expires in, cid: %s, key: %s, old: %d, new: %d", cid, key, old_, new_)
	}
}

func (c *Client) parseExchangeTokenRespData(data map[string]any) {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	v, ok := data[keyOfSecureTokens]
	if !ok {
		c.Warnf(
			"the required field[%s] is missing from the refresh token response, cid: %s, key: %s, data: %s",
			keyOfSecureTokens, cid, key, jsonx.MarshalIgnoreError(data),
		)
		return
	}

	var sts SecureTokens
	m, ok := v.(map[string]any)
	if !ok {
		if err := mapstructure.Decode(v, &sts); err != nil {
			c.Errorf("failed to copy data to struct[%T], cid: %s, key: %s, data: %v, error: %+v", sts, cid, key, v, err)
			return
		}
	} else {
		if err := mapper.MapperMap(m, &sts); err != nil {
			c.Errorf("failed to copy data to struct[%T], cid: %s, key: %s, data: %v, error: %+v", sts, cid, key, m, err)
			return
		}
	}

	if sts.AccessToken != "" {
		new_ := sts.AccessToken
		old_ := c.refreshInfo.UnionToken.Swap(new_)
		c.authInfo.UnionToken = new_
		c.Infof("refresh union token, cid: %s, key: %s, old: %s, new: %s", cid, key, old_, new_)
	}
	if sts.ExpiresIn != 0 {
		new_ := sts.ExpiresIn
		old_ := c.refreshInfo.UnionTokenExpiresIn.Swap(int64(new_))
		c.authInfo.UnionTokenExpiresIn = int64(new_)
		c.Infof("refresh union token expires in, cid: %s, key: %s, old: %d, new: %d", cid, key, old_, new_)
	}
	if sts.RefreshToken != "" {
		new_ := sts.RefreshToken
		old_ := c.refreshInfo.RefreshToken.Swap(new_)
		c.Infof("refresh refresh token, cid: %s, key: %s, old: %s, new: %s", cid, key, old_, new_)
	}
	if sts.RefreshTokenExpiresIn != 0 {
		new_ := sts.RefreshTokenExpiresIn
		old_ := c.refreshInfo.RefreshTokenExpiresIn.Swap(int64(new_))
		c.Infof("refresh refresh token expires in, cid: %s, key: %s, old: %d, new: %d", cid, key, old_, new_)
	}
}

// RefreshConfig 刷新配置
func (c *Client) RefreshConfig() {
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		return
	}

	var (
		cid = c.Cid()
		key = c.authInfo.UserName

		failure = true
	)
	defer func() {
		if failure {
			// 失败则增加连续失败次数
			c.AddFailures()
		} else {
			// 成功则重置连续失败次数
			c.ResetFailures()
		}
	}()

	in, err := c.makeRefreshConfigReqData()
	if err != nil {
		return
	}

	c.Infof(
		"the request data of refresh transport config, cid: %s, key: %s, headers: %s, body: %s",
		cid, key, jsonx.MarshalIgnoreError(in.GetHeaders()), protobuf.MarshalJSONIgnoreError(in.GetBody()),
	)

	req, err := c.NewRequest(in)
	if err != nil {
		c.Errorf("failed to new refresh transport config request, cid: %s, key: %s, error: %v", cid, key, err)
		return
	}

	select {
	case <-c.Context.Done():
	case <-c.ExitCh:
	default:
		timeout := req.Timeout()
		if timeout == 0 {
			timeout = waitRespTimeout
		}
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		response, err := c.Send(ctx, req)
		if err != nil {
			c.Errorf(
				"failed to send refresh transport config request, cid: %s, key: %s, error: %v",
				cid, key, err,
			)
			return
		}

		switch resp := response.(type) {
		case *TCPOrWSResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of refresh transport config response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.ret1 != 0 || resp.ret2 != 0 {
				c.Errorf(
					"failed to refresh transport config, cid: %s, key: %s, ret: %d(%s), svrRet: %d(%s)",
					cid, key,
					resp.ret1, MessageFromCode(int(resp.ret1)),
					resp.ret2, MessageFromCode(int(resp.ret2)),
				)
			} else {
				failure = false

				c.Infof(
					"the response data of refresh transport config, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		case *GRPCResponse:
			if resp.err != nil {
				c.Errorf(
					"the error of refresh transport config response, cid: %s, key: %s, error: %v",
					cid, key, resp.err,
				)
			} else if resp.status.Code() != codes.OK {
				c.Errorf(
					"failed to refresh transport config, cid: %s, key: %s, code: %d, msg: %s",
					cid, key, resp.status.Code(), resp.status.Message(),
				)
			} else if resp.ttCode != 0 {
				c.Errorf(
					"failed to refresh transport config, cid: %s, key: %s, tt_code: %d, tt_msg: %s",
					cid, key, resp.ttCode, resp.ttMessage,
				)
			} else {
				failure = false

				c.Infof(
					"the response data of refresh transport config, cid: %s, key: %s, data: %s",
					cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
				)
			}
		default:
			c.Errorf(
				"unknown the type of refresh transport config response, expected %T or %T, but got %T, cid: %s, key: %s",
				(*TCPOrWSResponse)(nil), (*GRPCResponse)(nil), response, cid, key,
			)
		}
	}
}

// makeRefreshConfigReqData 构造刷新配置请求数据
func (c *Client) makeRefreshConfigReqData() (*pb.ApiCallReq, error) {
	var data map[string]any
	if c.refreshInfo == nil || c.refreshInfo.Checksum == nil {
		data = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    c.clientInfo.AppID,
				keyOfMarketID: c.clientInfo.MarketID,
			},
			keyOfPreviousCheckSum: "",
		}
	} else {
		data = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    c.clientInfo.AppID,
				keyOfMarketID: c.clientInfo.MarketID,
			},
			keyOfPreviousCheckSum: c.refreshInfo.Checksum.Load(),
		}
	}
	body, err := protobuf.NewValue(data)
	if err != nil {
		return nil, err
	}

	return &pb.ApiCallReq{
		Cid:     c.Cid(),
		Method:  methodFullNameOfRefreshConfig,
		Headers: security.HandleHTTPPBHeaders(nil),
		Body:    body,
	}, nil
}

// parseRefreshConfigRespData 解析刷新配置响应数据
func (c *Client) parseRefreshConfigRespData(data any) error {
	var (
		cid = c.Cid()
		key = c.authInfo.UserName
	)

	m, ok := data.(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid refresh transport config response data type, expected: map[string]any, but got: %T, cid: %s, key: %s",
			data, cid, key,
		)
	}

	v, ok := m[keyOfNewChecksum]
	if !ok {
		return errors.Errorf(
			"the required field[%s] is missing from the refresh transport config response, cid: %s, key: %s, data: %s",
			keyOfNewChecksum, cid, key, data,
		)
	}

	new_ := cast.ToString(v)
	old_ := c.refreshInfo.Checksum.Swap(new_)
	c.Infof("refresh checksum of transport config, cid: %s, key: %s, old: %s, new: %s", cid, key, old_, new_)
	if strings.EqualFold(old_, new_) {
		c.Infof("checksum values are the same, no need to update transport config, cid: %s, key: %s", cid, key)
		return nil
	}

	if v, ok := m[keyOfTransportConfig]; ok {
		var grpcInfo GRPCInfo
		// 不能使用`mapper.MapperMap`这个方法，因为`GRPCInfo`中存在切片类型字段
		if err := mapstructure.Decode(v, &grpcInfo); err != nil {
			return errors.Errorf(
				"failed to copy data to struct[%T], cid: %s, key: %s, error: %v",
				grpcInfo, cid, key, err,
			)
		}

		if !equal(
			c.refreshInfo.GRPCBlackList.Load().GetApiRules(),
			grpcInfo.GRPCBlackList.ApiRules,
			StringMatchEqualsFn,
			StringMatchHashFn,
		) {
			var oldVal GRPCBlackList
			if old_ := c.refreshInfo.GRPCBlackList.Swap(&grpcInfo.GRPCBlackList); old_ != nil {
				oldVal = *old_
			}
			c.Infof(
				"refresh gRPC black list of transport config, cid: %s, key: %s, old: %s, new: %s",
				cid, key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.GRPCBlackList),
			)
		}

		if !equal(
			c.refreshInfo.GZIPWhiteList.Load().GetApiRules(),
			grpcInfo.GZIPWhiteList.ApiRules,
			StringMatchEqualsFn,
			StringMatchHashFn,
		) {
			var oldVal GZIPWhiteList
			if old_ := c.refreshInfo.GZIPWhiteList.Swap(&grpcInfo.GZIPWhiteList); old_ != nil {
				oldVal = *old_
			}
			c.Infof(
				"refresh gzip white list of transport config, cid: %s, key: %s, old: %s, new: %s",
				cid, key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.GZIPWhiteList),
			)
		}

		if !equal(*c.refreshInfo.Endpoints.Load(), grpcInfo.Endpoints, EndpointEqualsFn, EndpointHashFn) {
			if len(grpcInfo.Endpoints) > 0 {
				var oldVal []Endpoint
				if old_ := c.refreshInfo.Endpoints.Swap(&grpcInfo.Endpoints); old_ != nil {
					oldVal = *old_
				}
				endpoint := grpcInfo.Endpoints[0]

				rebuild := true
				if len(oldVal) > 0 && EndpointEqualsFn(oldVal[0], endpoint) {
					// 第一个`endpoint`一样则不重新创建`gRPC`客户端
					rebuild = false
				}
				if rebuild {
					c.grpcURL = endpoint.Address
					vv := c.grpcTransport.Swap(
						tgrpc.NewClient(
							c.grpcURL, tgrpc.ClientConf{
								Authority: endpoint.TlsConfig.Authority,
							},
						),
					)

					// 可能影响当前执行的请求
					_ = vv.Close()
				}

				c.Infof(
					"refresh endpoints of transport config, cid: %s, key: %s, old: %s, new: %s",
					cid, key, jsonx.MarshalIgnoreError(oldVal), jsonx.MarshalIgnoreError(grpcInfo.Endpoints),
				)
			} else {
				c.Warnf(
					"endpoints of transport config is empty, cid: %s, key: %s, config: %s",
					cid, key, jsonx.MarshalIgnoreError(grpcInfo),
				)
			}
		}
	}

	return nil
}

// Send 发送请求消息
func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	return c.Call(ctx, req, c.send)
}

func (c *Client) send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	cid := c.Cid()
	key := c.authInfo.UserName

	defer func() {
		if err == nil {
			c.afterSend(req, resp)
		}
	}()

	switch v := req.(type) {
	case *TCPOrWSRequest:
		resp, err = c.sendTCPOrWSRequest(ctx, v)
	case *GRPCRequest:
		resp, err = c.sendGRPCRequest(ctx, v)
	case *HTTPRequest:
		resp, err = c.sendHTTPRequest(ctx, v)
	default:
		err = errors.Errorf(
			"invalid request type, expected %T or %T or %T, but got %T, cid: %s, key: %s",
			(*TCPOrWSRequest)(nil), (*GRPCRequest)(nil), (*HTTPRequest)(nil), req, cid, key,
		)
	}

	return resp, err
}

func (c *Client) sendTCPOrWSRequest(ctx context.Context, req *TCPOrWSRequest) (resp *TCPOrWSResponse, err error) {
	cid := c.Cid()
	key := c.authInfo.UserName
	method := req.methodDescriptor.FullName()

	logger := c.Logger.WithContext(ctx)

	if req.cmd == 0 || req.seq == 0 {
		return nil, errors.Errorf(
			"invalid request data, the cmd or seq cannot be zero, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		req.timeout = time.Until(deadline)
		logger.Infof("timeout of the request, cid: %s, key: %s, method: %s, cmd: %d, seq: %d, timeout: %s",
			cid, key, method, req.cmd, req.seq, req.timeout.String(),
		)
	}

	// 根据私有协议对请求进行打包
	data, err := c.tcpOrWsProtocol.PackRequest(ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to pack the request data, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 请求没有响应，则构造空响应来返回
	if req.noResponse {
		return req.newEmptyResponse(), nil
	}

	respCh := respChPool.Get().(chan clienttypes.IResponse)
	defer func() {
		req.responseCh = nil
		respChPool.Put(respCh)
	}()

	// 请求有响应，则缓存请求信息等待响应
	c.tcpOrWsPendingRequests.put(req)

	req.responseCh = respCh
	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	var transport Transport
	if c.tcpTransport != nil {
		transport = c.tcpTransport
	} else if c.wsTransport != nil {
		transport = c.wsTransport
	} else {
		return nil, errEmptyTcpOrWsTransport
	}
	if err = transport.Send(data); err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to send the tcp or websocket request, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, method, req.cmd, req.seq,
		)
	}

	for {
		select {
		case <-ctx.Done():
			req.handled = true // 避免协程继续把响应消息传入响应管道中
			return nil, ctx.Err()
		case r, ok := <-respCh:
			if !ok {
				return nil, errors.Errorf(
					"the response channel has been closed, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
					cid, key, method, req.cmd, req.seq,
				)
			}

			resp, ok = r.(*TCPOrWSResponse)
			if !ok {
				return nil, errors.Errorf(
					"invalid response type, expected: %T, but got: %T, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
					(*TCPOrWSResponse)(nil), r, cid, key, method, req.cmd, req.seq,
				)
			}

			if errors.Is(resp.err, errRetryTimeout) {
				if err = transport.Send(data); err != nil {
					return nil, errors.Wrapf(err, "failed to send the tcp or websocket request")
				}
				logger.Infof(
					"retry to send the request, cid: %s, key: %s, method: %s, cmd: %d, seq: %d, times: %d",
					cid, key, method, req.cmd, req.seq, req.retryTimes,
				)
				continue
			}

			//logger.Infof(
			//	"the response data of %s which is called by tcp or websocket, cid: %s, key: %s, data: %s",
			//	method, cid, key, jsonx.MarshalToStringIgnoreError(resp.body),
			//)

			return resp, nil
		}
	}
}

func (c *Client) sendGRPCRequest(ctx context.Context, req *GRPCRequest) (resp *GRPCResponse, err error) {
	cid := c.Cid()
	key := c.authInfo.UserName

	logger := c.Logger.WithContext(ctx)

	if c.grpcTransport == nil || c.grpcTransport.Load() == nil {
		return nil, errors.Errorf(
			"grpc transport is null, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	} else if req.methodDescriptor.IsStreamingClient() || req.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		req.timeout = time.Until(deadline)
		logger.Infof("timeout of the request, cid: %s, key: %s, method: %s, cmd: %d, seq: %d, timeout: %s",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq, req.timeout.String(),
		)
	}

	// 设置固定的请求头信息
	if c.refreshInfo != nil && c.refreshInfo.UnionToken != nil {
		req.headers.Set(reqHeaderKeyOfAuthorization, fmt.Sprintf("Bearer %s", c.refreshInfo.UnionToken.Load()))
	}
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		req.headers.Set(reqHeaderKeyOfXTTClientBundleID, c.clientInfo.BundleID)
	}
	req.headers.Set(reqHeaderKeyOfXTTMarket, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	req.headers.Set(
		reqHeaderKeyOfXTTClientVersion, strconv.FormatUint(uint64(c.clientVersion()), 10),
	)
	req.headers.Set(reqHeaderKeyOfXTTDeviceID, hex.EncodeToString(c.deviceID()))
	req.headers.Set(reqHeaderKeyOfXTTTerminalType, strconv.FormatUint(uint64(c.terminalType()), 10))
	if req.supportCommonStatus {
		req.headers.Set(reqHeaderKeyOfXTTSupportCommonStatus, "true")
	}
	if spanCtx := trace.SpanContextFromContext(ctx); spanCtx.IsValid() {
		traceID := spanCtx.TraceID().String()
		// req.headers.Set(reqHeaderKeyOfRequestID, traceID)
		req.headers.Set(reqHeaderKeyOfXTTRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" {
		req.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	commonStatusMessage, err := c.pm.CreateMessage(messageFullNameOfCommonStatus)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to create the message, cid: %s, key: %s, name: %s",
			cid, key, messageFullNameOfCommonStatus,
		)
	}
	defer c.pm.PutMessage(commonStatusMessage)

	resp = &GRPCResponse{
		Response: &Response{
			Logger: logger,
			ctx:    ctx,

			cmd:    req.cmd,
			seq:    req.seq,
			branch: req.branch,
		},
		methodDescriptor:    req.methodDescriptor,
		outputDescriptor:    req.outputDescriptor,
		supportCommonStatus: req.supportCommonStatus,
		commonStatusMessage: commonStatusMessage,
	}
	resp.r = tgrpc.NewResponse(req.methodDescriptor, resp)

	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	timeout := waitRespTimeout
	if req.timeout > 0 {
		timeout = req.timeout
	}
	invokeCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	found := false
	if c.grpcTransport != nil {
		if v := c.grpcTransport.Load(); v != nil {
			found = true

			err = v.InvokeRPC(invokeCtx, req.r, req.headers, resp.r)
			err = resp.handle(err)
			if err != nil && !errors.Is(err, resp.err) {
				//logger.Errorf(
				//	"failed to invoke rpc, cid: %s, key: %s, method: %s, cmd: %d, seq: %d, error: %+v",
				//	cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq, err,
				//)
				resp.err = err
			}
			resp.elapsed = time.Since(req.startedAt)

			//logger.Infof(
			//	"the response data of %s which is invoked by grpc, cid: %s, key: %s, data: %s",
			//	req.methodDescriptor.FullName(), cid, key, jsonx.MarshalIgnoreError(resp.body),
			//)
		}
	}
	if !found {
		return nil, errors.Errorf(
			"grpc transport is null, cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
			cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	return resp, nil
}

func (c *Client) grpcRequestSupplier(req *GRPCRequest) tgrpc.RequestSupplier {
	cid := c.Cid()
	key := c.authInfo.UserName

	return func(msg *dynamicpb.Message) error {
		// JSON -> PB Message
		req.data = c.tryToSetBaseReq(req.data)
		req.data = c.tryToSetClientVersion(req.methodDescriptor, req.data)
		if err := c.pm.UnmarshalMessage(msg, req.data); err != nil {
			return errors.Wrapf(
				err,
				"failed to unmarshal request data to message[%s], cid: %s, key: %s, method: %s, cmd: %d, seq: %d",
				req.inputDescriptor.FullName(), cid, key, req.methodDescriptor.FullName(), req.cmd, req.seq,
			)
		}

		//c.Infof(
		//	"the request data of %s, cid: %s, key: %s, headers: %s, body: %s",
		//	req.methodDescriptor.FullName(), cid, key, jsonx.MarshalIgnoreError(req.headers),
		//	protobuf.MarshalJSONIgnoreError(msg),
		//)

		return io.EOF
	}
}

func (c *Client) sendHTTPRequest(ctx context.Context, req *HTTPRequest) (resp *HTTPResponse, err error) {
	cid := c.Cid()
	key := c.authInfo.UserName

	logger := logx.WithContext(ctx)

	if c.httpTransport == nil {
		return nil, errors.Errorf(
			"http transport is null, cid: %s, key: %s, url: %s, method: %s, seq: %d",
			cid, key, req.url, req.method, req.seq,
		)
	} else if req.url == "" || req.seq == 0 {
		return nil, errors.Errorf("invalid request data, the url cannot be empty or seq cannot be zero, cid: %s", cid)
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		req.timeout = time.Until(deadline)
	}

	//logger.Infof(
	//	"the request data of %s, cid: %s, key: %s, headers: %s, body: %s",
	//	req.url, cid, key, jsonx.MarshalIgnoreError(req.headers), req.raw,
	//)

	resp = &HTTPResponse{
		Response: &Response{
			Logger: logger,
			ctx:    ctx,

			method: req.method,
			cmd:    req.cmd,
			seq:    req.seq,
			branch: req.branch,
		},
		url: req.url,
	}

	// 设置固定的请求头信息
	if c.refreshInfo != nil && c.refreshInfo.UnionToken != nil {
		req.r.Header.Set(reqHeaderKeyOfAuthorization, fmt.Sprintf("Bearer %s", c.refreshInfo.UnionToken.Load()))
	}
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		req.headers.Set(reqHeaderKeyOfXTTClientBundleID, c.clientInfo.BundleID)
	}
	req.r.Header.Set(reqHeaderKeyOfXTTMarket, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	req.r.Header.Set(
		reqHeaderKeyOfXTTClientVersion, strconv.FormatInt(int64(c.clientVersion()), 10),
	)
	req.headers.Set(reqHeaderKeyOfXTTDeviceID, hex.EncodeToString(c.deviceID()))
	req.headers.Set(reqHeaderKeyOfXTTTerminalType, strconv.FormatUint(uint64(c.terminalType()), 10))
	if spanCtx := trace.SpanContextFromContext(ctx); spanCtx.IsValid() {
		traceID := spanCtx.TraceID().String()
		// req.headers.Set(reqHeaderKeyOfRequestID, traceID)
		req.headers.Set(reqHeaderKeyOfXTTRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" {
		req.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}
	req.r = req.r.WithContext(ctx)
	timeout := waitRespTimeout
	if req.timeout > 0 {
		timeout = req.timeout
	}
	resp.r, err = c.httpTransport.Send(req.r, timeout) //nolint:bodyclose
	if err != nil {
		resp.err = err

		if resp.r != nil {
			_ = resp.r.Body.Close()
		}
	} else {
		resp.handle()
	}
	resp.elapsed = time.Since(req.startedAt)

	//logger.Infof(
	//	"the response data of %s which is called by http, cid: %s, key: %s, data: %s",
	//	req.url, cid, key, jsonx.MarshalIgnoreError(resp.body),
	//)

	return resp, nil
}

func (c *Client) afterSend(req clienttypes.IRequest, resp clienttypes.IResponse) {
	cid := c.Cid()
	key := c.authInfo.UserName

	switch v1 := req.(type) {
	case *TCPOrWSRequest:
		if v2, ok := resp.(*TCPOrWSResponse); ok && v2.ret1 == 0 && v2.ret2 == 0 {
			c.afterSendTCPOrWSRequest(v1, v2)
		}
	case *GRPCRequest:
		if v2, ok := resp.(*GRPCResponse); ok && v2.err == nil && v2.status.Code() == codes.OK && v2.ttCode == 0 {
			c.afterSendGRPCRequest(v1, v2)
		}
	case *HTTPRequest:
		if v2, ok := resp.(*HTTPResponse); ok {
			c.afterSendHTTPRequest(v1, v2)
		}
	default:
		c.Warnf(
			"unknown request type, expected %T or %T or %T, but got %T, cid: %s, key: %s",
			(*TCPOrWSRequest)(nil), (*GRPCRequest)(nil), (*HTTPRequest)(nil), req, cid, key,
		)
	}
}

func (c *Client) afterSendTCPOrWSRequest(req *TCPOrWSRequest, resp *TCPOrWSResponse) {
	var err error

	switch req.methodDescriptor.FullName() {
	case methodFullNameOfHeartbeat:
		err = c.parseHeartbeatRespData(resp.body)
	case methodFullNameOfRefreshToken:
		err = c.parseRefreshTokenRespData(resp.body)
	case methodFullNameOfRefreshConfig:
		err = c.parseRefreshConfigRespData(resp.body)
	}

	if err != nil {
		c.Errorf(
			"failed to handle the request and response after send a request, method: %s, data: %v",
			req.methodDescriptor.FullName(), resp.body,
		)
	}
}

func (c *Client) afterSendGRPCRequest(req *GRPCRequest, resp *GRPCResponse) {
	var err error

	switch req.methodDescriptor.FullName() {
	case methodFullNameOfHeartbeat:
		err = c.parseHeartbeatRespData(resp.body)
	case methodFullNameOfRefreshToken, methodFullNameOfExchangeToken:
		err = c.parseRefreshTokenRespData(resp.body)
	case methodFullNameOfRefreshConfig:
		err = c.parseRefreshConfigRespData(resp.body)
	}

	if err != nil {
		c.Errorf(
			"failed to handle the request and response after send a request, method: %s, data: %v",
			req.methodDescriptor.FullName(), resp.body,
		)
	}
}

func (c *Client) afterSendHTTPRequest(_ *HTTPRequest, _ *HTTPResponse) {
}

// Recv 接收数据
func (c *Client) Recv(pkg []byte) {
	cid := c.Cid()
	key := c.authInfo.UserName

	resp, err := c.tcpOrWsProtocol.UnpackResponse(pkg)
	if err != nil {
		c.Errorf("failed to unpack the response, cid: %s, key: %s, error: %+v", cid, key, err)
		return
	} else if resp.isPush {
		if resp.cmd == cmdOfKickOut {
			c.Warnf("receive a kick out message, cid: %s, key: %s, cmd: %d, seq: %d", cid, key, resp.cmd, resp.seq)
			_ = c.Close()
		} else {
			c.Debugf("receive a push message, cid: %s, key: %s, cmd: %d, seq: %d", cid, key, resp.cmd, resp.seq)
		}
		return
	}

	req := c.tcpOrWsPendingRequests.getAndRemove(resp)
	if req != nil {
		resp.branch = req.branch
		resp.outputDescriptor = req.outputDescriptor
		resp.elapsed = time.Since(req.startedAt)

		if (resp.ret1 != 0 || resp.ret2 != 0) && len(resp._data) == 0 {
			resp.err = errors.Errorf(
				"the result code of response is not success, cid: %s, key:%s, ret: %d(%s), svrRet: %d(%s)",
				cid, key,
				resp.ret1, MessageFromCode(int(resp.ret1)),
				resp.ret2, MessageFromCode(int(resp.ret2)),
			)
		} else {
			pm, ok := proto.GetProtoManager(productName)
			if !ok {
				if resp.ret1 != 0 || resp.ret2 != 0 {
					resp.err = errors.Errorf(
						"can not to unmarshal the response cause by proto manager not found, cid: %s, key: %s, product name: %s, ret: %d(%s), svrRet: %d(%s), data: %s",
						cid, key, productName,
						resp.ret1, MessageFromCode(int(resp.ret1)),
						resp.ret2, MessageFromCode(int(resp.ret2)),
						resp._data,
					)
				} else {
					dm := dynamicpb.NewMessage(resp.outputDescriptor)
					_ = protobuf.UnmarshalMessage(resp._data, dm)
					resp.body, resp.err = ConvertRequestIDWithDynamicMessage(dm)
				}
			} else {
				output := string(req.outputDescriptor.FullName())
				if resp.ret1 != 0 || resp.ret2 != 0 {
					output = messageFullNameOfOnlyBaseResp
				}
				resp.body, resp.err = ConvertRequestIDWithMessageAndData(pm, c.clientInfo.Branch, output, resp._data)
			}
		}

		req.handle(resp)
	} else {
		c.Warnf(
			"the request corresponding to the response could not be found, cid: %s, key: %s, cmd: %d, seq: %d, ret: %d(%s), svrRet: %d(%s)",
			cid, key, resp.cmd, resp.seq,
			resp.ret1, MessageFromCode(int(resp.ret1)),
			resp.ret2, MessageFromCode(int(resp.ret2)),
		)
	}
}

// ParsePackage 解析数据包
func (c *Client) ParsePackage(buf []byte) (int, int) {
	if size := NewPacketParser(buf).IsComplete(); size != 0 {
		return int(size), ttcp.PackageFull
	}

	return 0, ttcp.PackageLess
}

func (c *Client) callByGRPC(cmd uint32, md protoreflect.MethodDescriptor) bool {
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		// PC极速版走`gRPC`的接口
		return true
	}
	if c.createClientInfo.GetType() == string(clientTypeTCP) || // 客户端类型为`tt_tcp`
		(c.clientInfo.ClientType != ConstClientTypePCLFG && (cmd == cmdOfAuth || cmd == cmdOfHeartbeat || cmd == cmdOfRefreshToken)) || // 非PC极速版指定走`TCP`的接口
		c.grpcTransport == nil || c.grpcTransport.Load() == nil {
		return false
	}

	method := requestMethod(md)

	if c.refreshInfo != nil && c.refreshInfo.GRPCBlackList != nil {
		for _, rule := range c.refreshInfo.GRPCBlackList.Load().GetApiRules() {
			switch rule.MatchType {
			case ConstStringMatchTypeExact, ConstStringMatchTypeAll:
				if method == rule.MatchValue {
					return false
				}
			case ConstStringMatchTypePrefix:
				if strings.HasPrefix(method, rule.MatchValue) {
					return false
				}
			default:
				continue
			}
		}
	}

	return true
}

func (c *Client) callWithGZIP(md protoreflect.MethodDescriptor) bool {
	method := requestMethod(md)

	if c.refreshInfo != nil && c.refreshInfo.GZIPWhiteList != nil {
		for _, rule := range c.refreshInfo.GZIPWhiteList.Load().GetApiRules() {
			switch rule.MatchType {
			case ConstStringMatchTypeExact, ConstStringMatchTypeAll:
				if method == rule.MatchValue {
					return true
				}
			case ConstStringMatchTypePrefix:
				if strings.HasPrefix(method, rule.MatchValue) {
					return true
				}
			default:
				continue
			}
		}
	}

	return false
}
