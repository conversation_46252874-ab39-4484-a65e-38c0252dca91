package tt

import "testing"

func TestGetClientVersionByString(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "Fake - 123",
			args: args{
				s: "123",
			},
			want: 123,
		},
		{
			name: "App - 6.34.0",
			args: args{
				s: "6.34.0",
			},
			want: 102891520,
		},
		{
			name: "App - 6.38.0",
			args: args{
				s: "6.38.0",
			},
			want: 103153664,
		},
		{
			name: "App - 6.52.5",
			args: args{
				s: "6.52.5",
			},
			want: 104071173,
		},
		{
			name: "PC - 2.0.8",
			args: args{
				s: "2.0.8",
			},
			want: 33554440,
		},
		{
			name: "App - 6.58.3",
			args: args{
				s: "6.58.3",
			},
			want: 104464387,
		},
		{
			name: "PC LFG - 1.1.7",
			args: args{
				s: "1.1.7",
			},
			want: 16842759,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetClientVersionByString(tt.args.s); got != tt.want {
					t.Errorf("GetClientVersionByString() = %v, want %v", got, tt.want)
				} else {
					t.Logf("GetClientVersionByString() = %d", got)
				}
			},
		)
	}
}

func TestGetClientVersionByInteger(t *testing.T) {
	type args struct {
		u uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "default client version",
			args: args{
				u: defaultClientVersion,
			},
			want: "6.58.3",
		},
		{
			name: "App - 6.34.0",
			args: args{
				u: 102891520,
			},
			want: "6.34.0",
		},
		{
			name: "App - 6.38.0",
			args: args{
				u: 103153664,
			},
			want: "6.38.0",
		},
		{
			name: "PC - 2.0.8",
			args: args{
				u: 33554440,
			},
			want: "2.0.8",
		},
		{
			name: "App - 6.58.3",
			args: args{
				u: 104464387,
			},
			want: "6.58.3",
		},
		{
			name: "PC LFG - 1.1.7",
			args: args{
				u: 16842759,
			},
			want: "1.1.7",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetClientVersionByInteger(tt.args.u); got != tt.want {
					t.Errorf("GetClientVersionByInteger() = %v, want %v", got, tt.want)
				} else {
					t.Logf("GetClientVersionByInteger() = %s", got)
				}
			},
		)
	}
}
