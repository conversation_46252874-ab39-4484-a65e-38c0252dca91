package tt

import (
	"encoding/hex"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

type (
	ServiceOptions struct {
		Name       string `json:"logic_service_name"`
		Language   string `json:"logic_service_language"`
		URIRewrite string `json:"logic_service_uri_rewrite"`
	}
	MethodOptions struct {
		ID              uint32 `json:"id"`
		Deprecated      bool   `json:"deprecated"`
		RewriteFullPath string `json:"rewrite_full_path"`
	}
	LogicOptions struct {
		ServiceOptions ServiceOptions
		MethodOptions  MethodOptions

		RequestMethod       string
		LogicMethod         string
		LogicMethodFullName string
	}
)

func GetLogicOptions(md protoreflect.MethodDescriptor) LogicOptions {
	sd, _ := md.Parent().(protoreflect.ServiceDescriptor)
	rm := requestMethod(md)
	opts := LogicOptions{
		ServiceOptions: getServiceOptions(sd),
		MethodOptions:  getMethodOptions(md),

		RequestMethod:       rm,
		LogicMethod:         rm,
		LogicMethodFullName: string(md.FullName()),
	}
	if opts.ServiceOptions.URIRewrite != "" {
		serviceURI := opts.ServiceOptions.URIRewrite
		if serviceURI[0] == '/' {
			serviceURI = serviceURI[1:]
		}
		if serviceURI[len(serviceURI)-1] == '/' {
			serviceURI = serviceURI[:len(serviceURI)-1]
		}

		opts.LogicMethod = fmt.Sprintf("/%s/%s", serviceURI, md.Name())
		opts.LogicMethodFullName = fmt.Sprintf("%s.%s", serviceURI, md.Name())
	}

	return opts
}

func getServiceOptions(sd protoreflect.ServiceDescriptor) (opts ServiceOptions) {
	if sd != nil {
		sd.Options().ProtoReflect().Range(
			func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
				if fd.IsExtension() && fd.Kind() == protoreflect.StringKind {
					switch fd.FullName() {
					case extensionFieldFullNameOfLogicServiceName:
						opts.Name = v.String()
					case extensionFieldFullNameOfLogicServiceLanguage:
						opts.Language = v.String()
					case extensionFieldFullNameOfLogicServiceURIRewrite:
						opts.URIRewrite = v.String()
					}

					return opts.Name == "" || opts.Language == "" || opts.URIRewrite == ""
				}

				return true // 继续遍历
			},
		)
	}

	return opts
}

func getMethodOptions(md protoreflect.MethodDescriptor) (opts MethodOptions) {
	if md != nil {
		md.Options().ProtoReflect().Range(
			func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
				if fd.IsExtension() && fd.Kind() == protoreflect.MessageKind && fd.FullName() == extensionFullNameOfCommand {
					bs, _ := protobuf.MarshalJSON(v.Message().Interface())
					_ = jsonx.Unmarshal(bs, &opts)

					return false // 跳出遍历
				}

				return true // 继续遍历
			},
		)
	}

	return opts
}

type LogicCommand struct {
	ID         uint32 `json:"id"`
	Deprecated bool   `json:"deprecated"`
}

func GetLogicCommandFromMethodDescriptor(md protoreflect.MethodDescriptor) (command LogicCommand) {
	if md != nil {
		md.Options().ProtoReflect().Range(
			func(fd protoreflect.FieldDescriptor, v protoreflect.Value) bool {
				if fd.IsExtension() && fd.Kind() == protoreflect.MessageKind && fd.FullName() == extensionFullNameOfCommand {
					bs, _ := protobuf.MarshalJSON(v.Message().Interface())
					_ = jsonx.Unmarshal(bs, &command)

					return false // 跳出遍历
				}

				return true // 继续遍历
			},
		)
	}

	return command
}

func ConvertRequestIDWithMessageAndData(pm *protobuf.ProtoManager, branch, name string, data []byte) (any, error) {
	m, err := pm.CreateMessage(name, protobuf.WithProductBranch(branch))
	if err != nil {
		return nil, errors.Errorf("failed to create message, name: %s, branch: %s, error: %+v", name, branch, err)
	} else if m == nil {
		return nil, errors.Errorf("protobuf message not found, name: %s, branch: %s", name, branch)
	}
	defer pm.PutMessage(m, protobuf.WithProductBranch(branch))

	if err := protobuf.UnmarshalMessage(data, m); err != nil {
		return nil, errors.Errorf(
			"failed to unmarshal byte data to message, name: %s, branch: %s, data: %s, error: %v",
			name, branch, hex.EncodeToString(data), err,
		)
	}

	return ConvertRequestIDWithDynamicMessage(m)
}

func ConvertRequestIDWithDynamicMessage(m *dynamicpb.Message) (any, error) {
	r := protobuf.ParseOptions{
		UseProtoNames:  true,
		UseEnumNumbers: false,
	}.ParseMessage(m)
	v, ok := r[keyOfBaseResp]
	if !ok {
		return r, nil
	}

	sm, ok := v.(map[string]any)
	if !ok {
		return r, nil
	}

	v, ok = sm[keyOfRequestID]
	if !ok {
		return r, nil
	}

	rid, ok := v.([]byte)
	if !ok {
		return r, nil
	}

	sm[keyOfRequestID] = string(rid)

	// return jsonx.Marshal(r)
	return r, nil
}

func requestMethod(md protoreflect.MethodDescriptor) string {
	sd, _ := md.Parent().(protoreflect.ServiceDescriptor)
	return fmt.Sprintf("/%s/%s", sd.FullName(), md.Name())
}

func equal[T comparable](old_, new_ []T, equals generic.EqualsFn[T], hash generic.HashFn[T]) bool {
	_old := set.NewHashset(uint64(len(old_)), equals, hash, old_...)
	_new := set.NewHashset(uint64(len(new_)), equals, hash, new_...)

	return _old.Equal(_new)
}

// xValues returns the value of a byte as a hexadecimal digit or 255.
var xValues = [256]byte{
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 255, 255, 255, 255, 255, 255,
	255, 10, 11, 12, 13, 14, 15, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 10, 11, 12, 13, 14, 15, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
	255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
}

// hexToByte converts hex characters x1 and x2 into a byte.
func hexToByte(x1, x2 byte) (byte, bool) {
	b1 := xValues[x1]
	b2 := xValues[x2]
	return (b1 << 4) | b2, b1 != 255 && b2 != 255
}

func encodeString32ToByte16(src string) (dst [16]byte, err error) {
	if len(src) != 32 {
		return dst, errors.Errorf("invalid string length: %d", len(src))
	}

	var ok bool
	for i := range dst {
		dst[i], ok = hexToByte(src[i*2], src[i*2+1])
		if !ok {
			return dst, errors.Errorf("invalid string format: %s", src)
		}
	}

	return dst, nil
}
