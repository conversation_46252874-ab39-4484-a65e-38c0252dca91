package tt

import (
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/registry"
)

func Init() {
	registry.RegisterClientFunc(clientTypeTT, NewClient)           // 注册`TT`客户端
	registry.RegisterClientFunc(clientTypeTCP, NewClient)          // 注册`TT TCP`客户端
	registry.RegisterClientFunc(clientTypeWebSocket, NewClient)    // 注册`TT Websocket`客户端
	registry.RegisterClientFunc(clientTypeGRPC, NewClient)         // 注册`TT gRPC`客户端
	registry.RegisterClientFunc(clientTypeLogicTT, NewLogicClient) // 注册`TT Logic`客户端
}
