package emojiLogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type EmojiLogic struct {
	*clienttypes.CommonLogic

	ctx context.Context
	c   clienttypes.IClient
}

func (l *EmojiLogic) InternalGetEmojiPkgList() (body map[string]any, err error) {
	reqBody, err := protobuf.NewValue(
		map[string]any{
			emojiReqKeyOfStartIndex: 0,
			emojiReqKeyOfCount:      defaultCount,
		},
	)
	if err != nil {
		return nil, err
	}

	resp, err := l.<PERSON>(
		&pb.ApiCallReq{
			Cid:    l.c.Cid(),
			Method: methodFullNameOfGetEmojiPkgList,
			Body:   reqBody,
		},
	)
	if err != nil {
		return nil, err
	} else if resp.Error() != nil {
		return nil, resp.Error()
	}

	body, ok := resp.Body().(map[string]any)
	if !ok {
		return nil, errors.Errorf("invalid type of response body, expectd map[string]any, but got %T", resp.Body())
	}

	return body, nil
}

func (l *EmojiLogic) InternalGetEmojiListByPkg() (body map[string]any, err error) {
	reqBody, err := protobuf.NewValue(
		map[string]any{
			emojiReqKeyOfPackageID:  "",
			emojiReqKeyOfStartIndex: 0,
			emojiReqKeyOfCount:      defaultCount,
		},
	)
	if err != nil {
		return nil, err
	}

	resp, err := l.RunHandler(
		&pb.ApiCallReq{
			Cid:    l.c.Cid(),
			Method: methodFullNameOfGetEmojiListByPkg,
			Body:   reqBody,
		},
	)
	if err != nil {
		return nil, err
	} else if resp.Error() != nil {
		return nil, resp.Error()
	}

	body, ok := resp.Body().(map[string]any)
	if !ok {
		return nil, errors.Errorf("invalid type of response body, expectd map[string]any, but got %T", resp.Body())
	}

	return body, nil
}
