package imLogic

const (
	methodFullNameOfSendMsg = "ga.api.im.ImCppLogic.SendMsg"

	messageFullNameOfEmojiMsg = "ga.im.EmojiMsg"

	defaultEmojiHeightPx = 100
	defaultEmojiWidthPx  = 100

	emojiDownloadURLFormat = "https://appcdn.52tt.com/emoji/download/%s?imageView2/0/w/%d/h/%d"

	emojiMsgKeyOfID     = "id"
	emojiMsgKeyOfURL    = "url"
	emojiMsgKeyOfHeight = "height"
	emojiMsgKeyOfWidth  = "width"

	sendMsgReqKeyOfTargetName    = "target_name"
	sendMsgReqKeyOfType          = "type"
	sendMsgReqKeyOfContent       = "content"
	sendMsgReqKeyOfClientMsgID   = "client_msg_id"
	sendMsgReqKeyOfClientMsgTime = "client_msg_time"
	sendMsgReqKeyOfHasAttachment = "has_attachment"
	sendMsgReqKeyOfMyLoginKey    = "my_login_key"
	sendMsgReqKeyOfExt           = "ext"
)

type IMMsgType uint32

const (
	IMMsgTypeTextMsg                 IMMsgType = iota + 1 // 文本
	IMMsgTypeImgMsg                                       // 图片
	IMMsgTypeVoiceMsg                                     // 语音
	IMMsgTypeVideoMsg                                     // 视频
	IMMsgTypeExpressionMsg           IMMsgType = 9        // 表情包表情消息
	IMMsgTypeGuildAssistantJoinGuild IMMsgType = 17       // 公会消息助手 -- 入会申请
	IMMsgTypeGuildAssistantJoinGroup IMMsgType = 18       // 公会消息助手 -- 入群申请
	IMMsgTypeAtSomeoneMsg            IMMsgType = 34       // 群聊@xxx的消息类型
	IMMsgTypeCustomEmoticonMsg       IMMsgType = 39       // 自定义表情
	IMMsgTypeCueMsg                  IMMsgType = 63
	IMMsgTypeSendPlayTogether        IMMsgType = 64 // 发送一起进房消息
	IMMsgTypeAcceptPlayTogether      IMMsgType = 65 // 接受一起进房的邀请
)
