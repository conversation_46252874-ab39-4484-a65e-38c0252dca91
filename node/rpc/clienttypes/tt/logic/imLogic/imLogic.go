package imLogic

import (
	"context"
	"fmt"
	"time"

	"github.com/devfeel/mapper"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
)

type IMLogic struct {
	*clienttypes.CommonLogic

	ctx context.Context
	c   *tt.Client
}

func NewIMLogic(ctx context.Context, c clienttypes.IClient) (clienttypes.ILogic, error) {
	_c, ok := c.(*tt.Client)
	if !ok {
		return nil, errors.Errorf("invalid client type, expected: *tt.Client, but got: %T", c)
	}

	return &IMLogic{
		CommonLogic: clienttypes.NewCommonLogic(ctx, c),

		ctx: ctx,
		c:   _c,
	}, nil
}

func (l *IMLogic) SendEmoji(in *pb.ApiCallReq) (out *pb.ApiCallResp, err error) {
	resp, err := l.RunHandler(
		in,
		clienttypes.WithReqMethodHandler(l.sendMsgReqMethodHandler),
		clienttypes.WithReqBodyHandler(l.sendEmojiReqBodyHandler),
	)
	if err != nil {
		return nil, err
	}

	return l.TeardownHandler(in, resp)
}

func (l *IMLogic) sendMsgReqMethodHandler(_ string) (string, error) {
	return methodFullNameOfSendMsg, nil
}

func (l *IMLogic) sendEmojiReqBodyHandler(in *structpb.Value) (out *structpb.Value, err error) {
	value := in.GetStructValue()
	if value == nil {
		return nil, errEmptyReqBody
	}

	var sendEmojiReq SendEmojiReq
	reqBody := value.AsMap()
	if err = mapper.MapperMap(reqBody, &sendEmojiReq); err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to copy data to struct[%T], data: %s",
			sendEmojiReq, jsonx.MarshalToStringIgnoreError(reqBody),
		)
	}

	if sendEmojiReq.Target == "" {
		return nil, errEmptyTarget
	}
	if sendEmojiReq.EmojiID == "" {
		return nil, errEmptyEmojiID
	}
	if sendEmojiReq.URL == "" {
		sendEmojiReq.URL = fmt.Sprintf(
			emojiDownloadURLFormat, sendEmojiReq.EmojiID, sendEmojiReq.Width, sendEmojiReq.Height,
		)
	}

	pm, ok := proto.GetProtoManager(l.c.ProductName())
	if !ok {
		return nil, errors.Errorf("the proto manager not found, product name: %s", l.c.ProductName())
	}

	emojiMsg := map[string]any{
		emojiMsgKeyOfID:     sendEmojiReq.EmojiID,
		emojiMsgKeyOfURL:    sendEmojiReq.URL,
		emojiMsgKeyOfHeight: sendEmojiReq.Height,
		emojiMsgKeyOfWidth:  sendEmojiReq.Width,
	}

	bs := jsonx.MarshalIgnoreError(emojiMsg)
	emojiMsgExt, err := pm.UnmarshalPB(messageFullNameOfEmojiMsg, bs, protobuf.WithProductBranch(l.c.Branch()))
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to unmarshal data to message, data: %s, message: %s",
			bs, messageFullNameOfEmojiMsg,
		)
	}

	ai := l.c.GetAuthInfo()

	data := map[string]any{
		sendMsgReqKeyOfTargetName:    sendEmojiReq.Target,
		sendMsgReqKeyOfType:          IMMsgTypeCustomEmoticonMsg,
		sendMsgReqKeyOfContent:       "",
		sendMsgReqKeyOfClientMsgID:   ai.IMSyncKey + 1,
		sendMsgReqKeyOfClientMsgTime: time.Now().Second(),
		sendMsgReqKeyOfHasAttachment: false,
		sendMsgReqKeyOfMyLoginKey:    ai.LoginKey,
		sendMsgReqKeyOfExt:           emojiMsgExt,
	}

	out, err = protobuf.NewValue(data)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to new a *structpb.Value, data: %s",
			jsonx.MarshalToStringIgnoreError(data),
		)
	}

	return out, nil
}
