package logic

import (
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/registry"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt/logic/imLogic"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt/logic/syncLogic"
)

func Init() {
	registry.RegisterLogicFunc(tt.ProductName(), imLogic.Name, imLogic.NewIMLogic)       // 注册`IMLogic`
	registry.RegisterLogicFunc(tt.ProductName(), syncLogic.Name, syncLogic.NewSyncLogic) // 注册`SyncLogic`
}
