package syncLogic

const (
	methodFullNameOfCheckSyncKey = "ga.api.sync.SyncGoLogic.CheckSyncKey"
	methodFullNameOfSync         = "ga.api.sync.SyncLogic.Sync"

	messageFullNameOfNewMessageSync              = "ga.sync.NewMessageSync"
	messageFullNameOfContactSync                 = "ga.sync.ContactSync"
	messageFullNameOfNewContactSync              = "ga.sync.NewContactSync"
	messageFullNameOfMyInfoSync                  = "ga.sync.MyInfoSync"
	messageFullNameOfGuildDetailSync             = "ga.sync.GuildDetailSync"
	messageFullNameOfGuildMemSync                = "ga.sync.GuildMemSync"
	messageFullNameOfGuildGroupSync              = "ga.sync.GuildGroupSync"
	messageFullNameOfGuildChangeSync             = "ga.sync.GuildChangeSync"
	messageFullNameOfImGuildGroupMemSync         = "ga.sync.ImGuildGroupMemSync"
	messageFullNameOfGrowInfoSync                = "ga.sync.GrowInfoSync"
	messageFullNameOfMissionSync                 = "ga.sync.MissionSync"
	messageFullNameOfTGroupInfoSync              = "ga.sync.TGroupInfoSync"
	messageFullNameOfGuildDetailSyncV2           = "ga.sync.GuildDetailSyncV2"
	messageFullNameOfGuildAdminsSync             = "ga.sync.GuildAdminsSync"
	messageFullNameOfGuildCheckInSyncV2          = "ga.sync.GuildCheckInSyncV2"
	messageFullNameOfGuildDonateSync             = "ga.sync.GuildDonateSync"
	messageFullNameOfMemberGuildTitleSync        = "ga.sync.MemberGuildTitleSync"
	messageFullNameOfGuildMemberContributionSync = "ga.sync.GuildMemberContributionSync"
	messageFullNameOfSessionSync                 = "ga.sync.SessionSync"
	messageFullNameOfWelfareSync                 = "ga.sync.WelfareSync"
	messageFullNameOfBestGameSync                = "ga.sync.BestGameSync"
	messageFullNameOfNewGameSync                 = "ga.sync.NewGameSync"
	messageFullNameOfHotGameSync                 = "ga.sync.HotGameSync"
	messageFullNameOfAdvancedConfigPresentSync   = "ga.sync.AdvancedConfigPresentSync"
	messageFullNameOfInteractiveInfoUpdate       = "ga.ugc.InteractiveInfoUpdate"
	messageFullNameOfFollowingListUpdate         = "ga.ugc.FollowingListUpdate"

	syncReqKeyOfType       = "type"
	syncReqKeyOfMsgSyncKey = "msg_sync_key"
	syncReqKeyOfIMSyncType = "im_sync_type"
	syncRespKeyOfContent   = "content"
	syncRespKeyOfCMD       = "cmd"
)

type IMSyncType uint32

const (
	IMSyncTypeAll             IMSyncType = iota
	IMSyncTypeIMWithoutPush              // 1V1和临时群的消息（没有主动推送的）
	IMSyncTypeIMGroupWithPush            // 公会群、群组（有主动推送的）
)

type CMDType uint32

const (
	CMDTypeIMMsg        CMDType = iota + 1 // IM_MSG
	CMDTypeContact                         // CONTACT
	CMDTypeNewContact                      // CONTACT
	CMDTypeMyInfo                          // CONTACT
	CMDTypeGuildInfo                       // GUILD
	CMDTypeGuildMemList                    // GUILD

	CMDTypeGuildCheckin              CMDType = 11  // 工会签到同步(GUILD)
	CMDTypeGuildGroup                CMDType = 12  // 工会群同步(GUILD)
	CMDTypeGuildChange               CMDType = 13  // 用户工会有变更(GUILD)
	CMDTypeIMGuildGroupMem           CMDType = 15  // 群成员信息变化(IM_MSG)
	CMDTypeGrowInfo                  CMDType = 18  // 成长体系 - 个人信息(经验值, 红钻值)(GROW)
	CMDTypeGrowMission               CMDType = 19  // 成长体系 - 任务消息(GROW)
	CMDTypeTGroupInfo                CMDType = 23  // 群组同步
	CMDTypeGuildInfoV2               CMDType = 24  // 公会基本信息 V2... 拆分了的
	CMDTypeGuildAdminsInfo           CMDType = 25  // 公会管理员同步
	CMDTypeGuildCheckinV2            CMDType = 26  // 公会签到信息，为了兼容，新版就不发旧的GUILD_CHECKIN了
	CMDTypeGuildDonate               CMDType = 30  // 公会捐献信息
	CMDTypeGuildMemberTitle          CMDType = 31  // 成员的公会称号
	CMDTypeMemberContribution        CMDType = 32  // 成员贡献
	CMDTypeSession                   CMDType = 201 // 实时语音
	CMDTypeGameTabWelfare            CMDType = 250 // 福利活动
	CMDTypeGameTabBestGame           CMDType = 251 // 精品专区
	CMDTypeGameTabNewGame            CMDType = 252 // 新游榜
	CMDTypeGameTabHotGame            CMDType = 253 // 热游榜
	CMDTypeAdvancedConfigUserPresent CMDType = 500 // 礼物配置
	CMDTypeUGCInteractiveInfoUpdate  CMDType = 600 // 互动信息更新
	CMDTypeUGCFollowingListUpdate    CMDType = 601 // 关注列表更新
)
