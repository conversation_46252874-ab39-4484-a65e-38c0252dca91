package syncLogic

import (
	"context"
	"encoding/hex"

	"github.com/devfeel/mapper"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
)

type SyncLogic struct {
	*clienttypes.CommonLogic

	ctx context.Context
	c   *tt.Client
}

func NewSyncLogic(ctx context.Context, c clienttypes.IClient) (clienttypes.ILogic, error) {
	_c, ok := c.(*tt.Client)
	if !ok {
		return nil, errors.Errorf("invalid client type, expected: *tt.Client, but got: %T", c)
	}

	return &SyncLogic{
		CommonLogic: clienttypes.NewCommonLogic(ctx, c),

		ctx: ctx,
		c:   _c,
	}, nil
}

func (l *SyncLogic) Sync(in *pb.ApiCallReq) (out *pb.ApiCallResp, err error) {
	resp, err := l.RunHandler(
		in,
		clienttypes.WithReqMethodHandler(l.syncReqMethodHandler),
		clienttypes.WithReqBodyHandler(l.syncReqBodyHandler),
	)
	if err != nil {
		return nil, err
	}

	return l.TeardownHandler(
		in,
		resp,
		clienttypes.WithRespBodyHandler(l.syncRespBodyHandler),
	)
}

func (l *SyncLogic) checkSyncKey() (body map[string]any, err error) {
	resp, err := l.RunHandler(
		&pb.ApiCallReq{
			Cid:    l.c.Cid(),
			Method: methodFullNameOfCheckSyncKey,
			Body:   structpb.NewNullValue(),
		},
	)
	if err != nil {
		return nil, err
	} else if resp.Error() != nil {
		return nil, resp.Error()
	}

	body, ok := resp.Body().(map[string]any)
	if !ok {
		return nil, errors.Errorf("invalid type of response body, expectd map[string]any, but got %T", resp.Body())
	}

	return body, nil
}

func (l *SyncLogic) getSyncKey(syncType SyncType) (syncKey uint32, err error) {
	defer func() {
		if err == nil && syncKey > 10 {
			// 默认取最近10条记录
			syncKey = syncKey - 10
		}
	}()

	syncKeys := l.c.GetSyncKeys()
	if len(syncKeys) == 0 {
		m, err := l.checkSyncKey()
		if err != nil {
			return syncKey, err
		}

		var resp CheckSyncKeyResp
		// 不能使用`mapper.MapperMap`这个方法，因为`CheckSyncKeyResp`中存在切片类型字段
		if err = mapstructure.Decode(m, &resp); err != nil {
			return syncKey, err
		}

		syncKeys = resp.SyncKeys
	}

	for _, item := range syncKeys {
		if item.SyncType == syncType {
			syncKey = item.SyncKey
			break
		}
	}

	return syncKey, nil
}

func (l *SyncLogic) syncReqMethodHandler(_ string) (string, error) {
	return methodFullNameOfSync, nil
}

func (l *SyncLogic) syncReqBodyHandler(in *structpb.Value) (out *structpb.Value, err error) {
	value := in.GetStructValue()
	if value == nil {
		return nil, errEmptyReqBody
	}

	var syncReq SyncReq
	reqBody := value.AsMap()
	// 不能使用`mapstructure.Decode`，因为`reqBody.SyncType`为字符串，不能转为`SyncReq.SyncType`
	if err = mapper.MapperMap(reqBody, &syncReq); err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to copy data to struct[%T], data: %s",
			syncReq, jsonx.MarshalToStringIgnoreError(reqBody),
		)
	}

	if syncReq.SyncKey == 0 {
		syncReq.SyncKey, err = l.getSyncKey(syncReq.SyncType)
		if err != nil {
			return nil, err
		}
	}

	data := map[string]any{
		syncReqKeyOfType:       syncReq.SyncType,
		syncReqKeyOfMsgSyncKey: syncReq.SyncKey,
	}
	if syncReq.SyncType == tt.SyncTypeIMMsg || syncReq.SyncType == tt.SyncTypeIMMsgV2 {
		data[syncReqKeyOfIMSyncType] = syncReq.IMSyncType
	}

	out, err = protobuf.NewValue(data)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to new a *structpb.Value, data: %s",
			jsonx.MarshalToStringIgnoreError(data),
		)
	}

	return out, nil
}

func (l *SyncLogic) syncRespBodyHandler(in any) (out *structpb.Value, err error) {
	defer func() {
		out, err = protobuf.NewValue(in)
	}()

	respBody, ok := in.(map[string]any)
	if !ok {
		l.Errorf("the type of response[%T] is not map[string]any", in)
		return out, err
	}

	v, ok := respBody[syncRespKeyOfContent]
	if !ok {
		l.Errorf(
			"the key %q is not in the response, response: %s",
			syncRespKeyOfContent, jsonx.MarshalToStringIgnoreError(respBody),
		)
		return out, err
	}

	messages, ok := v.([]any)
	if !ok {
		l.Errorf("the type of key %q[%T] is not []any", syncRespKeyOfContent, v)
		return out, err
	}

	pm, ok := proto.GetProtoManager(l.c.ProductName())
	if !ok {
		l.Errorf("the proto manager not found, product name: %s", l.c.ProductName())
		return out, err
	}

	for _, message := range messages {
		mm, ok := message.(map[string]any)
		if !ok {
			continue
		}

		v, ok = mm[syncRespKeyOfCMD]
		if !ok {
			l.Errorf("the key %q is not in the ga.sync.SyncMessage", syncRespKeyOfCMD)
			continue
		}

		cmd, ok := v.(uint32)
		if !ok {
			l.Errorf("the type of key %q[%T] is not uint32", syncRespKeyOfCMD, v)
			continue
		}

		v, ok = mm[syncRespKeyOfContent]
		if !ok {
			l.Errorf("the key %q is not in the ga.sync.SyncMessage", syncRespKeyOfContent)
			continue
		}

		content, ok := v.([]byte)
		if !ok {
			l.Errorf("the type of key %q[%T] is not []byte", syncRespKeyOfContent, v)
			continue
		}

		name, ok := syncMessages[CMDType(cmd)]
		if !ok {
			l.Errorf("the cmd of sync message is not supported, cmd: %d", cmd)
			continue
		}

		branch := l.c.Branch()
		msg, err := pm.CreateMessage(name, protobuf.WithProductBranch(branch))
		if err != nil {
			return nil, errors.Errorf(
				"failed to create protobuf message, name: %s, branch: %s, cmd: %d, error: %+v",
				name, branch, cmd, err,
			)
		} else if msg == nil {
			return nil, errors.Errorf(
				"protobuf message not found, name: %s, branch: %s, cmd: %d",
				name, branch, cmd,
			)
		}

		if err = protobuf.UnmarshalMessage(content, msg); err != nil {
			return nil, errors.Errorf(
				"failed to unmarshal the content, message: %s, cmd: %d, content: %s, error: %v",
				name, cmd, hex.EncodeToString(content), err,
			)
		}

		mm[syncRespKeyOfContent] = protobuf.ParseOptions{
			UseProtoNames:  true,
			UseEnumNumbers: false,
		}.ParseMessage(msg)

		pm.PutMessage(msg, protobuf.WithProductBranch(branch))
	}

	return out, err
}
