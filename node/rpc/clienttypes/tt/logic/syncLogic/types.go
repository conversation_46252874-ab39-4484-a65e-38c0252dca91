package syncLogic

import "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"

type (
	SyncType         = tt.SyncType
	SyncKey          = tt.SyncKey
	CheckSyncKeyResp = tt.CheckSyncKeyResp
)

type (
	SyncReq struct {
		SyncType   SyncType   `json:"sync_type" mapstructure:"sync_type"`
		SyncKey    uint32     `json:"sync_key,omitempty,optional,default=0" mapstructure:"sync_key,omitempty"`
		IMSyncType IMSyncType `json:"im_sync_type,omitempty,optional,default=0" mapstructure:"im_sync_type,omitempty"`
	}
	// Deprecated: use map[string]any instead.
	SyncMessage struct {
		CMD       CMDType `json:"cmd" mapstructure:"cmd"`
		Content   any     `json:"content" mapstructure:"content"`
		ExtraFlag uint32  `json:"extra_flag,omitempty,optional" mapstructure:"extra_flag,omitempty"`
	}
	// Deprecated: use map[string]any instead.
	SyncResp struct {
		Contents []*SyncMessage `json:"content" mapstructure:"content"`
	}
)
