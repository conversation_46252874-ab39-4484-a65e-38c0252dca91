package syncLogic

import (
	"reflect"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

var (
	Name = types.LogicName(reflect.TypeOf((*SyncLogic)(nil)).Elem().Name())

	errEmptyReqBody = errors.New("the body of request cannot be null")

	syncMessages = map[CMDType]string{
		CMDTypeIMMsg:                     messageFullNameOfNewMessageSync,
		CMDTypeContact:                   messageFullNameOfContactSync,
		CMDTypeNewContact:                messageFullNameOfNewContactSync,
		CMDTypeMyInfo:                    messageFullNameOfMyInfoSync,
		CMDTypeGuildInfo:                 messageFullNameOfGuildDetailSync,
		CMDTypeGuildMemList:              messageFullNameOfGuildMemSync,
		CMDTypeGuildGroup:                messageFullNameOfGuildGroupSync,
		CMDTypeGuildChange:               messageFullNameOfGuildChangeSync,
		CMDTypeIMGuildGroupMem:           messageFullNameOfImGuildGroupMemSync,
		CMDTypeGrowInfo:                  messageFullNameOfGrowInfoSync,
		CMDTypeGrowMission:               messageFullNameOfMissionSync,
		CMDTypeTGroupInfo:                messageFullNameOfTGroupInfoSync,
		CMDTypeGuildInfoV2:               messageFullNameOfGuildDetailSyncV2,
		CMDTypeGuildAdminsInfo:           messageFullNameOfGuildAdminsSync,
		CMDTypeGuildCheckinV2:            messageFullNameOfGuildCheckInSyncV2,
		CMDTypeGuildDonate:               messageFullNameOfGuildDonateSync,
		CMDTypeGuildMemberTitle:          messageFullNameOfMemberGuildTitleSync,
		CMDTypeMemberContribution:        messageFullNameOfGuildMemberContributionSync,
		CMDTypeSession:                   messageFullNameOfSessionSync,
		CMDTypeGameTabWelfare:            messageFullNameOfWelfareSync,
		CMDTypeGameTabBestGame:           messageFullNameOfBestGameSync,
		CMDTypeGameTabNewGame:            messageFullNameOfNewGameSync,
		CMDTypeGameTabHotGame:            messageFullNameOfHotGameSync,
		CMDTypeAdvancedConfigUserPresent: messageFullNameOfAdvancedConfigPresentSync,
		CMDTypeUGCInteractiveInfoUpdate:  messageFullNameOfInteractiveInfoUpdate,
		CMDTypeUGCFollowingListUpdate:    messageFullNameOfFollowingListUpdate,
	}
)
