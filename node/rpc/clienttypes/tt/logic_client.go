package tt

import (
	"context"
	"encoding/hex"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"time"

	"github.com/devfeel/mapper"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/utils"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/dynamicpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/grpcpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	clientTypeLogicTT = types.ClientType(productName + "_logic")
)

var (
	_ clienttypes.IClient = (*LogicClient)(nil)

	errWrongScheme = errors.Errorf("the scheme of url must be %s", constants.GRPC)
	errEmptyTarget = errors.New("the url cannot be empty while creating grpc request")

	grpcPool = grpcpool.NewGRPCPool()
)

func NewLogicClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v clienttypes.IClient, err error) {
	ci, err := parseCreateLogicClientReq(in)
	if err != nil {
		return nil, errors.Errorf("failed to create %q client, error: %v", productName, err)
	}

	pm, ok := proto.GetProtoManager(productName)
	if !ok {
		return nil, errProtoManagerNotFound
	}

	c := &LogicClient{
		client: &client[LogicClientInfo]{
			BasicClient: clienttypes.NewBasicClient(context.Background(), opts...),

			createClientInfo: &pb.CreateClientReq{
				Type:         in.GetType(),
				Url:          in.GetUrl(),
				CustomFields: in.GetCustomFields(),
			},
			clientInfo: ci,

			pm: pm,
		},
	}

	return c, nil
}

func parseCreateLogicClientReq(in *pb.CreateClientReq) (ci LogicClientInfo, err error) {
	ci = LogicClientInfo{basicClientInfo: defaultBasicClientInfo()}

	if in.GetUrl() != "" {
		ci.Url = in.GetUrl()

		u, err := url.ParseRequestURI(in.GetUrl())
		if err != nil {
			return ci, err
		}

		if u.Scheme != string(constants.GRPC) {
			return ci, errWrongScheme
		}

		host := u.Host
		if host == "" {
			host = u.Path
		}
		if u.Port() == "" {
			host = fmt.Sprintf("%s:%d", host, common.ConstPort80)
		}
		ci.grpcInfo.Target = host
	}

	customFields := in.GetCustomFields()
	if customFields != nil {
		decoder, err := mapstructure.NewDecoder(
			&mapstructure.DecoderConfig{
				WeaklyTypedInput: true,
				Result:           &ci,
			},
		)
		if err != nil {
			return ci, err
		}
		if err = decoder.Decode(customFields.AsMap()); err != nil {
			return ci, err
		}
		//if err = mapper.MapperMap(customFields.AsMap(), &ci); err != nil {
		//	return ci, err
		//}
	}

	return ci, nil
}

func (c *LogicClient) deviceID() []byte {
	return hash.Md5(utils.StringToByteSlice(c.clientInfo.Uid))
}

// ProductName 获取客户端的产品名称
func (c *LogicClient) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *LogicClient) ClientType() types.ClientType {
	return types.ClientType(c.createClientInfo.GetType())
}

// GetCreateInfo 获取客户端的创建信息
func (c *LogicClient) GetCreateInfo() *commonpb.CreateInfo {
	return &commonpb.CreateInfo{
		Type:         c.createClientInfo.GetType(),
		Url:          c.createClientInfo.GetUrl(),
		CustomFields: c.createClientInfo.GetCustomFields(),
	}
}

// GetClientInfo 获取客户端基础信息
func (c *LogicClient) GetClientInfo() *commonpb.ClientInfo {
	return &commonpb.ClientInfo{
		Cid:             c.Cid(),
		Nid:             c.NodeID,
		Product:         string(c.ProductName()),
		Type:            c.createClientInfo.GetType(),
		Status:          "", // TODO: 待补充
		CreatedAt:       timestamppb.New(c.CreatedAt()),
		LastCreatedAt:   nil, // TODO: 待补充
		LastRequestedAt: timestamppb.New(c.LastRequestedAt()),
		NumOfRequest:    c.NumOfRequest(),
		CustomInfo:      c.GetCustomInfo(),
		LoginResp:       nil,
		UserInfo:        c.GetUserInfo(),
		CreateInfo:      c.GetCreateInfo(),
	}
}

// GetCustomInfo 获取客户端自定义信息
func (c *LogicClient) GetCustomInfo() *structpb.Struct {
	m := c.createClientInfo.GetCustomFields().AsMap()
	if _, ok := m[keyOfBranch]; !ok {
		m[keyOfBranch] = c.clientInfo.Branch
	}
	if _, ok := m[keyOfClientVersion]; !ok {
		m[keyOfClientVersion] = c.clientInfo.ClientVersion
	}

	v, err := protobuf.NewStruct(m)
	if err != nil {
		c.Errorf("failed to create custom info, data: %s, error: %+v", jsonx.MarshalIgnoreError(m), err)
	}

	return v
}

func (c *LogicClient) GetUserInfo() *commonpb.UserInfo {
	return &commonpb.UserInfo{
		Cid: c.Cid(),
		Nid: c.NodeID,
		Uid: "",
	}
}

func (c *LogicClient) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.CancelFunc != nil {
				c.CancelFunc()
			}
		},
	)

	return err
}

func (c *LogicClient) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	var (
		method       = in.GetMethod()
		headers      = in.GetHeaders()
		customFields = in.GetCustomFields()

		setNoTLS bool
		info     grpcInfo
		conf     tgrpc.ClientConf
		body     []byte
	)

	defer func() {
		if err == nil {
			grpcPool.Register(info.Target, conf)
		}
	}()

	if customFields != nil && len(customFields.GetFields()) != 0 {
		m := customFields.AsMap()
		if err = mapper.MapperMap(m, &info); err != nil {
			c.Errorf("failed to parse custom fields, custom fields: %s, error: %+v", jsonx.MarshalIgnoreError(m), err)
		} else if _, ok := m[keyOfNoTLS]; ok {
			setNoTLS = true
		}
	}
	if info.Target == "" {
		info.Target = c.clientInfo.Target
	}
	if info.Authority == "" {
		info.Authority = c.clientInfo.Authority
	}
	if info.UserAgent == "" {
		info.UserAgent = c.clientInfo.UserAgent
	}
	if !setNoTLS {
		info.NoTLS = c.clientInfo.NoTLS
	}
	conf = tgrpc.ClientConf{
		Authority: info.Authority,
		UserAgent: info.UserAgent,
		NoTLS:     info.NoTLS,
	}

	if info.Target == "" {
		return nil, errEmptyTarget
	}
	if method == "" {
		return nil, errEmptyMethod
	}

	md, err := c.pm.FindMethodDescriptorByName(method, protobuf.WithProductBranch(c.clientInfo.Branch))
	if err != nil {
		return nil, err
	}

	bodyVal := in.GetBody()
	if bodyVal != nil {
		if _, ok := bodyVal.GetKind().(*structpb.Value_NullValue); !ok {
			body, err = protobuf.MarshalJSON(bodyVal) // in.GetBody().MarshalJSON()
			if err != nil {
				return nil, err
			}
		}
	}
	if body == nil {
		body = emptyReqBody
	}

	hs := make(metadata.MD, len(headers)+3)
	for _, h := range headers {
		hs.Append(h.GetKey(), h.GetValue())
	}

	lo := GetLogicOptions(md)
	r := &GRPCRequest{
		Request: &Request{
			name:    lo.LogicMethodFullName,
			seq:     c.GetSeq(),
			raw:     body,
			host:    info.Target,
			branch:  c.clientInfo.Branch,
			timeout: waitRespTimeout,
		},

		methodDescriptor: md,
		inputDescriptor:  md.Input(),
		outputDescriptor: md.Output(),
		headers:          hs,

		c: conf,
	}
	r.r = tgrpc.NewRequest(
		md, c.grpcRequestSupplier(r),
		tgrpc.WithMethod(lo.LogicMethod),
		tgrpc.WithCallOptions(grpc.UseCompressor(gzip.Name)),
	)

	return r, nil
}

func (c *LogicClient) grpcRequestSupplier(req *GRPCRequest) tgrpc.RequestSupplier {
	cid := c.Cid()

	return func(msg *dynamicpb.Message) error {
		// JSON -> PB Message
		req.raw = c.tryToSetBaseReq(req.raw)
		req.raw = c.tryToSetClientVersion(req.methodDescriptor, req.raw)
		if err := c.pm.UnmarshalMessage(msg, req.raw); err != nil {
			return errors.Wrapf(
				err,
				"failed to unmarshal request data to message[%s], cid: %s, method: %s, cmd: %d, seq: %d",
				req.inputDescriptor.FullName(), cid, req.name, req.cmd, req.seq,
			)
		}

		c.Infof(
			"the request data of %s, cid: %s, headers: %s, body: %s",
			req.name, cid, jsonx.MarshalIgnoreError(req.headers), protobuf.MarshalJSONIgnoreError(msg),
		)

		return io.EOF
	}
}

func (c *LogicClient) Send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	return c.Call(ctx, req, c.send)
}

func (c *LogicClient) send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	cid := c.Cid()
	logger := logx.WithContext(ctx)

	v, ok := req.(*GRPCRequest)
	if !ok {
		return nil, errors.Errorf("invalid request type, expected %T, but got %T, cid: %s", (*Request)(nil), req, cid)
	}

	transport, err := grpcPool.Get(v.host, v.c)
	if err != nil {
		return nil, err
	} else if transport == nil {
		return nil, errors.Errorf("grpc transport is null, cid: %s, method: %s, seq: %d", cid, v.name, v.seq)
	} else if v.methodDescriptor.IsStreamingClient() || v.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, cid: %s, method: %s, seq: %d",
			cid, v.name, v.seq,
		)
	}

	// 使用上下文的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		v.timeout = time.Until(deadline)
	}

	if c.clientInfo.Uid != "" && len(v.headers.Get(reqHeaderKeyOfUID)) == 0 {
		v.headers.Set(reqHeaderKeyOfUID, c.clientInfo.Uid)
	}
	v.headers.Set(reqHeaderKeyOfMarketID, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	v.headers.Set(reqHeaderKeyOfClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	v.headers.Set(
		reqHeaderKeyOfClientVersion,
		strconv.FormatUint(uint64(c.clientVersion()), 10),
	)
	if c.clientInfo.Uid != "" && len(v.headers.Get(reqHeaderKeyOfDeviceID)) == 0 {
		v.headers.Set(reqHeaderKeyOfDeviceID, hex.EncodeToString(c.deviceID()))
	}
	v.headers.Set(reqHeaderKeyOfTerm, strconv.FormatUint(uint64(c.terminalType()), 10))
	if spanCtx := trace.SpanContextFromContext(ctx); spanCtx.IsValid() {
		traceID := spanCtx.TraceID().String()
		v.headers.Set(reqHeaderKeyOfRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" && len(v.headers.Get(reqHeaderKeyOfXQWTrafficMark)) == 0 {
		v.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	r := &GRPCResponse{
		Response: &Response{
			cmd:    v.cmd,
			seq:    v.seq,
			branch: v.branch,
		},
		methodDescriptor: v.methodDescriptor,
		outputDescriptor: v.outputDescriptor,
	}
	r.r = tgrpc.NewResponse(v.methodDescriptor, r)

	if v.startedAt.IsZero() {
		v.startedAt = time.Now()
	}

	timeout := waitRespTimeout
	if v.timeout > 0 {
		timeout = v.timeout
	}
	invokeCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	err = transport.InvokeRPC(invokeCtx, v.r, v.headers, r.r)
	if err != nil && !errors.Is(err, r.err) {
		logger.Errorf(
			"failed to invoke rpc, cid: %s, method: %s, cmd: %d, seq: %d, error: %+v",
			cid, v.name, v.cmd, v.seq, err,
		)
		r.err = err
	}
	r.elapsed = time.Since(v.startedAt)

	logger.Infof(
		"the response data of %s which is invoked by grpc, cid: %s, data: %s",
		v.name, cid, jsonx.MarshalIgnoreError(r.body),
	)

	return r, nil
}
