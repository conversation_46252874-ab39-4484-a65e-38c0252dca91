package tt

import (
	"bytes"
	"encoding/binary"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

var bufferpool = utils.NewPool()

type Packet struct {
	ph        *PacketHeader
	svrPkgLen uint32
	sph       *ServicePacketHeader
	body      []byte // 请求时的数据（已压缩、已加密），响应时的数据（未解密、未解压）
	data      []byte // 整个`packet`的二进制数据
}

func NewPacket(ph *PacketHeader, sph *ServicePacketHeader, body, tail []byte) *Packet {
	buf := new(bytes.Buffer)

	// svr_pkg_len = service_packet_header_len + body_len + tail_len
	svrPkgLen := uint32(sph.HeadLen) + sph.BodyLen + uint32(sph.TailLen)
	// packet_header | svr_pkg_len | service_packet_header | body
	ph.Write(buf)
	_ = binary.Write(buf, binary.BigEndian, svrPkgLen)
	sph.Write(buf)
	buf.Write(body)
	buf.Write(tail)

	p := &Packet{
		ph:        ph,
		svrPkgLen: svrPkgLen,
		sph:       sph,
		body:      body,
		data:      buf.Bytes(),
	}

	return p
}

func (p *Packet) PacketHeader() PacketHeader {
	return *p.ph
}

func (p *Packet) SvrPkgLen() uint32 {
	return p.svrPkgLen
}

func (p *Packet) ServicePacketHeader() ServicePacketHeader {
	return *p.sph
}

func (p *Packet) Size() uint32 {
	return p.ph.PackageLength
}

// Body 不含头部的请求或响应数据
// 注：请求数据是已压缩和加密的数据，响应数据是未解密和解压的数据
func (p *Packet) Body() []byte {
	return p.body
}

// Data 完整的请求或响应数据
func (p *Packet) Data() []byte {
	return p.data
}

func (p *Packet) String() string {
	return fmt.Sprintf(
		`{"packetHeader": %s, "svrPkgLen": %d, "servicePacketHeader": %s}`, p.ph.String(), p.svrPkgLen, p.sph.String(),
	)
}

type PacketParser struct {
	data []byte
}

func NewPacketParser(data []byte) *PacketParser {
	return &PacketParser{data: data}
}

// IsComplete 判断包数据是否完成
// 不完整时返回0，完整时返回包目标长度
func (pp *PacketParser) IsComplete() uint32 {
	buf := bufferpool.Get()
	defer bufferpool.Put(buf)

	buf.Write(pp.data)
	// `PackageLength`为`uint32`类型，即为`4 bytes`
	// 当`data`的长度小于`4 bytes`时，即包数据不完整
	if buf.Len() < int(constSizeOfPacketLen) {
		return 0
	}

	// 读取`data`的前`4 bytes`为`PackageLength`
	var size uint32
	_ = binary.Read(buf, binary.BigEndian, &size)
	// `data`剩余的长度小于包目标的剩余长度，即包数据不完整
	if buf.Len() < int(size-constSizeOfPacketLen) {
		return 0
	}

	return size
}

func (pp *PacketParser) ParsePacket() *Packet {
	if 0 == pp.IsComplete() {
		return nil
	}

	buf := bufferpool.Get()
	defer bufferpool.Put(buf)

	buf.Write(pp.data)

	p := &Packet{
		ph:   newPacketHeader(),
		sph:  newServicePacketHeader(),
		data: pp.data,
	}
	p.ph.Read(buf)

	if p.ph.PayloadLength() == 0 {
		return p
	} else if slices.Contains[[]uint32, uint32](pushCmds, p.ph.Cmd) {
		// 推送消息没有`svr_pkg_len`和`service_packet_header`
		p.svrPkgLen = 0
		_, _ = buf.Read(p.body)
	} else {
		_ = binary.Read(buf, binary.BigEndian, &p.svrPkgLen)
		p.sph.Read(buf)

		if p.sph.Magic != ConstSphMagic || p.sph.HeadLen != ConstSphHeadLen {
			logx.Errorf("failed to parse packet, ph: %s, sph: %s", p.ph.String(), p.sph.String())
			return nil
		}

		p.body = make([]byte, p.sph.BodyLen)
		_ = binary.Read(buf, binary.BigEndian, &p.body)
	}

	return p
}
