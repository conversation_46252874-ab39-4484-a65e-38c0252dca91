package tt

import (
	"encoding/binary"
	"io"

	"github.com/zeromicro/go-zero/core/jsonx"
)

type Fixed struct {
	PackageLength uint32 `json:"packageLength"`
	HeaderLength  uint16 `json:"headerLength"`
	Version       uint16 `json:"version"`
	Cmd           uint32 `json:"cmd"`
	Seq           uint32 `json:"seq"`
}

func NewFixed(cmd, seq, payloadLen uint32, version uint16) Fixed {
	var headerLen uint16
	if version == ConstFixedVersionV1 {
		headerLen = ConstFixedHeadLenV1
	} else {
		version = ConstFixedVersionV2
		headerLen = ConstFixedHeadLenV2
	}

	return Fixed{
		PackageLength: uint32(headerLen) + payloadLen,
		HeaderLength:  headerLen,
		Version:       version,
		Cmd:           cmd,
		Seq:           seq,
	}
}

func (f *Fixed) PayloadLength() uint32 {
	return f.PackageLength - uint32(f.<PERSON>)
}

func (f *Fixed) Write(w io.Writer) {
	_ = binary.Write(w, binary.BigEndian, f.<PERSON>)
	_ = binary.Write(w, binary.BigE<PERSON>an, f.<PERSON>)
	_ = binary.Write(w, binary.BigEndian, f.Version)
	_ = binary.Write(w, binary.BigEndian, f.Cmd)
	_ = binary.Write(w, binary.BigEndian, f.Seq)
}

func (f *Fixed) Read(r io.Reader) {
	_ = binary.Read(r, binary.BigEndian, &f.PackageLength)
	_ = binary.Read(r, binary.BigEndian, &f.HeaderLength)
	_ = binary.Read(r, binary.BigEndian, &f.Version)
	_ = binary.Read(r, binary.BigEndian, &f.Cmd)
	_ = binary.Read(r, binary.BigEndian, &f.Seq)
}

type Extent struct {
	StreamId uint16 `json:"stream_id"`
	Padding  uint16 `json:"padding"`
}

func (e *Extent) Write(w io.Writer) {
	_ = binary.Write(w, binary.BigEndian, e.StreamId)
	_ = binary.Write(w, binary.BigEndian, e.Padding)
}

func (e *Extent) Read(r io.Reader) {
	_ = binary.Read(r, binary.BigEndian, &e.StreamId)
	_ = binary.Read(r, binary.BigEndian, &e.Padding)
}

type PacketHeader struct {
	Fixed
	Extent
}

func NewPacketHeader(cmd, seq, payloadLen uint32) *PacketHeader {
	return &PacketHeader{
		Fixed: NewFixed(cmd, seq, payloadLen, ConstFixedVersionV2),
	}
}

func newPacketHeader() *PacketHeader {
	return &PacketHeader{
		Fixed: Fixed{
			Version: ConstFixedVersionV2,
		},
	}
}

func (p *PacketHeader) isWithExtent() bool {
	return p.Version >= ConstFixedVersionV2
}

func (p *PacketHeader) Write(w io.Writer) {
	p.Fixed.Write(w)
	if p.isWithExtent() {
		p.Extent.Write(w)
	}
}

func (p *PacketHeader) Read(r io.Reader) {
	p.Fixed.Read(r)
	if p.isWithExtent() {
		p.Extent.Read(r)
	}
}

func (p *PacketHeader) String() string {
	return jsonx.MarshalToStringIgnoreError(p)
}
