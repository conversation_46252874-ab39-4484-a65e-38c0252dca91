// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.12
// source: servicepackettail.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JaegerContext []byte            `protobuf:"bytes,1,opt,name=jaegerContext" json:"jaegerContext,omitempty"`                                                             // opentrace jaeger_context binary marshal
	TestContext   *string           `protobuf:"bytes,2,opt,name=test_context,json=testContext" json:"test_context,omitempty"`                                              // 自动化测试平台测试用例
	Header        map[string]string `protobuf:"bytes,3,rep,name=header" json:"header,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 透传http2协议头部
	RequestId     *string           `protobuf:"bytes,4,opt,name=request_id,json=requestId" json:"request_id,omitempty"`                                                    // 客户端携带的request-id
}

func (x *TailReq) Reset() {
	*x = TailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicepackettail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TailReq) ProtoMessage() {}

func (x *TailReq) ProtoReflect() protoreflect.Message {
	mi := &file_servicepackettail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TailReq.ProtoReflect.Descriptor instead.
func (*TailReq) Descriptor() ([]byte, []int) {
	return file_servicepackettail_proto_rawDescGZIP(), []int{0}
}

func (x *TailReq) GetJaegerContext() []byte {
	if x != nil {
		return x.JaegerContext
	}
	return nil
}

func (x *TailReq) GetTestContext() string {
	if x != nil && x.TestContext != nil {
		return *x.TestContext
	}
	return ""
}

func (x *TailReq) GetHeader() map[string]string {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *TailReq) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

type TailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TailResp) Reset() {
	*x = TailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicepackettail_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TailResp) ProtoMessage() {}

func (x *TailResp) ProtoReflect() protoreflect.Message {
	mi := &file_servicepackettail_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TailResp.ProtoReflect.Descriptor instead.
func (*TailResp) Descriptor() ([]byte, []int) {
	return file_servicepackettail_proto_rawDescGZIP(), []int{1}
}

type ExtraTailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId        []byte  `protobuf:"bytes,1,opt,name=trace_id,json=traceId" json:"trace_id,omitempty"`
	ChannelId      *uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	IsExtraChannel *bool   `protobuf:"varint,3,opt,name=is_extra_channel,json=isExtraChannel" json:"is_extra_channel,omitempty"`
	ProxyIp        *uint32 `protobuf:"varint,4,opt,name=proxy_ip,json=proxyIp" json:"proxy_ip,omitempty"`
	ProxyPort      *uint32 `protobuf:"varint,5,opt,name=proxy_port,json=proxyPort" json:"proxy_port,omitempty"`
	ClientId       *uint32 `protobuf:"varint,6,opt,name=client_id,json=clientId" json:"client_id,omitempty"`
}

func (x *ExtraTailReq) Reset() {
	*x = ExtraTailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicepackettail_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraTailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraTailReq) ProtoMessage() {}

func (x *ExtraTailReq) ProtoReflect() protoreflect.Message {
	mi := &file_servicepackettail_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraTailReq.ProtoReflect.Descriptor instead.
func (*ExtraTailReq) Descriptor() ([]byte, []int) {
	return file_servicepackettail_proto_rawDescGZIP(), []int{2}
}

func (x *ExtraTailReq) GetTraceId() []byte {
	if x != nil {
		return x.TraceId
	}
	return nil
}

func (x *ExtraTailReq) GetChannelId() uint32 {
	if x != nil && x.ChannelId != nil {
		return *x.ChannelId
	}
	return 0
}

func (x *ExtraTailReq) GetIsExtraChannel() bool {
	if x != nil && x.IsExtraChannel != nil {
		return *x.IsExtraChannel
	}
	return false
}

func (x *ExtraTailReq) GetProxyIp() uint32 {
	if x != nil && x.ProxyIp != nil {
		return *x.ProxyIp
	}
	return 0
}

func (x *ExtraTailReq) GetProxyPort() uint32 {
	if x != nil && x.ProxyPort != nil {
		return *x.ProxyPort
	}
	return 0
}

func (x *ExtraTailReq) GetClientId() uint32 {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return 0
}

type ExtraTailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelId *uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`
}

func (x *ExtraTailResp) Reset() {
	*x = ExtraTailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicepackettail_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraTailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraTailResp) ProtoMessage() {}

func (x *ExtraTailResp) ProtoReflect() protoreflect.Message {
	mi := &file_servicepackettail_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraTailResp.ProtoReflect.Descriptor instead.
func (*ExtraTailResp) Descriptor() ([]byte, []int) {
	return file_servicepackettail_proto_rawDescGZIP(), []int{3}
}

func (x *ExtraTailResp) GetChannelId() uint32 {
	if x != nil && x.ChannelId != nil {
		return *x.ChannelId
	}
	return 0
}

var File_servicepackettail_proto protoreflect.FileDescriptor

var file_servicepackettail_proto_rawDesc = []byte{
	0x0a, 0x17, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x74,
	0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xe8, 0x01, 0x0a, 0x07, 0x54, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x6a, 0x61, 0x65, 0x67, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6a, 0x61, 0x65,
	0x67, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x0a, 0x0a, 0x08, 0x54, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22,
	0xc9, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x54, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x69, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x49, 0x70, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2e, 0x0a, 0x0d, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x54, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64,
}

var (
	file_servicepackettail_proto_rawDescOnce sync.Once
	file_servicepackettail_proto_rawDescData = file_servicepackettail_proto_rawDesc
)

func file_servicepackettail_proto_rawDescGZIP() []byte {
	file_servicepackettail_proto_rawDescOnce.Do(func() {
		file_servicepackettail_proto_rawDescData = protoimpl.X.CompressGZIP(file_servicepackettail_proto_rawDescData)
	})
	return file_servicepackettail_proto_rawDescData
}

var file_servicepackettail_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_servicepackettail_proto_goTypes = []interface{}{
	(*TailReq)(nil),       // 0: servicepacket.TailReq
	(*TailResp)(nil),      // 1: servicepacket.TailResp
	(*ExtraTailReq)(nil),  // 2: servicepacket.ExtraTailReq
	(*ExtraTailResp)(nil), // 3: servicepacket.ExtraTailResp
	nil,                   // 4: servicepacket.TailReq.HeaderEntry
}
var file_servicepackettail_proto_depIdxs = []int32{
	4, // 0: servicepacket.TailReq.header:type_name -> servicepacket.TailReq.HeaderEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_servicepackettail_proto_init() }
func file_servicepackettail_proto_init() {
	if File_servicepackettail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_servicepackettail_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicepackettail_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicepackettail_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraTailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicepackettail_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraTailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_servicepackettail_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_servicepackettail_proto_goTypes,
		DependencyIndexes: file_servicepackettail_proto_depIdxs,
		MessageInfos:      file_servicepackettail_proto_msgTypes,
	}.Build()
	File_servicepackettail_proto = out.File
	file_servicepackettail_proto_rawDesc = nil
	file_servicepackettail_proto_goTypes = nil
	file_servicepackettail_proto_depIdxs = nil
}
