package tt

import (
	"fmt"
	"sync"
	"time"

	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

type cleanType int

const (
	cleanTypeOfTimeout cleanType = iota // 超时
	cleanTypeOfExit                     // 退出
)

type TcpOrWsPendingRequests struct {
	c *Client

	cache   *hashmap.Map[string, *TCPOrWSRequest] // TCP或者Websocket协议的请求等待响应队列
	timeout time.Duration                         // TCP或者Websocket协议的等待响应超时时间
	mutex   sync.Mutex
}

func NewTcpOrWsPendingRequests(c *Client) *TcpOrWsPendingRequests {
	return &TcpOrWsPendingRequests{
		c: c,

		cache: hashmap.New[string, *TCPOrWSRequest](
			common.ConstHashMapCapacity, generic.Equals[string], generic.HashString,
		),
		timeout: waitRespTimeout,
	}
}

func (p *TcpOrWsPendingRequests) handle() {
	defer p.clean(cleanTypeOfExit)

	ticker := timewheel.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.clean(cleanTypeOfTimeout)
		case <-p.c.Context.Done():
			return
		case <-p.c.ExitCh:
			return
		}
	}
}

func (p *TcpOrWsPendingRequests) clean(t cleanType) {
	cid := p.c.Cid()
	now := time.Now()
	defer func() {
		if d := time.Since(now); d > time.Second {
			p.c.Infof("clean pending requests, cid: %s, clean_type: %d, elapsed: %s", cid, t, d.String())
		}
	}()

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 处理剩余的待处理请求
	p.cache.Each(
		func(k string, req *TCPOrWSRequest) {
			switch t {
			case cleanTypeOfTimeout:
				timeout := p.timeout
				if req.timeout != 0 {
					timeout = req.timeout
				}

				duration := time.Since(req.startedAt)
				if duration > timeout {
					// 生成超时响应，避免请求侧一直等待响应
					if req.handle(req.newErrorResponse(errClientWaitTimeout)) {
						p.c.Warnf(
							"waiting for the response timeout[%s], cid: %s, method: %s, cmd: %d, seq: %d, startedAt: %s",
							timeout.String(), cid, req.methodDescriptor.FullName(), req.cmd, req.seq,
							req.startedAt.Format("2006-01-02 15:04:05.000"),
						)
					}

					p.cache.Remove(k)
				}

				if req.everyRetryTimeout != 0 && duration > time.Duration(req.retryTimes+1)*req.everyRetryTimeout {
					req.retryTimeout(errRetryTimeout)
				}
			case cleanTypeOfExit:
				// 生成失效响应，避免请求侧一直等待响应
				if req.handle(req.newErrorResponse(errClientDisabled)) {
					p.c.Warnf(
						"the client has been disabled, cid: %s, method: %s, cmd: %d, seq: %d, startedAt: %s",
						cid, req.methodDescriptor.FullName(), req.cmd, req.seq,
						req.startedAt.Format("2006-01-02 15:04:05.000"),
					)
				}

				p.cache.Remove(k)
			default:
				return
			}
		},
	)
}

func (p *TcpOrWsPendingRequests) put(req *TCPOrWSRequest) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.cache == nil {
		p.cache = hashmap.New[string, *TCPOrWSRequest](
			common.ConstHashMapCapacity, generic.Equals[string], generic.HashString,
		)
	}
	p.cache.Put(genKeyByCmdAndSeq(req.cmd, req.seq), req)
}

func (p *TcpOrWsPendingRequests) getAndRemove(resp *TCPOrWSResponse) *TCPOrWSRequest {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	key := genKeyByCmdAndSeq(resp.cmd, resp.seq)
	req, ok := p.cache.Get(key)
	if !ok {
		return nil
	}

	p.cache.Remove(key)
	return req
}

func genKeyByCmdAndSeq(cmd, seq uint32) string {
	return fmt.Sprintf("%d:%d", cmd, seq)
}
