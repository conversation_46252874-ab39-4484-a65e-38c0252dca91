package tt

import (
	"bytes"
	"compress/zlib"
	"context"
	"crypto/rsa"
	"encoding/hex"
	"io"
	"math/big"
	"strconv"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zeromicro/go-zero/core/utils"
	"golang.org/x/exp/slices"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/crypto"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt/pb"
)

var (
	// errCompressAlgorithmNotSupport = errors.New("compress algorithm not support")
	errCryptAlgorithmNotSupport = errors.New("crypt algorithm not support")
	errFailedToNewRsaEncrypter  = errors.New("new rsa encrypter failed")

	zlibWriterPool = &sync.Pool{New: func() any { return zlib.NewWriter(nil) }}

	getRsaPubKeyOnce    sync.Once
	getRsaEncrypterOnce sync.Once
	rsaPublicKey        *rsa.PublicKey
	rsaEncrypter        crypto.RsaEncrypter
)

func getRsaPublicKey() *rsa.PublicKey {
	getRsaPubKeyOnce.Do(
		func() {
			N, _ := new(big.Int).SetString(strings.TrimPrefix(strings.ToUpper(constRsaPublicKeyN), "0X"), 16)
			E, _ := strconv.ParseInt(constRsaPublicKeyE, 16, 0)
			rsaPublicKey = &rsa.PublicKey{
				N: N,
				E: int(E),
			}
		},
	)

	return rsaPublicKey
}

func GetRsaEncrypter() crypto.RsaEncrypter {
	getRsaEncrypterOnce.Do(
		func() {
			var err error
			rsaEncrypter, err = crypto.NewRsaEncrypter(getRsaPublicKey())
			if err != nil {
				logx.Errorf("failed to new rsa encrypter, error: %+v", err)
			}
		},
	)

	return rsaEncrypter
}

type Protocol struct {
	c *Client
}

func NewProtocol(c *Client) *Protocol {
	return &Protocol{c: c}
}

func (p *Protocol) PackRequest(ctx context.Context, req *TCPOrWSRequest) ([]byte, error) {
	// 压缩
	compressAlgorithm := ConstCompressAlgorithmVersionV1
	body, err := p.Compress(req.raw, compressAlgorithm)
	if err != nil {
		return nil, errors.Errorf("failed to compress request data, error: %+v", err)
	}

	// 加密
	cryptAlgorithm := p.getCryptAlgorithmByCmd(req.cmd)
	// 加密，登录请求进行RSA加密，其它进行AES加密
	body, err = p.Encrypt(body, cryptAlgorithm)
	if err != nil {
		return nil, errors.Errorf("failed to encrypt request data, error: %+v", err)
	}

	tailReq := &pb.TailReq{
		Header: make(map[string]string, len(req.headers)+2),
	}
	// 设置请求头
	for k, v := range req.headers {
		tailReq.Header[k] = v
	}
	// 客户端版本
	tailReq.Header[reqHeaderKeyOfXTTClientVersion] = strconv.FormatInt(int64(p.c.clientVersion()), 10)
	// 子环境标识符
	if p.c.clientInfo.SubEnvFlag != "" {
		tailReq.Header[reqHeaderKeyOfXQWTrafficMark] = p.c.clientInfo.SubEnvFlag
	}
	// 请求ID
	if traceID := trace.TraceIDFromContext(ctx); traceID != "" {
		tailReq.RequestId = &traceID
	}
	tail, _ := protobuf.MarshalMessage(tailReq)

	sph := NewServicePacketHeader(
		WithBodyLen(uint32(len(body))),
		WithCompressLen(uint32(len(req.raw))),
		WithClientVersion(p.c.clientVersion()),
		WithCmd(req.cmd),
		WithUid(p.c.getUidByCmd(req.cmd)),
		WithDeviceId(p.c.deviceID()),
		WithClientType(uint16(p.c.clientInfo.ClientType)),
		WithCompressAlgorithm(compressAlgorithm),
		WithCryptAlgorithm(cryptAlgorithm),
		WithTerminalType(p.c.terminalType()),
		WithTailLen(uint16(len(tail))),
	)

	ph := NewPacketHeader(req.cmd, req.seq, constSizeOfPacketLen+uint32(sph.HeadLen)+sph.BodyLen+uint32(sph.TailLen))

	packet := NewPacket(ph, sph, body, tail)
	return packet.Data(), nil
}

func (p *Protocol) UnpackResponse(data []byte) (*TCPOrWSResponse, error) {
	packet := NewPacketParser(data).ParsePacket()
	if packet == nil {
		return nil, errors.Errorf("failed to parse data to packet[%s]", hex.EncodeToString(data))
	}

	ph := packet.PacketHeader()
	sph := packet.ServicePacketHeader()
	body := packet.Body()

	if slices.Contains[[]uint32, uint32](pushCmds, ph.Cmd) {
		// 推送消息没有 service_packet_header 部分
		return &TCPOrWSResponse{
			Response: &Response{
				cmd:   ph.Cmd,
				seq:   ph.Seq,
				_data: packet.Data(),
			},
			isPush: true,
		}, nil
	}

	if sph.Ret != 0 || sph.ServiceRetCode != 0 {
		logx.Errorf(
			"got an error from tcp or websocket response, ret: %d, svrRet: %d, bodyLen: %d, body: %s",
			sph.Ret, sph.ServiceRetCode, sph.BodyLen, hex.EncodeToString(body),
		)
	}

	if len(body) == 0 {
		return &TCPOrWSResponse{
			Response: &Response{
				cmd: ph.Cmd,
				seq: ph.Seq,
			},
			ret1: sph.Ret,
			ret2: sph.ServiceRetCode,
		}, nil
	}

	// 解密
	decrypt, err := p.Decrypt(body, sph.CryptAlgorithm)
	if err != nil {
		return nil, errors.Errorf(
			"failed to decrypt data of packet[%s], error: %+v", hex.EncodeToString(packet.Body()), err,
		)
	}

	// 解压
	decompress, err := p.Decompress(decrypt, sph.CompressLen, sph.CompressAlgorithm)
	if err != nil {
		return nil, errors.Errorf(
			"failed to decompress data of packet[%s], error: %+v", hex.EncodeToString(decrypt), err,
		)
	}

	return &TCPOrWSResponse{
		Response: &Response{
			cmd:   ph.Cmd,
			seq:   ph.Seq,
			_data: decompress,
		},
		ret1: sph.Ret,
		ret2: sph.ServiceRetCode,
	}, nil
}

func (p *Protocol) Compress(src []byte, algorithm uint16) ([]byte, error) {
	if algorithm == ConstCompressAlgorithmVersionV1 {
		return p.zlibCompress(src)
	}

	return src, nil
}

func (p *Protocol) Decompress(src []byte, compressLen uint32, algorithm uint16) ([]byte, error) {
	if algorithm == ConstCompressAlgorithmVersionV1 {
		return p.zlibDecompress(src, compressLen)
	}

	return src, nil
}

func (p *Protocol) zlibCompress(src []byte) (dst []byte, err error) {
	b := new(bytes.Buffer)

	w := zlibWriterPool.Get().(*zlib.Writer)
	defer zlibWriterPool.Put(w)

	w.Reset(b)
	if _, err = w.Write(src); err != nil {
		return
	}
	err = w.Close()
	dst = b.Bytes()

	return
}

func (p *Protocol) zlibDecompress(src []byte, compressLen uint32) (dst []byte, err error) {
	br := bytes.NewReader(src)

	r, err := zlib.NewReader(br)
	if err != nil {
		return dst, err
	}
	defer func(r io.ReadCloser) {
		err = r.Close()
	}(r)

	var b bytes.Buffer
	b.Grow(int(compressLen))

	// G110: Potential DoS vulnerability via decompression bomb (gosec)
	for {
		if _, err = io.CopyN(&b, r, 1024*8); err != nil {
			if err == io.EOF {
				err = nil
			}
			break
		}
	}

	if err != nil {
		return dst, err
	} else {
		dst = b.Bytes()
	}

	return dst, err
}

func (p *Protocol) Encrypt(src []byte, algorithm uint16) ([]byte, error) {
	if algorithm == ConstCryptAlgorithmNoEncrypt {
		return src, nil
	} else if algorithm == ConstCryptAlgorithmRsaEncryptWithPublicKey {
		return p.rsaEncrypt(src)
	} else if algorithm == ConstCryptAlgorithmAesDecryptWithPrivateKey {
		key := p.c.authInfo.MD5Password
		if p.c.authInfo.SessionKey != "" {
			key = p.c.authInfo.SessionKey
		}

		return p.aesEncrypt(src, utils.StringToByteSlice(key))
	} else {
		return nil, errCryptAlgorithmNotSupport
	}
}

func (p *Protocol) Decrypt(src []byte, algorithm uint16) ([]byte, error) {
	if algorithm == ConstCryptAlgorithmNoEncrypt {
		return src, nil
	} else if algorithm == ConstCryptAlgorithmAesDecryptWithPrivateKey {
		key := p.c.authInfo.MD5Password
		if p.c.authInfo.SessionKey != "" {
			key = p.c.authInfo.SessionKey
		}

		return p.aesDecrypt(src, utils.StringToByteSlice(key))
	} else {
		return nil, errCryptAlgorithmNotSupport
	}
}

func (p *Protocol) rsaEncrypt(src []byte) (dst []byte, err error) {
	encrypter := GetRsaEncrypter()
	if encrypter == nil {
		return nil, errFailedToNewRsaEncrypter
	}

	return encrypter.Encrypt(src)
}

func (p *Protocol) aesEncrypt(src, key []byte) (dst []byte, err error) {
	var k, iv [16]byte
	copy(k[:16], key)
	copy(iv[:16], key)

	dst, err = crypto.AesCBCEncrypt(src, k[:], iv[:], crypto.PKCS7_PADDING)
	return
}

func (p *Protocol) aesDecrypt(src, key []byte) (dst []byte, err error) {
	var k, iv [16]byte
	copy(k[:16], key)
	copy(iv[:16], key)

	dst, err = crypto.AesCBCDecrypt(src, k[:], iv[:], crypto.PKCS7_PADDING)
	return
}

func (p *Protocol) getCryptAlgorithmByCmd(cmd uint32) uint16 {
	if cmd == cmdOfAuth {
		return ConstCryptAlgorithmRsaEncryptWithPublicKey
	}

	return ConstCryptAlgorithmAesDecryptWithPrivateKey
}
