package tt

import (
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"time"

	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	http "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
)

var (
	_ clienttypes.IRequest = (*Request)(nil)
	_ clienttypes.IRequest = (*TCPOrWSRequest)(nil)
	_ clienttypes.IRequest = (*GRPCRequest)(nil)
	_ clienttypes.IRequest = (*HTTPRequest)(nil)
)

type Request struct {
	name      string        // 请求消息名称（唯一标识）
	cmd       uint32        // 请求消息的CMD值
	seq       uint32        // 请求消息的序列号
	raw       []byte        // 压缩、加密前的数据
	startedAt time.Time     // 请求开始时间
	host      string        // 请求目标地址（域名）
	branch    string        // 接口所属分支
	timeout   time.Duration // 请求超时时间
	lock      sync.Mutex
	handled   bool
}

func (r *Request) Key() string {
	return fmt.Sprintf("cmd: %d, seq: %d", r.cmd, r.seq)
}

func (r *Request) Headers() map[string][]string {
	return nil
}

func (r *Request) Body() []byte {
	return r.raw
}

func (r *Request) Target() string {
	return strconv.FormatUint(uint64(r.cmd), 10)
}

func (r *Request) Name() string {
	return r.name
}

func (r *Request) Protocol() string {
	return ""
}

func (r *Request) Host() string {
	return r.host
}

func (r *Request) Path() string {
	return ""
}

func (r *Request) Data() []byte {
	return r.raw
}

func (r *Request) Timeout() time.Duration {
	return r.timeout
}

func (r *Request) StartedAt() time.Time {
	return r.startedAt
}

type TCPOrWSRequest struct {
	*Request

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	inputDescriptor  protoreflect.MessageDescriptor // 请求描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述
	headers          map[string]string              // 请求头
	data             []byte                         // 用于展示的请求数据

	noResponse bool // 请求是否没有响应，为`true`时即不需要等待响应消息
	responseCh chan<- clienttypes.IResponse

	retryTimes        int           // 重试次数
	everyRetryTimeout time.Duration // 每次重试的超时时间
}

func (r *TCPOrWSRequest) Protocol() string {
	return string(constants.TCP)
}

func (r *TCPOrWSRequest) Path() string {
	return string(r.methodDescriptor.FullName())
}

func (r *TCPOrWSRequest) Data() []byte {
	return r.data
}

func (r *TCPOrWSRequest) handle(resp clienttypes.IResponse) bool {
	if r.noResponse || r.responseCh == nil || r.handled {
		// 表示本次没有对请求对象做处理（可能的因为有：请求没有响应、响应管道为空、请求已被处理）
		return false
	}

	r.lock.Lock()
	defer r.lock.Unlock()

	r.responseCh <- resp
	r.handled = true
	return true
}

func (r *TCPOrWSRequest) retryTimeout(err error) {
	r.retryTimes += 1
	r.responseCh <- r.newErrorResponse(err)
}

func (r *TCPOrWSRequest) newEmptyResponse() *TCPOrWSResponse {
	return &TCPOrWSResponse{
		Response: &Response{
			method:  string(r.methodDescriptor.FullName()),
			cmd:     r.cmd,
			seq:     r.seq,
			elapsed: time.Since(r.startedAt),
		},
		methodDescriptor: r.methodDescriptor,
		outputDescriptor: r.outputDescriptor,
	}
}

func (r *TCPOrWSRequest) newErrorResponse(err error) *TCPOrWSResponse {
	return &TCPOrWSResponse{
		Response: &Response{
			method:  string(r.methodDescriptor.FullName()),
			cmd:     r.cmd,
			seq:     r.seq,
			elapsed: time.Since(r.startedAt),
			err:     err,
		},
		methodDescriptor: r.methodDescriptor,
		outputDescriptor: r.outputDescriptor,
	}
}

type GRPCRequest struct {
	*Request

	methodDescriptor protoreflect.MethodDescriptor  // 方法描述
	inputDescriptor  protoreflect.MessageDescriptor // 请求描述
	outputDescriptor protoreflect.MessageDescriptor // 响应描述
	headers          metadata.MD                    // 请求消息的Headers
	data             []byte                         // 用于展示的请求数据

	supportCommonStatus bool // 是否支持`TT`的`CommonStatus`

	c grpc.ClientConf
	r *grpc.Request
}

func (r *GRPCRequest) Headers() map[string][]string {
	return r.headers.Copy()
}

func (r *GRPCRequest) Target() string {
	if r.cmd != 0 {
		return r.Request.Target()
	}

	return string(r.methodDescriptor.FullName())
}

func (r *GRPCRequest) Protocol() string {
	return string(constants.GRPC)
}

func (r *GRPCRequest) Path() string {
	return string(r.methodDescriptor.FullName())
}

func (r *GRPCRequest) Data() []byte {
	return r.data
}

type HTTPRequest struct {
	*Request

	url     string      // 请求消息的Url
	method  string      // 请求消息的Method
	headers http.Header // 请求消息的Headers

	onceOfParseURL sync.Once
	uOfParseURL    *url.URL
	errOfParseURL  error

	r *http.Request
}

func (r *HTTPRequest) Key() string {
	return fmt.Sprintf("url: %s, seq: %d", r.url, r.seq)
}

func (r *HTTPRequest) Headers() map[string][]string {
	return r.headers.Clone()
}

func (r *HTTPRequest) Target() string {
	r.parseURL()
	if r.errOfParseURL != nil {
		return r.url
	}

	// remove the data of user, query and fragment
	r.uOfParseURL.User = nil
	r.uOfParseURL.RawQuery = ""
	r.uOfParseURL.Fragment = ""

	return r.uOfParseURL.String()
}

func (r *HTTPRequest) Protocol() string {
	r.parseURL()
	if r.errOfParseURL == nil && r.uOfParseURL.Scheme != "" {
		return r.uOfParseURL.Scheme
	}

	if protocol := r.Request.Protocol(); protocol != "" {
		return protocol
	}

	return string(constants.HTTP)
}

func (r *HTTPRequest) Host() string {
	r.parseURL()
	if r.errOfParseURL == nil && r.uOfParseURL.Hostname() != "" {
		return r.uOfParseURL.Hostname()
	}

	return ""
}

func (r *HTTPRequest) Path() string {
	return r.Target()
}

func (r *HTTPRequest) parseURL() {
	r.onceOfParseURL.Do(
		func() {
			r.uOfParseURL, r.errOfParseURL = url.Parse(r.url)
		},
	)
}
