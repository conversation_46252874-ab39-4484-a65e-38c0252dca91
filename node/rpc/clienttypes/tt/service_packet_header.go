package tt

import (
	"encoding/binary"
	"io"

	"github.com/zeromicro/go-zero/core/jsonx"
)

type Option interface {
	apply(sph *ServicePacketHeader)
}

type optionFunc func(sph *ServicePacketHeader)

func (fn optionFunc) apply(sph *ServicePacketHeader) {
	fn(sph)
}

func WithBodyLen(u uint32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.BodyLen = u
		},
	)
}

func WithSessionKey(b []byte) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			copy(sph.SessionKey[:], b)
		},
	)
}

func WithCompressLen(u uint32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.CompressLen = u
		},
	)
}

func WithClientVersion(i int32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.Ret = i
		},
	)
}

func WithCmd(u uint32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.CmdId = u
		},
	)
}

func WithUid(u uint32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.Uid = u
		},
	)
}

func WithDeviceId(b []byte) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			copy(sph.DeviceId[:], b)
		},
	)
}

func WithClientType(u uint16) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.ClientType = u
		},
	)
}

func WithCompressAlgorithm(u uint16) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.CompressAlgorithm = u
		},
	)
}

func WithCryptAlgorithm(u uint16) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.CryptAlgorithm = u
		},
	)
}

func WithTerminalType(u uint32) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.TerminalType = u
		},
	)
}

func WithTailLen(u uint16) Option {
	return optionFunc(
		func(sph *ServicePacketHeader) {
			sph.TailLen = u
		},
	)
}

func defaultServicePacketHeader() *ServicePacketHeader {
	return &ServicePacketHeader{
		Magic:   ConstSphMagic,
		HeadLen: ConstSphHeadLen,
		HeadVer: ConstSphHeadVerV2,
		SessionKey: [32]byte{
			0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
			0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
			0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
			0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
		},
		ClientType:        uint16(ConstClientTypeAndroid),
		CompressVersion:   constCompressVersion,
		CompressAlgorithm: ConstCompressAlgorithmVersionV1,
		CryptAlgorithm:    ConstCryptAlgorithmAesDecryptWithPrivateKey,
		Reserved:          [4]byte{0x30, 0x30, 0x30, 0x30},
	}
}

func withOptions(opts ...Option) *ServicePacketHeader {
	sph := defaultServicePacketHeader()
	for _, opt := range opts {
		opt.apply(sph)
	}

	return sph
}

type ServicePacketHeader struct {
	/* 内部字段，修改无用 */
	/* 1 + 1 + 2 + 4 + 32 = 40 bytes */
	Magic      byte     `json:"magic"`      // LOGIC_PACKET_MAGIC
	HeadLen    byte     `json:"headLen"`    // 是主机字节序，不是网络字节序
	HeadVer    uint16   `json:"headVer"`    // SERVICE_PACKET_VER_V1/V2
	BodyLen    uint32   `json:"bodyLen"`    // 压缩后的大小
	SessionKey [32]byte `json:"sessionKey"` // 加密Key，Client不用填， 由proxy填充

	/* 以下需要外部赋值 */
	/* 4 + 4 + 4 + 4 = 16 bytes */
	CompressLen uint32 `json:"compressLen"` // 压缩前的大小
	Ret         int32  `json:"ret"`         // client -> server，ret填客户端版本号；server -> client，返回结果。如果非0，body无用
	CmdId       uint32 `json:"cmdId"`       // 命令号
	Uid         uint32 `json:"uid"`         // uid

	/* 16 bytes */
	DeviceId [16]byte `json:"deviceId"` // 设备号

	/* 2 + 2 + 2 + 2 = 8 bytes */
	ClientType        uint16 `json:"clientType"`        // 终端类型
	CompressVersion   uint16 `json:"compressVersion"`   // 压缩版本
	CompressAlgorithm uint16 `json:"compressAlgorithm"` // 压缩算法
	CryptAlgorithm    uint16 `json:"cryptAlgorithm"`    // 加密算法 （RSA或AES）

	/* 4 + 2 + 4 + 4 + 2 = 16 bytes */
	Ip             uint32 `json:"ip"` // 客户端ip
	SyncType       uint16 `json:"syncType"`
	TerminalType   uint32 `json:"terminalType"`
	ServiceRetCode int32  `json:"serviceRetCode"` // 暂时：client -> server，携带channel_id
	ClientPort     uint16 `json:"clientPort"`     // 客户端端口

	// 至此，保留字段用光光，2018.12.24
	// 以下是 PACKET_VER_V2 新增, 2020.05.12
	/* 2 + 2 + 4 = 8 bytes */
	TailLen      uint16  `json:"tailLen"` // tail长度
	ExtraTailLen uint16  `json:"extraTailLen"`
	Reserved     [4]byte `json:"reserved"` // 保留
}

func NewServicePacketHeader(options ...Option) *ServicePacketHeader {
	return withOptions(options...)
}

func newServicePacketHeader() *ServicePacketHeader {
	return &ServicePacketHeader{}
}

func (h *ServicePacketHeader) Write(w io.Writer) {
	_ = binary.Write(w, binary.BigEndian, h.Magic)
	_ = binary.Write(w, binary.BigEndian, h.HeadLen)
	_ = binary.Write(w, binary.BigEndian, h.HeadVer)
	_ = binary.Write(w, binary.BigEndian, h.BodyLen)
	_ = binary.Write(w, binary.BigEndian, h.SessionKey)
	_ = binary.Write(w, binary.BigEndian, h.CompressLen)
	_ = binary.Write(w, binary.BigEndian, h.Ret)
	_ = binary.Write(w, binary.BigEndian, h.CmdId)
	_ = binary.Write(w, binary.BigEndian, h.Uid)
	_ = binary.Write(w, binary.BigEndian, h.DeviceId)
	_ = binary.Write(w, binary.BigEndian, h.ClientType)
	_ = binary.Write(w, binary.BigEndian, h.CompressVersion)
	_ = binary.Write(w, binary.BigEndian, h.CompressAlgorithm)
	_ = binary.Write(w, binary.BigEndian, h.CryptAlgorithm)
	_ = binary.Write(w, binary.BigEndian, h.Ip)
	_ = binary.Write(w, binary.BigEndian, h.SyncType)
	_ = binary.Write(w, binary.BigEndian, h.TerminalType)
	_ = binary.Write(w, binary.BigEndian, h.ServiceRetCode)
	_ = binary.Write(w, binary.BigEndian, h.ClientPort)

	// V1 also write
	_ = binary.Write(w, binary.BigEndian, h.TailLen)
	_ = binary.Write(w, binary.BigEndian, h.ExtraTailLen)

	_ = binary.Write(w, binary.BigEndian, h.Reserved)
}

func (h *ServicePacketHeader) Read(r io.Reader) {
	_ = binary.Read(r, binary.BigEndian, &h.Magic)
	_ = binary.Read(r, binary.BigEndian, &h.HeadLen)
	_ = binary.Read(r, binary.BigEndian, &h.HeadVer)
	_ = binary.Read(r, binary.BigEndian, &h.BodyLen)
	_ = binary.Read(r, binary.BigEndian, &h.SessionKey)
	_ = binary.Read(r, binary.BigEndian, &h.CompressLen)
	_ = binary.Read(r, binary.BigEndian, &h.Ret)
	_ = binary.Read(r, binary.BigEndian, &h.CmdId)
	_ = binary.Read(r, binary.BigEndian, &h.Uid)
	_ = binary.Read(r, binary.BigEndian, &h.DeviceId)
	_ = binary.Read(r, binary.BigEndian, &h.ClientType)
	_ = binary.Read(r, binary.BigEndian, &h.CompressVersion)
	_ = binary.Read(r, binary.BigEndian, &h.CompressAlgorithm)
	_ = binary.Read(r, binary.BigEndian, &h.CryptAlgorithm)

	_ = binary.Read(r, binary.BigEndian, &h.Ip)
	_ = binary.Read(r, binary.BigEndian, &h.SyncType)
	_ = binary.Read(r, binary.BigEndian, &h.TerminalType)
	_ = binary.Read(r, binary.BigEndian, &h.ServiceRetCode)
	_ = binary.Read(r, binary.BigEndian, &h.ClientPort)

	// v1 also read
	_ = binary.Read(r, binary.BigEndian, &h.TailLen)
	_ = binary.Read(r, binary.BigEndian, &h.ExtraTailLen)

	_ = binary.Read(r, binary.BigEndian, &h.Reserved)
}

func (h *ServicePacketHeader) String() string {
	return jsonx.MarshalToStringIgnoreError(h)
}
