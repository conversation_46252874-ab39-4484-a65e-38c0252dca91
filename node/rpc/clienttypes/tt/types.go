package tt

import (
	"fmt"
	"slices"
	"sync"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zyedidia/generic"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/reflect/protoreflect"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	http "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/tcp"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/websocket"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type (
	Transport interface {
		Send(req []byte) error
		Close() error
	}

	Compressor interface {
		Compress(src []byte) (dst []byte, err error)
		Decompress(src []byte) (dst []byte, err error)
	}

	Crypto interface {
		Encrypt(src []byte) ([]byte, uint16, error)
		Decrypt(src []byte, algorithm uint16) ([]byte, error)
	}
)

type (
	client[I IClientInfo] struct {
		*clienttypes.BasicClient

		createClientInfo *pb.CreateClientReq // 创建客户端时的请求信息
		clientInfo       I                   // 客户端信息

		pm *protobuf.ProtoManager // ProtoBuf管理器

		closeOnce sync.Once
	}

	// LogicClient TT逻辑层客户端（用于直接请求`TT`逻辑层服务接口）
	LogicClient struct {
		*client[LogicClientInfo]
	}

	Client struct {
		*client[ClientInfo]

		tcpURL                 string                  // TCP服务端地址
		tcpTransport           *tcp.Client             // 传输客户端（TCP协议）
		wsURL                  string                  // Websocket服务端地址
		wsTransport            *websocket.Client       // 传输客户端（Websocket协议）
		tcpOrWsProtocol        *Protocol               // 产品协议
		tcpOrWsPendingRequests *TcpOrWsPendingRequests // 等待响应的请求

		grpcURL       string                       // gRPC服务端地址
		grpcAuthority string                       // gRPC服务端的Authority
		grpcTransport *atomic.Pointer[grpc.Client] // 传输客户端(gRPC协议)，注：这里不对`gRPC`客户端设置专门的锁，因为更改连接地址是低概率事件，不想由于这种低概率事件影响整体性能

		httpBaseURL   string       // HTTP服务端基础地址（HTTP协议）
		httpTransport *http.Client // 传输客户端(HTTP协议)

		authInfo    AuthInfo       // 用户信息
		grpcInfo    GRPCInfo       // // Deprecated: use RefreshInfo` instead，GRPC信息
		refreshInfo *RefreshInfo   // 定时刷新的信息
		loginResp   map[string]any // 模拟登录的响应信息
		loggedIn    bool           // 是否已登录

		pendingMutex sync.Mutex
	}

	IClientInfo interface {
		branch() string
		platformType() PlatformType
		clientVersion() string
		clientType() ClientType
		appID() AppID
		marketID() MarketID
		subEnv() string
	}
	basicClientInfo struct {
		Url           string       `json:"url" zh:"服务地址"`
		Branch        string       `json:"branch,default=master" mapstructure:"branch" zh:"分支"`
		PlatformType  PlatformType `json:"platform_type,omitempty,optional,default=1" mapstructure:"platform_type" zh:"平台类型（移动端，Web端，PC端，Pad端，QQ小程序，微信小程序）"`
		ClientVersion string       `json:"client_version,omitempty,optional" mapstructure:"client_version" zh:"客户端版本"`
		ClientType    ClientType   `json:"client_type,omitempty,optional,default=0" mapstructure:"client_type" zh:"客户端类型（TT：安卓，IOS，WEB，PC；提提电竞：安卓App，安卓H5，IOS App，IOS H5）"`
		AppID         AppID        `json:"app_id,omitempty,optional,default=0" mapstructure:"app_id" zh:"App ID"`
		MarketID      MarketID     `json:"market_id,omitempty,optional,default=0" mapstructure:"market_id" zh:"Market ID"`
		BundleID      string       `json:"bundle_id,omitempty,optional" mapstructure:"bundle_id" zh:"Bundle ID"`
		SubEnvFlag    string       `json:"sub_env_flag,omitempty,optional" mapstructure:"sub_env_flag" zh:"子环境标识"`
	}
	LogicClientInfo struct {
		basicClientInfo `mapstructure:",squash"`

		Uid string `json:"uid" mapstructure:"uid" zh:"uid"`

		grpcInfo `mapstructure:",squash"`
	}
	ClientInfo struct {
		basicClientInfo `mapstructure:",squash"`

		Username      string `json:"username" zh:"用户名"`
		Password      string `json:"password" zh:"密码"`
		IsMD5Pwd      bool   `json:"is_md5_pwd,omitempty,optional,default=false" mapstructure:"is_md5_pwd" zh:"密码是否已使用MD5加密"`
		GrpcAuthority string `json:"grpc_authority,omitempty,optional" mapstructure:"grpc_authority" zh:"gRPC Authority"`

		TcpURL       string `json:"tcp_url,omitempty,optional" zh:"TCP URL"`
		WebsocketURL string `json:"websocket_url,omitempty,optional" zh:"Websocket URL"`
		GrpcURL      string `json:"grpc_url,omitempty,optional" zh:"gRPC URL"`
		HttpURL      string `json:"http_url,omitempty,optional" zh:"HTTP URL"`
	}

	grpcInfo struct {
		Target    string `json:"target,omitempty,optional" mapstructure:"target" zh:"gRPC Target Endpoint"`
		Authority string `json:"authority,omitempty,optional" mapstructure:"authority" zh:"gRPC Authority"`
		UserAgent string `json:"user_agent,omitempty,optional" mapstructure:"user_agent" zh:"gRPC User-Agent"`
		NoTLS     bool   `json:"no_tls,omitempty,optional" mapstructure:"no_tls" zh:"gRPC NoTLS"`
	}
	AuthInfo struct {
		UserName            string `json:"username"`                                                           // 用户名（测试数据传入）
		Password            string `json:"password"`                                                           // 未加密密码（测试数据传入）
		MD5Password         string `json:"md5_password"`                                                       // 已加密密码
		Uid                 uint32 `json:"uid" mapstructure:"uid"`                                             // uid（从登录响应中获取）
		NickName            string `json:"nick_name" mapstructure:"nick_name"`                                 // 昵称（从登录响应中获取）
		Phone               string `json:"user_phone" mapstructure:"user_phone"`                               // 手机号码（从登录响应中获取）
		Account             string `json:"user_account" mapstructure:"user_account"`                           // 唯一账号（从登录响应中获取）
		Alias               string `json:"alias" mapstructure:"alias"`                                         // 别名（从登录响应中获取）
		IMSyncKey           uint32 `json:"im_sync_key" mapstructure:"im_sync_key"`                             // IM消息最新的`sync key`
		LoginKey            string `json:"login_key" mapstructure:"login_key"`                                 // 标志用户这次登录的唯一key，每次登录都不一样
		SessionKey          string `json:"session_key" mapstructure:"session_key"`                             // 通信用的加密key（从登录响应中获取）
		UserType            uint32 `json:"user_type" mapstructure:"user_type"`                                 // 用户类型（从登录响应中获取）
		UnionToken          string `json:"tt_union_token" mapstructure:"tt_union_token"`                       // Deprecated: use RefreshInfo.UnionToken instead，gRPC调用接口时鉴权用的token
		UnionTokenExpiresIn int64  `json:"tt_union_token_expires_in" mapstructure:"tt_union_token_expires_in"` // Deprecated: use RefreshInfo.UnionTokenExpiresIn instead，gRPC调用接口时鉴权用的token的过期时间
		WebSecureToken      string `json:"web_secure_token" mapstructure:"web_secure_token"`                   // Deprecated: use RefreshInfo.WebSecureToken instead，TT内Web使用的token
	}

	SecureTokens struct {
		IDToken               string   `json:"id_token" mapstructure:"id_token"`                                 // ID Token，包含用户的身份信息及Stellaris Authentication系统对其的签名
		AccessToken           string   `json:"access_token" mapstructure:"access_token"`                         // Access Token，可用于调用Stellaris的API
		TokenType             string   `json:"token_type" mapstructure:"token_type"`                             // Access Token的类型
		ExpiresIn             uint32   `json:"expires_in" mapstructure:"expires_in"`                             // Access Token和ID Token的到期时间（以秒为单位）
		Scopes                []string `json:"scopes" mapstructure:"scopes"`                                     // 保留字段：与令牌关联的范围（可以有多个值）
		RefreshToken          string   `json:"refresh_token" mapstructure:"refresh_token"`                       // Refresh Token
		RefreshTokenExpiresIn uint32   `json:"refresh_token_expires_in" mapstructure:"refresh_token_expires_in"` // Refresh Token的到期时间（以秒为单位）
	}

	GRPCBlackList struct {
		ApiRules []StringMatch `json:"api_rule" mapstructure:"api_rule"`
	}
	GZIPWhiteList struct {
		ApiRules []StringMatch `json:"api_rule" mapstructure:"api_rule"`
	}
	StringMatch struct {
		MatchType  StringMatchType `json:"match_type" mapstructure:"match_type"`
		MatchValue string          `json:"match_value" mapstructure:"match_value"`
	}
	Endpoint struct {
		Address   string    `json:"address" mapstructure:"address"`
		TlsConfig TlsConfig `json:"tls_config" mapstructure:"tls_config"`
	}
	TlsConfig struct {
		Authority string  `json:"authority" mapstructure:"authority"`
		Mode      TLSMode `json:"mode" mapstructure:"mode"`
	}
	GRPCInfo struct {
		GRPCBlackList GRPCBlackList `json:"black_list_api_info" mapstructure:"black_list_api_info"`
		GZIPWhiteList GZIPWhiteList `json:"gzip_white_list_api_info" mapstructure:"gzip_white_list_api_info"`
		Endpoints     []Endpoint    `json:"endpoints" mapstructure:"endpoints"`
	}

	RefreshInfo struct {
		UnionToken          *atomic.String             // gRPC调用接口时鉴权用的token
		UnionTokenExpiresIn *atomic.Int64              // gRPC调用接口时鉴权用的token的过期时间
		WebSecureToken      *atomic.String             // TT内Web使用的token
		SyncKeys            *atomic.Pointer[[]SyncKey] // 同步Key列表

		RefreshToken          *atomic.String // 刷新token
		RefreshTokenExpiresIn *atomic.Int64  // 刷新token的过期时间

		Checksum      *atomic.String
		GRPCBlackList *atomic.Pointer[GRPCBlackList] // gRPC黑名单
		GZIPWhiteList *atomic.Pointer[GZIPWhiteList] // gzip白名单
		Endpoints     *atomic.Pointer[[]Endpoint]    // gRPC连接地址列表
	}
	SyncKey struct {
		SyncType SyncType `json:"type" mapstructure:"type"`
		SyncKey  uint32   `json:"msg_sync_key" mapstructure:"msg_sync_key"`
	}
	CheckSyncKeyResp struct {
		SyncKeys       []SyncKey `json:"sync_key_list" mapstructure:"sync_key_list"`
		WebSecureToken string    `json:"web_secure_token" mapstructure:"web_secure_token"`
	}
)

func (c *client[I]) clientVersion() int32 {
	switch c.clientInfo.clientType() {
	case ConstClientTypePCTT:
		return int32(GetClientVersionByString(c.clientInfo.clientVersion(), defaultPCClientVersion))
	case ConstClientTypePCLFG:
		return int32(GetClientVersionByString(c.clientInfo.clientVersion(), defaultPCLFGClientVersion))
	default:
		return int32(GetClientVersionByString(c.clientInfo.clientVersion()))
	}
}

func (c *client[I]) terminalType() uint32 {
	return NewFromClientType(c.clientInfo.clientType()).Get()
}

func (c *client[I]) tryToSetBaseReq(raw []byte) []byte {
	var m map[string]any
	if err := jsonx.Unmarshal(raw, &m); err != nil {
		return raw
	}

	v, ok := m[keyOfBaseReq]
	if !ok {
		// 没有设置`base_req`
		m[keyOfBaseReq] = map[string]any{
			keyOfAppID:    c.clientInfo.appID(),
			keyOfMarketID: c.clientInfo.marketID(),
		}
	} else {
		// 有设置`base_req`
		br, ok := v.(map[string]any)
		if !ok {
			return raw
		}

		if _, ok = br[keyOfAppID]; !ok {
			// 没有设置`base_req.app_id`
			br[keyOfAppID] = c.clientInfo.appID()
		}
		if _, ok = br[keyOfMarketID]; !ok {
			// 没有设置`base_req.market_id`
			br[keyOfMarketID] = c.clientInfo.marketID()
		}
	}

	bs, err := jsonx.Marshal(m)
	if err != nil {
		return raw
	}

	return bs
}

func (c *client[I]) tryToSetClientVersion(md protoreflect.MethodDescriptor, raw []byte) []byte {
	method := string(md.FullName())
	if !slices.Contains[[]string, string](setClientVersionMethods, method) {
		return raw
	}

	var m map[string]any
	if err := jsonx.Unmarshal(raw, &m); err != nil {
		return raw
	}

	switch method {
	case methodFullNameOfCppCheckSyncKey, methodFullNameOfGoCheckSyncKey:
		_, ok := m[keyOfCurrVer]
		if !ok {
			m[keyOfCurrVer] = c.clientInfo.clientVersion()
		}

		_, ok = m[keyOfCurrVerCode]
		if !ok {
			m[keyOfCurrVerCode] = c.clientVersion()
		}
	}

	bs, err := jsonx.Marshal(m)
	if err != nil {
		return raw
	}

	return bs
}

func defaultBasicClientInfo() basicClientInfo {
	return basicClientInfo{
		Branch:       defaultBranch,
		PlatformType: ConstPlatformTypeMobile,
		ClientType:   ConstClientTypeAndroid,
		AppID:        ConstAppIDTTNormal,
		MarketID:     ConstMarketIDNone,
		BundleID:     defaultClientBundleID,
	}
}

func newRefreshInfo() *RefreshInfo {
	return &RefreshInfo{
		UnionToken:            atomic.NewString(""),
		UnionTokenExpiresIn:   atomic.NewInt64(0),
		WebSecureToken:        atomic.NewString(""),
		SyncKeys:              atomic.NewPointer((*[]SyncKey)(nil)),
		RefreshToken:          atomic.NewString(""),
		RefreshTokenExpiresIn: atomic.NewInt64(0),
		Checksum:              atomic.NewString(""),
		GRPCBlackList:         atomic.NewPointer((*GRPCBlackList)(nil)),
		GZIPWhiteList:         atomic.NewPointer((*GZIPWhiteList)(nil)),
		Endpoints:             atomic.NewPointer((*[]Endpoint)(nil)),
	}
}

func (i basicClientInfo) branch() string {
	return i.Branch
}

func (i basicClientInfo) platformType() PlatformType {
	return i.PlatformType
}

func (i basicClientInfo) clientVersion() string {
	return i.ClientVersion
}

func (i basicClientInfo) clientType() ClientType {
	return i.ClientType
}

func (i basicClientInfo) appID() AppID {
	return i.AppID
}

func (i basicClientInfo) marketID() MarketID {
	return i.MarketID
}

func (i basicClientInfo) subEnv() string {
	return i.SubEnvFlag
}

func (x *GRPCBlackList) GetApiRules() []StringMatch {
	if x == nil {
		return nil
	}

	return x.ApiRules
}

func (x *GZIPWhiteList) GetApiRules() []StringMatch {
	if x == nil {
		return nil
	}

	return x.ApiRules
}

func StringMatchEqualsFn(a, b StringMatch) bool {
	return a.MatchType == b.MatchType && a.MatchValue == b.MatchValue
}

func StringMatchHashFn(t StringMatch) uint64 {
	return generic.HashString(fmt.Sprintf("%s:%s", t.MatchType, t.MatchValue))
}

func EndpointEqualsFn(a, b Endpoint) bool {
	return a.Address == b.Address && a.TlsConfig.Authority == b.TlsConfig.Authority && a.TlsConfig.Mode == b.TlsConfig.Mode
}

func EndpointHashFn(t Endpoint) uint64 {
	return generic.HashString(fmt.Sprintf("%s:%s:%s", t.Address, t.TlsConfig.Authority, t.TlsConfig.Mode))
}
