package tt

import (
	"net/url"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/atomic"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

func TestPointer(t *testing.T) {
	l1 := &GRPCBlackList{
		ApiRules: []StringMatch{
			{
				MatchType:  ConstStringMatchTypePrefix,
				MatchValue: "/a/b/c",
			},
			{
				MatchType:  ConstStringMatchTypeExact,
				MatchValue: "/x/y/z",
			},
		},
	}
	l2 := &GRPCBlackList{
		ApiRules: []StringMatch{
			{
				MatchType:  ConstStringMatchTypePrefix,
				MatchValue: "/a/b/d",
			},
			{
				MatchType:  ConstStringMatchTypeExact,
				MatchValue: "/x/y/z",
			},
		},
	}
	tests := []struct {
		desc      string
		newAtomic func() *atomic.Pointer[GRPCBlackList]
		initial   *GRPCBlackList
	}{
		{
			desc: "New",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				return atomic.NewPointer(l1)
			},
			initial: l1,
		},
		{
			desc: "New/nil",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				return atomic.NewPointer[GRPCBlackList](nil)
			},
			initial: nil,
		},
		{
			desc: "zero value",
			newAtomic: func() *atomic.Pointer[GRPCBlackList] {
				var p atomic.Pointer[GRPCBlackList]
				return &p
			},
			initial: nil,
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.desc, func(t *testing.T) {
				t.Run(
					"Load", func(t *testing.T) {
						atom := tt.newAtomic()
						require.Equal(t, tt.initial, atom.Load(), "Load should report nil.")
					},
				)

				t.Run(
					"Swap", func(t *testing.T) {
						atom := tt.newAtomic()
						require.Equal(t, tt.initial, atom.Swap(l2), "Swap didn't return the old value.")
						require.Equal(t, l2, atom.Load(), "Swap didn't set the correct value.")
					},
				)

				t.Run(
					"CAS", func(t *testing.T) {
						atom := tt.newAtomic()
						require.True(t, atom.CompareAndSwap(tt.initial, l2), "CAS didn't report a swap.")
						require.Equal(t, l2, atom.Load(), "CAS didn't set the correct value.")
					},
				)

				t.Run(
					"Store", func(t *testing.T) {
						atom := tt.newAtomic()
						atom.Store(l1)
						require.Equal(t, l1, atom.Load(), "Store didn't set the correct value.")
					},
				)
			},
		)
	}
}

func TestParseURL(t *testing.T) {
	tests := []struct {
		name string
		_url string
		want string
	}{
		{
			name: "http url",
			_url: "https://dev-quality.ttyuyin.com",
			want: "dev-quality.ttyuyin.com",
		},
		{
			name: "http url with 80 port",
			_url: "https://dev-quality.ttyuyin.com:80",
			want: "dev-quality.ttyuyin.com",
		},
		{
			name: "tcp url with authority",
			_url: "tcp://110258177:<EMAIL>",
			want: "testing-login.ttyuyin.com",
		},
		{
			name: "multiple urls",
			_url: "tcp://110258177:<EMAIL>,grpc://testing-apiv2.ttyuyin.com:443",
			want: "testing-login.ttyuyin.com",
		},
		{
			name: "grpc url with 443 port",
			_url: "grpc://testing-apiv2.ttyuyin.com:443",
			want: "testing-apiv2.ttyuyin.com:443",
		},
	}

	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				urls := strings.Split(tt._url, common.ConstAddressSeparator)
				u, err := url.Parse(urls[0])
				if err != nil {
					t.Fatal(err)
				}

				domain := u.Host
				if u.Port() == "80" {
					domain = u.Hostname()
				}

				t.Logf("Domain: %s", domain)
				assert.Equal(t, tt.want, domain)
			},
		)
	}
}
