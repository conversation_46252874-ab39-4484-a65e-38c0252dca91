package tt

import "github.com/pkg/errors"

var (
	errEmptyURL              = errors.New("the url cannot be empty")
	errEmptyTcpOrWsURL       = errors.New("the tcp url and websocket url cannot be empty at the same time")
	errEmptyGrpcURL          = errors.New("the grpc url cannot be empty when the client type is PC LFG")
	errEmptyUsername         = errors.New("the username cannot be empty")
	errEmptyPassword         = errors.New("the password cannot be empty")
	errEmptyTcpOrWsTransport = errors.New("the tcp transport and websocket transport cannot be null at the same time")
	errEmptyMethod           = errors.New("the method cannot be empty while creating tcp or grpc request")
	errProtoManagerNotFound  = errors.Errorf("the proto manager not found, product name: %s", productName)
	errClientWaitTimeout     = errors.New("wait response timeout")
	errClientDisabled        = errors.New("the client is disabled")
	errRetryTimeout          = errors.New("retry timeout")

	emptyReqBody = []byte("{}")
)
