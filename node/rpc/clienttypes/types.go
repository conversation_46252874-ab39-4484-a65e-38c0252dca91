package clienttypes

import (
	"context"
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type (
	NewClientFunc         func(*pb.CreateClientReq, ...CreateClientOption) (IClient, error)
	NewLogicFunc          func(context.Context, IClient) (ILogic, error)
	RegisterNewClientFunc func(types.ClientType, NewClientFunc)
	RegisterNewLogicFunc  func(types.ProductName, types.LogicName, NewLogicFunc)
	SendFunc              func(context.Context, IRequest) (IResponse, error)
)

// Transport is the interface that wraps the basic Send method.
// Must be implemented by custom Client.
type Transport interface {
	Send(context.Context, IRequest) (IResponse, error)
}

// Closer is the interface that wraps the basic Close method.
// Must be implemented by custom Client.
type Closer interface {
	Close() error
}

// Auth is the interface about authN.
// Can optionally be implemented by custom Client.
type Auth interface {
	// Login 登录
	Login(ctx context.Context) error
	// Logout 登出
	Logout(ctx context.Context) error
	// GetLoginResp 获取登录响应信息
	GetLoginResp() *structpb.Struct
	// GetUserInfo 获取用户基础信息
	GetUserInfo() *commonpb.UserInfo
}

// Logic is the interface about custom logic function.
// Can optionally be implemented by custom Client.
type Logic interface {
	CustomLogic(context.Context, *pb.ApiCallReq) (*pb.ApiCallResp, error)
}

// IClient is the interface about custom Client
type IClient interface {
	Transport
	Closer

	// ProductName 获取客户端的产品名称
	ProductName() types.ProductName
	// ClientType 获取客户端类型
	ClientType() types.ClientType
	// Cid 获取客户端ID
	Cid() string
	// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
	Environment() string
	// CreatedAt 获取客户端创建时间
	CreatedAt() time.Time
	// GetCreateInfo 获取客户端的创建信息
	GetCreateInfo() *commonpb.CreateInfo
	// GetClientInfo 获取客户端基础信息
	GetClientInfo() *commonpb.ClientInfo
	// GetCustomInfo 获取客户端自定义信息
	GetCustomInfo() *structpb.Struct

	// Failures 获取客户端的连续失败次数
	Failures() uint32
	// ResetFailures 重置客户端的连续失败次数
	ResetFailures()
	// AddFailures 客户端的连续失败次数加一
	AddFailures() uint32
	// Enabled 客户端是否生效
	Enabled() bool
	// SetDisable 把客户端设置为无效
	SetDisable()
	// LastRequestedAt 获取客户端最近一次业务请求时间
	LastRequestedAt() time.Time
	// NumOfRequest 获取客户端请求次数
	NumOfRequest() uint64
	// Requested 客户端完成一次请求
	Requested()
	// UpdateLastRequestedAt 更新客户端最近一次业务请求时间（用于重置空闲时间）
	UpdateLastRequestedAt(time.Time)

	// NewRequest 创建请求，各客户端返回其对应的请求对象
	NewRequest(*pb.ApiCallReq) (IRequest, error)
}

// IRequest is the interface about custom Request.
// Must be implemented by custom Request.
type IRequest interface {
	// Key 获取唯一性键值（用于区分不同的请求）
	Key() string
	// Headers 获取请求头信息
	Headers() map[string][]string
	// Body 获取请求体信息
	Body() []byte
	// Target 获取目标对象（用于限流判断，如：`HTTP`的请求路径、`gRPC`的方法全名、...）
	Target() string
	// Name 获取请求名称
	Name() string
	// Protocol 获取请求协议
	Protocol() string
	// Host  获取请求域名
	Host() string
	// Path 获取请求路径
	Path() string
	// Data 获取请求数据
	Data() []byte
	// Timeout 获取请求超时时间
	Timeout() time.Duration
}

// IResponse is the interface about custom Response.
// Must be implemented by custom Response.
type IResponse interface {
	// Key 获取唯一性键值（用于区分不同的响应）
	Key() string
	// Headers 获取响应头信息
	Headers() *Headers
	// Body 获取响应体信息
	Body() any
	// Status 获取响应状态码
	Status() int32
	// Result 获取响应结果
	Result() string
	// Error 获取响应错误信息
	Error() error
	// Elapsed 获取响应耗时
	Elapsed() time.Duration
}

// BusinessMetric is the interface about business metric.
// Can optionally be implemented by custom Response.
type BusinessMetric interface {
	// BusinessStatus 获取业务状态码
	BusinessStatus() int32
}

type Headers struct {
	Header  map[string][]string
	Trailer map[string][]string
}

// ILogic is the interface about business logic function.
// Must be implemented by custom Logic.
type ILogic interface {
	Call(*pb.ApiCallReq, ...Option) (*pb.ApiCallResp, error)
}
