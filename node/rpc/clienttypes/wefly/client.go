package wefly

import (
	"context"
	"fmt"
	"net/url"

	"github.com/devfeel/mapper"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/utils"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	authcli "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/sso"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	// wefly: 起飞中台客户端
	productName     types.ProductName = "wefly"
	clientTypeWeFly                   = types.ClientType(productName)
)

var _ clienttypes.IClient = (*Client)(nil)

func ProductName() types.ProductName {
	return productName
}

type Client struct {
	*authcli.Client

	createClientInfo *pb.CreateClientReq // 创建客户端时的请求信息

	clientInfo ClientInfo // 客户端信息
	authInfo   AuthInfo   // 用户信息

	loginResp map[string]any // 模拟登录的响应信息
}

func NewClient(in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption) (v clienttypes.IClient, err error) {
	createClientInfo := &pb.CreateClientReq{
		Type:         in.GetType(),
		Url:          in.GetUrl(),
		CustomFields: in.GetCustomFields(),
	}

	c := &Client{createClientInfo: createClientInfo}
	c.Client, err = authcli.NewClient(createClientInfo, opts...)
	if err != nil {
		return nil, err
	}

	err = c.parseCreateClientReq(in)
	if err != nil {
		return nil, err
	}

	err = c.Login()
	if err != nil {
		return nil, err
	}

	c.Keepalive(c.Refresh)

	return c, nil
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return clientTypeWeFly
}

// GetClientInfo 获取客户端基础信息
func (c *Client) GetClientInfo() *commonpb.ClientInfo {
	return &commonpb.ClientInfo{
		Cid:             c.Cid(),
		Nid:             c.NodeID,
		Product:         string(c.ProductName()),
		Type:            c.createClientInfo.GetType(),
		Status:          "", // TODO: 待补充
		CreatedAt:       timestamppb.New(c.CreatedAt()),
		LastCreatedAt:   nil, // TODO: 待补充
		LastRequestedAt: timestamppb.New(c.LastRequestedAt()),
		NumOfRequest:    c.NumOfRequest(),
		CustomInfo:      nil,
		LoginResp:       c.GetLoginResp(),
		UserInfo:        c.GetUserInfo(),
		CreateInfo:      c.GetCreateInfo(),
	}
}

func (c *Client) NewRequest(in *pb.ApiCallReq) (req clienttypes.IRequest, err error) {
	in.Headers = append(
		in.Headers, &http.HttpHeader{Key: ConstHeaderAuthorization, Value: ConstHeaderAuthorizationValue},
	)
	in.Headers = append(
		in.Headers,
		&http.HttpHeader{Key: ConstHeaderBladeAuth, Value: fmt.Sprintf("bearer %s", c.authInfo.AccessToken)},
	)
	return c.Client.NewRequest(in)
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	return c.Call(ctx, req, c.Client.Send)
}

func (c *Client) parseCreateClientReq(in *pb.CreateClientReq) (err error) {
	customFields := in.GetCustomFields()
	if customFields != nil {
		if err = mapper.MapperMap(customFields.AsMap(), &c.clientInfo); err != nil {
			return err
		}
	}
	return nil
}

func (c *Client) Login() (err error) {
	call := &pb.ApiCallReq{
		Method:  ConstMethodPOST,
		Headers: make([]*http.HttpHeader, 0, 5),
	}

	u, err := url.Parse(LoginPath)
	if err != nil {
		return errors.Errorf("invalid url, url: %s", LoginPath)
	}

	q := u.Query()
	q.Set(ConstKeyTenantId, ConstValueTenantIdFly)
	q.Set(ConstKeyGrantType, ConstValueGrantTypePassword)
	q.Set(ConstKeyScope, ConstValueScopeAll)
	q.Set(ConstKeyUsername, c.clientInfo.Account)
	q.Set(ConstKeyPassword, hash.Md5Hex(utils.StringToByteSlice(c.clientInfo.Password)))

	u.RawQuery = q.Encode()
	call.Url = u.String()

	call.Headers = append(
		call.Headers, &http.HttpHeader{Key: ConstHeaderAuthorization, Value: ConstHeaderAuthorizationValue},
	)
	call.Headers = append(
		call.Headers, &http.HttpHeader{Key: ConstHeaderCaptchaCode, Value: ConstHeaderCaptchaCodeValue},
	)
	call.Headers = append(call.Headers, &http.HttpHeader{Key: ConstHeaderTenantId, Value: ConstHeaderTenantIdValue})

	req, err := c.Client.NewRequest(call)
	if err != nil {
		return err
	}

	resp, err := c.Client.Send(c.Context, req)
	if err != nil {
		return err
	}

	if resp.Status() != 200 {
		return errors.Errorf("invalid status of auth response, status: %d, body: %+v", resp.Status(), resp.Body())
	}

	var ok bool
	c.loginResp, ok = resp.Body().(map[string]any)
	if !ok {
		return errors.Errorf(
			"invalid type of auth response data, expected: map[string]any, but got: %T, cid: %s, key: %v",
			resp.Body(), c.Cid(), c.clientInfo,
		)
	}

	var ai AuthInfo
	if err := mapper.MapperMap(c.loginResp, &ai); err != nil {
		return errors.Wrapf(
			err, "failed to copy data to struct[%T], cid: %s, key: %v, data: %v", ai, c.Cid(), c.clientInfo,
			c.loginResp,
		)
	}

	if ai.AccessToken == "" {
		return errors.Errorf(
			"invalid access_token, cid: %s, loginResp: %+v",
			c.Cid(), c.loginResp,
		)
	}

	c.authInfo = ai

	fmt.Println("authInfo =", c.authInfo)

	return nil
}

func (c *Client) Refresh() {
	err := c.Login()
	if err != nil {
		logx.Errorf("refresh err: %s", err)
	}
}

// GetLoginResp 获取模拟登录的响应信息
func (c *Client) GetLoginResp() *structpb.Struct {
	v, err := protobuf.NewStruct(c.loginResp)
	if err != nil {
		logx.Errorf(
			"failed to new a json object, data: %s, error: %v",
			jsonx.MarshalToStringIgnoreError(c.loginResp), err,
		)
	}

	return v
}

func (c *Client) GetUserInfo() *commonpb.UserInfo {
	return &commonpb.UserInfo{
		Cid: c.Cid(),
		Nid: c.NodeID,
		Uid: c.clientInfo.Account, // 用户名
	}
}
