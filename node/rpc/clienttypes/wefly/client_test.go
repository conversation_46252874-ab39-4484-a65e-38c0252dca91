package wefly

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

func Test_Client(t *testing.T) {
	config := &pb.CreateClientReq{
		Type: "wefly",
		Url:  "http://wefly.52tt.com",
	}

	var err error
	config.CustomFields, err = structpb.NewStruct(map[string]any{
		"account":  "linhui",
		"password": "TV@123123aa",
	})
	if err != nil {
		t.<PERSON><PERSON>("err: %s", err)
		t.FailNow()
	}

	c, err := NewClient(config)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("err: %s", err)
		t.<PERSON>ail<PERSON>ow()
	}

	callReq := &pb.ApiCallReq{
		Cid:    c.Cid(),
		Url:    "/api/wefly-ad-report/marketing-project-report/retentionPage",
		Method: "POST",
	}

	callReq.Headers = []*http.HttpHeader{
		{Key: "appId", Value: "1"},
	}

	bodyjson := `{"colName":"realCost","desc":true,"download":false,"totalDate":true,"groupByList":["sumDate","departmentIdList","marketingProject","marketingProjectTargetList","marketingProjectTypeList","marketingTask","ascribeTypeList","nickName","ttId","roomId","parentChannelIdList","channelIdList","adPlatformId","appType","cpIdStr","projectChargerIdList","taskChargerIdList"],"timeType":"d","marketingProject":"TT语音-赏金赛-王者","marketingProjectTargetList":["3"],"marketingProjectTypeList":["22"],"marketingTask":"TT语音-快手-快任务-王者赏金赛","ascribeTypeList":[],"tag":{"tagIds":[],"andTagId":false},"nickName":"","ttId":"","roomId":"","parentChannelIdList":[],"channelIdList":[],"adPlatformId":[85],"appType":[],"cpIdStr":"*********.tt_no_game","departmentIdList":[47],"projectChargerIdList":["1438387731603566594"],"taskChargerIdList":["1438387731603566594"],"bizField":"guest","startDate":"2023-12-08","endDate":"2023-12-13","limit":10,"page":1}`
	body := make(map[string]any)
	err = json.Unmarshal([]byte(bodyjson), &body)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}
	callReq.Body, err = structpb.NewValue(body)

	req, err := c.NewRequest(callReq)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	resp, err := c.Send(context.Background(), req)
	if err != nil {
		t.Errorf("err: %s", err)
		t.FailNow()
	}

	if resp.Status() != 200 {
		t.Errorf("resp status err: %d", resp.Status())
		t.FailNow()
	}

	t.Logf("resp body: %s", jsonx.MarshalToStringIgnoreError(resp.Body()))
}
