package wefly

const (
	LoginPath = "/api/blade-auth/oauth/token"

	ConstMethodPOST = "POST"

	ConstHeaderAuthorization = "Authorization"
	ConstHeaderCaptchaCode   = "Captcha-Code"
	ConstHeaderTenantId      = "Tenant-Id"
	ConstHeaderBladeAuth     = "Blade-Auth"

	ConstHeaderAuthorizationValue = "Basic d2VmbHk6d2VmbHl1WHJqSmtKb1M3ZW1IM29o"
	ConstHeaderCaptchaCodeValue   = "123456"
	ConstHeaderTenantIdValue      = "FLY"

	ConstKeyUsername  = "username"
	ConstKeyPassword  = "password"
	ConstKeyTenantId  = "tenantId"
	ConstKeyGrantType = "grant_type"
	ConstKeyScope     = "scope"

	ConstValueTenantIdFly       = "FLY"
	ConstValueGrantTypePassword = "password"
	ConstValueScopeAll          = "all"
)
