Name: rpc.node
Log:
  Encoding: plain
  Stat: false
ListenOn: 192.168.38.17:8081
Timeout: 0
MethodTimeouts:
  - FullMethod: /node.NodeService/UpdateProto
    Timeout: 5m30s

# gRPC服务端keepalive配置
ServerOptions:
  MaxConnectionAge: 0s               # 禁用连接年龄限制，0s表示无限制
  MaxConnectionIdle: 0s              # 禁用空闲连接超时，0s表示无限制
  MaxConnectionAgeGrace: 0s          # 禁用连接年龄宽限期
  KeepaliveTime: 30s                 # 服务端keepalive间隔
  KeepaliveTimeout: 5s               # keepalive超时时间
  KeepaliveMinTime: 10s              # 客户端keepalive最小间隔
  KeepalivePermitWithoutStream: true # 允许无流时keepalive

Redis:
  Key: rpc.node
  Host: 127.0.0.1:6379
  Type: node
  Pass:

ClientPool:
  MaxConsecutiveFailures: 5
  MaxIdleDuration: 30m

Proto:
  RootPath: ./git_data/protos
  Products:
    - ProductName: tt
      Branch: master
      Projects:
        - ProjectName: app
          GitURL: https://probe:<EMAIL>/tt-protocols/app.git
          Branch: master
          Dependencies:
            - Local:
                LocalPath: ./git_data/protos/googleapis
        - ProjectName: stellaris-api
          GitURL: https://probe:<EMAIL>/stellaris/api.git
          Branch: main
          Dependencies:
            - Local:
                LocalPath: ./git_data/protos/googleapis
    - ProductName: tt
      Branch: develop
      Projects:
        - ProjectName: app
          GitURL: https://probe:<EMAIL>/tt-protocols/app.git
          Branch: develop
          Dependencies:
            - Local:
                LocalPath: ./git_data/protos/googleapis
        - ProjectName: stellaris-api
          GitURL: https://probe:<EMAIL>/stellaris/api.git
          Branch: main
          Dependencies:
            - Local:
                LocalPath: ./git_data/protos/googleapis
    - ProductName: recommend
      Branch: master
      Projects:
        - ProjectName: rcmd
          GitURL: https://probe:<EMAIL>/root/rcmd_proto.git
          Branch: master
          ExcludePaths:
            - ./abtest
            - ./ai-partner-push
            - ./ai-voice
            - ./aigc
            - ./business-ai-partner
            - ./rcmd-ai-partner
            - ./rcmd_aigc_community
          Dependencies:
            - Local:
                LocalPath: ./git_data/protos/googleapis
            - Local:
                LocalPath: ./git_data/protos/protoc-gen-validate

#Limit:
#  - ProductName: tt
#    Environment: testing-login.ttyuyin.com
#    Pattern:     Login # 创建客户端时的登录接口，固定命名为`Login`
#    Requests:    1
#    Duration:    10s
#  - ProductName: tt
#    Pattern:     "447"
#    Requests:    2
#    Duration:    30s
#  - ProductName:
#    Environment: 10.65.251.7:8080
#    Pattern:     /get
#    Requests:    2
#    Duration:    30s
#    RetryTimes:  -1

Security:
  Enabled: true
  Headers:
    x-tt-security-timestamp: 1419379681024

Cleaner:
  InvalidClients: "3 * * * ?" # at three minutes past every hour
  InvalidUsers: "6 * * * ?" # at six minutes past every hour
  UnmanagedClients: "*/5 * * * ?" # at nine minutes past every half hour
  MaxRetentionDuration: 30m
