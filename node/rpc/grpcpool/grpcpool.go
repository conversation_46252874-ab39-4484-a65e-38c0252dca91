package grpcpool

import (
	"context"
	"encoding/binary"
	"hash/fnv"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
)

const initPoolSize = 1024

var ErrClientNotFound = errors.New("specific client not found")

type (
	item struct {
		client *tgrpc.Client
		target string
		conf   tgrpc.ClientConf
	}
	Pool struct {
		logx.Logger

		items map[uint64]*item
		lock  sync.RWMutex
		once  sync.Once
	}
)

func NewGRPCPool() *Pool {
	p := &Pool{
		Logger: logx.WithContext(context.Background()),

		items: make(map[uint64]*item, initPoolSize),
	}

	proc.AddShutdownListener(p.close)

	return p
}

func (p *Pool) Register(target string, conf tgrpc.ClientConf) {
	p.lock.Lock()
	defer p.lock.Unlock()

	key := keyOfTargetAndClientConf(target, conf)
	_, ok := p.items[key]
	if ok {
		return
	}

	p.items[key] = &item{
		client: tgrpc.NewClient(target, conf),
		target: target,
		conf:   conf,
	}
}

func (p *Pool) Get(target string, conf tgrpc.ClientConf) (*tgrpc.Client, error) {
	p.lock.RLock()
	defer p.lock.RUnlock()

	key := keyOfTargetAndClientConf(target, conf)
	v, ok := p.items[key]
	if !ok {
		return nil, ErrClientNotFound
	}

	return v.client, nil
}

func (p *Pool) close() {
	p.once.Do(
		func() {
			p.Info("closing the grpc pool")

			p.lock.Lock()
			defer p.lock.Unlock()

			for k, v := range p.items {
				if err := v.client.Close(); err != nil {
					p.Errorf("failed to close client, address: %s, error: %+v", k, err)
				} else {
					p.Infof("close client successfully, address: %s")
				}

				delete(p.items, k)
			}

			p.items = nil
		},
	)
}

func keyOfTargetAndClientConf(target string, conf tgrpc.ClientConf) uint64 {
	h := fnv.New64a()
	_, _ = h.Write([]byte(target))
	_ = binary.Write(h, binary.BigEndian, conf.DialTimeout)
	_ = binary.Write(h, binary.BigEndian, conf.KeepaliveTime)
	_ = binary.Write(h, binary.BigEndian, int64(conf.MaxMessageSize))
	_, _ = h.Write([]byte(conf.Authority))
	_, _ = h.Write([]byte(conf.UserAgent))
	_ = binary.Write(h, binary.BigEndian, conf.NoTLS)
	_ = binary.Write(h, binary.BigEndian, conf.InsecureSkipVerify)
	_, _ = h.Write([]byte(conf.RootCAFile))
	_, _ = h.Write([]byte(conf.ClientCertFile))
	_, _ = h.Write([]byte(conf.ClientKeyFile))
	return h.Sum64()
}
