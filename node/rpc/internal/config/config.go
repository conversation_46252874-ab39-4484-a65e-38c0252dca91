package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/cleaner"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
)

type Config struct {
	zrpc.RpcServerConf
	ServerOptions common.ServerOptions `json:",optional"` // gRPC服务端keepalive选项

	ClientPool clientpool.Config
	Proto      proto.InitProtoConfig
	Limit      []limiter.RateLimitConfig `json:",optional,omitempty"`
	Security   security.Config           `json:",optional,omitempty"`
	Cleaner    cleaner.Config            `json:",optional,omitempty"`
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
