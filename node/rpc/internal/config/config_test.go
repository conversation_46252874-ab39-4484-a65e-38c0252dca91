package config

import (
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
)

type TestFoo struct {
	Foo string
	Bar TestBar
}

type TestBar struct {
	Name     string
	Children []TestBar
}

func TestConfigLoad(t *testing.T) {
	content := []byte(`Foo: test
Bar:
  Name: bar1
  Children:
    - Name: bar11
    - Name: bar12
      Children:
        - Name: bar121
`)

	var c TestFoo
	if err := conf.LoadFromYamlBytes(content, &c); err != nil {
		t.Fatal(err)
	}
}
