package nodeservicelogic

import (
	"context"
	"reflect"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/registry"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

var (
	errNotFoundClientInfo  = errors.New("client info not found in context")
	errNotFoundCustomLogic = errors.New("custom logic not found")
)

type ApiCallLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	createClientLogic *CreateClientLogic
}

func NewApiCallLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApiCallLogic {
	return &ApiCallLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		createClientLogic: NewCreateClientLogic(ctx, svcCtx),
	}
}

// ApiCall 接口调用
func (l *ApiCallLogic) ApiCall(in *pb.ApiCallReq) (out *pb.ApiCallResp, err error) {
	c, err := clientpool.Get(
		&pb.SearchClient{
			Condition: &pb.SearchClient_Cid{
				Cid: in.GetCid(),
			},
		},
	)
	if err != nil && !errors.Is(err, clientpool.ErrClientNotFound) {
		return nil, err
	} else if errors.Is(err, clientpool.ErrClientNotFound) {
		ci, e := l.getClientInfoFromContext()
		if e != nil || ci == nil {
			return nil, err
		}

		c, e = l.tryToCreateClient(in, ci.GetCreateInfo())
		if e != nil || c == nil {
			return nil, err
		}
	}

	defer func() {
		if c != nil {
			// 客户端完成一次请求
			c.Requested()

			if out != nil {
				// 返回最新的客户端信息
				out.ClientInfo = c.GetClientInfo()
			}

			setToTrailerByClient(l.ctx, c)
		}
	}()

	// 通过安全处理器设置请求头
	in.Headers = security.HandleHTTPPBHeaders(in.GetHeaders())

	// 尝试按自定义方式调用业务接口
	out, err = l.tryToCallByCustomLogic(in, c)
	if err == nil {
		return out, err
	} else if !errors.Is(err, errNotFoundCustomLogic) {
		return nil, err
	}

	// 按通用方式调用业务接口
	return l.callByCommonLogic(in, c)
}

func (l *ApiCallLogic) getClientInfoFromContext() (*commonpb.ClientInfo, error) {
	value := metadata.ValueFromIncomingContext(l.ctx, common.ConstClientInfoMetadataKey)
	if len(value) == 0 || len(value[0]) == 0 {
		return nil, errNotFoundClientInfo
	}

	ci := &commonpb.ClientInfo{}
	if err := protobuf.UnmarshalJSONFromString(value[0], ci); err != nil {
		return nil, err
	}

	return ci, nil
}

func (l *ApiCallLogic) tryToCreateClient(in *pb.ApiCallReq, createInfo *commonpb.CreateInfo) (
	clienttypes.IClient, error,
) {
	return l.createClientLogic.CreateClientForInternal(
		&pb.CreateClientReq{
			Type:         createInfo.GetType(),
			Url:          createInfo.GetUrl(),
			CustomFields: createInfo.GetCustomFields(),
		}, clienttypes.WithClientID(in.GetCid()),
	)
}

func (l *ApiCallLogic) tryToCallByCustomLogic(in *pb.ApiCallReq, c clienttypes.IClient) (
	out *pb.ApiCallResp, err error,
) {
	defer func() {
		if r := recover(); r != nil {
			if err != nil {
				err = errors.Wrapf(
					err,
					"get a panic while calling custom logic, req: %s, error: %v",
					protobuf.MarshalJSONToStringIgnoreError(in), r,
				)
			} else {
				err = errors.Errorf(
					"get a panic while calling custom logic, req: %s, error: %v",
					protobuf.MarshalJSONToStringIgnoreError(in), r,
				)
			}
		}
	}()

	// 如果客户端实现了的`Logic`借口，则直接调用该借口的方法
	if cl, ok := c.(clienttypes.Logic); ok {
		return cl.CustomLogic(l.ctx, in)
	}

	// 如果`LogicRegistry`中找到对应的自定义逻辑及方法，则通过反射的方式调用该方法
	product, logic, method := registry.SplitURL(in.GetUrl())
	if product == "" || logic == "" || method == "" {
		return nil, errNotFoundCustomLogic
	}

	fn, ok := registry.GetLogicFunc(product, logic)
	if !ok {
		return nil, errNotFoundCustomLogic
	}

	cl, err := fn(l.ctx, c)
	if err != nil {
		return nil, err
	}

	m := reflect.ValueOf(cl).MethodByName(method)
	if !m.IsValid() {
		return nil, errNotFoundCustomLogic
	} else if m.Kind() != reflect.Func {
		return nil, errors.Errorf(
			"the method is not a function, method: %s, kind: %s",
			method, m.Kind().String(),
		)
	}

	vs := m.Call([]reflect.Value{reflect.ValueOf(in)})
	if len(vs) != 2 {
		return nil, errors.Errorf(
			"the number of output parameters for the method is incorrect, method: %s, expected 2, but got %d",
			method, len(vs),
		)
	}

	ret0 := vs[0].Interface()
	out, ok = ret0.(*pb.ApiCallResp)
	if !ok {
		return nil, errors.Errorf(
			"the type of first output parameter for the method is incorrect, method: %s, expected *pb.ApiCallResp, but got %T",
			method, ret0,
		)
	}

	ret1 := vs[1].Interface()
	if ret1 != nil {
		err, ok = ret1.(error)
		if !ok {
			return nil, errors.Errorf(
				"the type of second output parameter for the method is incorrect, method: %s, expected *pb.ApiCallResp, but got %T",
				method, ret1,
			)
		}
	} else {
		err = nil
	}

	return out, err
}

func (l *ApiCallLogic) callByCommonLogic(in *pb.ApiCallReq, c clienttypes.IClient) (out *pb.ApiCallResp, err error) {
	return clienttypes.NewCommonLogic(l.ctx, c).Call(in)
}
