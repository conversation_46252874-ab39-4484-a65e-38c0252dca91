package nodeservicelogic

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/registry"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type CreateClientLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateClientLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateClientLogic {
	return &CreateClientLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateClientLogic) CreateClient(in *pb.CreateClientReq) (out *pb.CreateClientResp, err error) {
	var c clienttypes.IClient
	defer func() {
		if c != nil {
			setToTrailerByClient(l.ctx, c)
		}
	}()

	c, err = l.CreateClientForInternal(in)
	if err != nil {
		return nil, err
	}

	return &pb.CreateClientResp{Item: c.GetClientInfo()}, nil
}

func (l *CreateClientLogic) CreateClientForInternal(
	in *pb.CreateClientReq, opts ...clienttypes.CreateClientOption,
) (clienttypes.IClient, error) {
	c, err := clientpool.Get(
		&pb.SearchClient{
			Condition: &pb.SearchClient_Create{
				Create: in,
			},
		},
	)

	if err != nil && !errors.Is(err, clientpool.ErrClientNotFound) {
		return nil, err
	} else if errors.Is(err, clientpool.ErrClientNotFound) {
		c, err = registry.NewClient(in, opts...)
		if err != nil {
			return nil, err
		}

		if v, ok := c.(clienttypes.Auth); ok {
			ret, err := limiter.Allow(l.ctx, c.ProductName(), c.Environment(), "Login")
			if err != nil {
				return nil, err
			} else if ret != nil {
				// set the result of rate limit to trailer
				limiter.SetToTrailer(l.ctx, *ret)

				if ret.Allowed < 1 {
					return nil, limiter.LimitExceededErr
				}
			}

			if err = v.Login(l.ctx); err != nil {
				return nil, err
			}
		}

		clientpool.Set(c.Cid(), c)
		l.Infof("create client completed, cid: %s", c.Cid())
	} else {
		// update the last requested time to avoid the client being evicted before the next request comes
		c.UpdateLastRequestedAt(c.LastRequestedAt().Add(time.Minute))
		l.Infof("client already exists, no need to create, cid: %s", c.Cid())
	}

	return c, nil
}
