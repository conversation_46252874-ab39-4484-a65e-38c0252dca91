package nodeservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type DeleteClientLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteClientLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteClientLogic {
	return &DeleteClientLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteClientLogic) DeleteClient(in *pb.DeleteClientReq) (out *pb.DeleteClientResp, err error) {
	ci := clientpool.Del(in.GetCid())
	if ci == nil {
		l.Warnf("not found the client to be deleted, cid: %s", in.GetCid())
		return &pb.DeleteClientResp{
			Item: &commonpb.ClientInfo{
				Cid: in.GetCid(),
			},
		}, nil
	}
	l.Infof("delete client completed, cid: %s", ci.GetCid())

	defer func() {
		setToTrailerByClientInfo(l.ctx, ci)
	}()

	return &pb.DeleteClientResp{
		Item: ci,
	}, nil
}
