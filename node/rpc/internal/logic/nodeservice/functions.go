package nodeservicelogic

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes"
)

func setToTrailerByClient(ctx context.Context, c clienttypes.IClient) {
	if c == nil {
		return
	}

	_ = grpc.SetTrailer(
		ctx, metadata.New(
			map[string]string{
				common.ProductNameHeaderKey: string(c.ProductName()),
				common.ClientTypeHeaderKey:  string(c.ClientType()),
			},
		),
	)
}

func setToTrailerByClientInfo(ctx context.Context, ci *pb.ClientInfo) {
	if ci == nil {
		return
	}

	_ = grpc.SetTrailer(
		ctx, metadata.New(
			map[string]string{
				common.ProductNameHeaderKey: ci.GetProduct(),
				common.ClientTypeHeaderKey:  ci.GetType(),
			},
		),
	)
}
