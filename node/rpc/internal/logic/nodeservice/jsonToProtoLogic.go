package nodeservicelogic

import (
	"context"
	"encoding/base64"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type JsonToProtoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewJsonToProtoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *JsonToProtoLogic {
	return &JsonToProtoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// JsonToProto json数据转换成proto字符串
func (l *JsonToProtoLogic) JsonToProto(in *pb.JsonToProtoReq) (resp *pb.JsonToProtoResp, err error) {
	name := in.GetProductName()
	pm, ok := proto.GetProtoManager(types.ProductName(name))
	if !ok {
		return nil, errors.Errorf("invalid product name: %s", name)
	}

	j, err := jsonx.Marshal(in.GetData())
	if err != nil {
		return nil, err
	}

	bs, err := pm.UnmarshalPB(in.GetMessage(), j)
	if err != nil {
		return nil, err
	}

	s := base64.URLEncoding.EncodeToString(bs)

	return &pb.JsonToProtoResp{ProtoString: s}, nil
}
