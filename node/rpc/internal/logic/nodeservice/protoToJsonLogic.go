package nodeservicelogic

import (
	"context"
	"encoding/base64"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type ProtoToJsonLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProtoToJsonLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProtoToJsonLogic {
	return &ProtoToJsonLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ProtoToJson proto字符串转换成json数据
func (l *ProtoToJsonLogic) ProtoToJson(in *pb.ProtoToJsonReq) (resp *pb.ProtoToJsonResp, err error) {
	name := in.GetProductName()
	pm, ok := proto.GetProtoManager(types.ProductName(name))
	if !ok {
		return nil, errors.Errorf("invalid product name: %s", name)
	}

	ds, err := base64.URLEncoding.DecodeString(in.GetData())
	if err != nil {
		return nil, err
	}

	p, err := pm.MarshalJSONPB(in.Message, ds)
	if err != nil {
		return nil, err
	}

	var j any
	err = jsonx.Unmarshal(p, &j)
	if err != nil {
		return nil, err
	}

	data, err := protobuf.NewValue(j)
	if err != nil {
		return nil, err
	}

	return &pb.ProtoToJsonResp{JsonData: data}, nil
}
