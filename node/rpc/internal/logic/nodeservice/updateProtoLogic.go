package nodeservicelogic

import (
	"context"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type UpdateProtoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateProtoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateProtoLogic {
	return &UpdateProtoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// UpdateProto 更新`proto`项目
func (l *UpdateProtoLogic) UpdateProto(in *pb.UpdateProtoReq) (out *pb.UpdateProtoResp, err error) {
	out = &pb.UpdateProtoResp{
		ProductName: in.GetProductName(),
		Branch:      in.GetBranch(),
	}

	c, err := proto.ReloadProtoManager(l.ctx, convertToProductConfig(in))
	if err != nil {
		return nil, err
	}

	out.Projects = make([]*pb.UpdateProtoResp_ProjectInfo, 0, len(c.Projects))
	for _, p := range c.Projects {
		out.Projects = append(
			out.Projects, &pb.UpdateProtoResp_ProjectInfo{
				ProjectName: p.ProjectName,
				Branch:      p.Branch,
				Commit:      convertToCommit(p.Commit()),
			},
		)
	}

	return out, nil
}

func convertToProductConfig(in *pb.UpdateProtoReq) proto.ProductConfig {
	projects := make([]proto.ProjectConfig, 0, len(in.GetProjects()))
	for _, p := range in.GetProjects() {
		projects = append(projects, convertToProjectConfig(p))
	}

	return proto.ProductConfig{
		ProductName: types.ProductName(in.GetProductName()),
		Branch:      in.GetBranch(),
		Projects:    projects,
	}
}

func convertToProjectConfig(in *pb.UpdateProtoReq_ProjectConfig) proto.ProjectConfig {
	dependencies := make([]proto.DependenceConfig, 0, len(in.GetDependencies()))
	for _, dep := range in.GetDependencies() {
		dependencies = append(dependencies, convertToDependenceConfig(dep))
	}

	return proto.ProjectConfig{
		ProjectName: in.GetProjectName(),
		GitConfig: proto.GitConfig{
			GitURL:     in.GetGitUrl(),
			Branch:     in.GetBranch(),
			ImportPath: in.GetImportPath(),
		},
		ExcludePaths: in.GetExcludePaths(),
		ExcludeFiles: in.GetExcludeFiles(),
		Dependencies: dependencies,
	}
}

func convertToDependenceConfig(in *pb.UpdateProtoReq_DependenceConfig) proto.DependenceConfig {
	switch v := in.GetConfig().(type) {
	case *pb.UpdateProtoReq_DependenceConfig_Git:
		return proto.DependenceConfig{
			Git: proto.GitDependenceConfig{
				GitConfig: proto.GitConfig{
					GitURL:     v.Git.GetGitUrl(),
					Branch:     v.Git.GetBranch(),
					ImportPath: v.Git.GetImportPath(),
				},
			},
		}
	case *pb.UpdateProtoReq_DependenceConfig_LocalPath:
		return proto.DependenceConfig{
			Local: proto.LocalDependenceConfig{
				LocalPath: v.LocalPath,
			},
		}
	default:
		return proto.DependenceConfig{}
	}
}

func convertToCommit(in *proto.Commit) *pb.UpdateProtoResp_Commit {
	if in == nil {
		return nil
	}

	return &pb.UpdateProtoResp_Commit{
		Hash:    in.Hash.String(),
		Author:  in.Author.String(),
		Date:    in.Author.When.Format(object.DateFormat),
		Message: in.Message,
	}
}
