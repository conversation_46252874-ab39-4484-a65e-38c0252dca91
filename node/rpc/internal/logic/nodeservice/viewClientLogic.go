package nodeservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type ViewClientLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewViewClientLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewClientLogic {
	return &ViewClientLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ViewClient 查看客户端
func (l *ViewClientLogic) ViewClient(in *pb.ViewClientReq) (out *pb.ViewClientResp, err error) {
	c, err := clientpool.Get(
		&pb.SearchClient{
			Condition: &pb.SearchClient_Cid{
				Cid: in.GetCid(),
			},
		},
	)
	if err != nil {
		return nil, err
	}

	defer func() {
		setToTrailerByClient(l.ctx, c)
	}()

	return &pb.ViewClientResp{
		Item: c.GetClientInfo(),
	}, nil
}
