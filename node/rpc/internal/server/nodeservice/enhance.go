package server

import (
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

func (s *NodeServiceServer) GetTimeoutByFullMethod(fullMethod string, defaultTimeout time.Duration) time.Duration {
	if fullMethod == pb.NodeService_UpdateProto_FullMethodName {
		return common.ConstUpdateProtoTimeout
	}

	return defaultTimeout
}
