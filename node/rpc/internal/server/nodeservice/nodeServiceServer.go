// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: node.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	nodeservicelogic "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/logic/nodeservice"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
)

type NodeServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedNodeServiceServer
}

func NewNodeServiceServer(svcCtx *svc.ServiceContext) *NodeServiceServer {
	return &NodeServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateClient 创建客户端
func (s *NodeServiceServer) CreateClient(ctx context.Context, in *pb.CreateClientReq) (*pb.CreateClientResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewCreateClientLogic(ctx, s.svcCtx)

	return l.CreateClient(in)
}

// DeleteClient 删除客户端
func (s *NodeServiceServer) DeleteClient(ctx context.Context, in *pb.DeleteClientReq) (*pb.DeleteClientResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewDeleteClientLogic(ctx, s.svcCtx)

	return l.DeleteClient(in)
}

// ViewClient 查看客户端
func (s *NodeServiceServer) ViewClient(ctx context.Context, in *pb.ViewClientReq) (*pb.ViewClientResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewViewClientLogic(ctx, s.svcCtx)

	return l.ViewClient(in)
}

// ApiCall 接口调用
func (s *NodeServiceServer) ApiCall(ctx context.Context, in *pb.ApiCallReq) (*pb.ApiCallResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewApiCallLogic(ctx, s.svcCtx)

	return l.ApiCall(in)
}

// UpdateProto 更新`proto`项目
func (s *NodeServiceServer) UpdateProto(ctx context.Context, in *pb.UpdateProtoReq) (*pb.UpdateProtoResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewUpdateProtoLogic(ctx, s.svcCtx)

	return l.UpdateProto(in)
}

// JsonToProto `json`数据转换成`proto`字符串
func (s *NodeServiceServer) JsonToProto(ctx context.Context, in *pb.JsonToProtoReq) (*pb.JsonToProtoResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewJsonToProtoLogic(ctx, s.svcCtx)

	return l.JsonToProto(in)
}

// ProtoToJson `proto`字符串转换成`json`数据
func (s *NodeServiceServer) ProtoToJson(ctx context.Context, in *pb.ProtoToJsonReq) (*pb.ProtoToJsonResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := nodeservicelogic.NewProtoToJsonLogic(ctx, s.svcCtx)

	return l.ProtoToJson(in)
}
