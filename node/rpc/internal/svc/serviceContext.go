package svc

import (
	"github.com/bufbuild/protovalidate-go"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	Validator protovalidate.Validator
}

func NewServiceContext(c config.Config) *ServiceContext {
	mds := make([]protoreflect.MessageDescriptor, 0, protoregistry.GlobalTypes.NumMessages())
	protoregistry.GlobalTypes.RangeMessages(
		func(mt protoreflect.MessageType) bool {
			mds = append(mds, mt.Descriptor())
			return true
		},
	)

	v, _ := protovalidate.New(protovalidate.WithMessageDescriptors(mds...), protovalidate.WithDisableLazy())

	return &ServiceContext{
		Config: c,

		Redis: redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),

		Validator: v,
	}
}
