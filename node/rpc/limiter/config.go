package limiter

import (
	"regexp"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type RateLimitConfig struct {
	KeyConfig
	MatchConfig
	RateConfig
	HandleConfig
}

type KeyConfig struct {
	ProductName types.ProductName
	Environment string `json:",optional"`
}

type MatchConfig struct {
	Pattern   string    `json:",default=/"`
	MatchType MatchType `json:",default=exact,options=exact|prefix|regex"`
}

type RateConfig struct {
	Requests int64         `json:",default=100"`
	Duration time.Duration `json:",default=1s"`
}

type HandleConfig struct {
	RetryTimes int64 `json:",default=0"` // 重试次数，0（不重试，即直接返回），-1（不断重试，直到超时）
}

type MatchType string

const (
	exact  MatchType = "exact"
	prefix MatchType = "prefix"
	regex  MatchType = "regex"
)

type Match struct {
	MatchConfig

	re *regexp.Regexp
}

func (c Match) IsMatch(item string) bool {
	switch c.MatchType {
	case exact:
		return c.Pattern == item
	case prefix:
		return strings.HasPrefix(item, c.Pattern)
	case regex:
		if c.re != nil {
			return c.re.MatchString(item)
		}

		ok, err := regexp.MatchString(c.Pattern, item)
		if err != nil {
			return false
		}

		return ok
	default:
		return false
	}
}
