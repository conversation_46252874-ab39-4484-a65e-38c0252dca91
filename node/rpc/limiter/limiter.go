package limiter

import (
	"context"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislimiter"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	constSep = "::"

	constGlobal = "global"
)

var (
	once    sync.Once
	limiter *Limiter

	LimitExceededErr = status.Error(codes.ResourceExhausted, "limit exceeded")
)

// InitRateLimiter init the rate limiter by rate limit config
func InitRateLimiter(cs []RateLimitConfig, rc redis.RedisConf) {
	once.Do(
		func() {
			limiter = NewLimiter(cs, rc)
		},
	)
}

// Allow represents whether the current request is allowed.
// When `*redislimiter.Result` is nil, means no rate limit.
func Allow(ctx context.Context, productName types.ProductName, environment, target string) (
	*redislimiter.Result, error,
) {
	return limiter.Take(ctx, productName, environment, target)
}

func SetToTrailer(ctx context.Context, result redislimiter.Result) {
	_ = grpc.SetTrailer(
		ctx, metadata.New(
			map[string]string{
				common.RateLimitAllowedHeaderKey:    strconv.FormatInt(result.Allowed, 10),
				common.RateLimitRemainingHeaderKey:  strconv.FormatInt(result.Remaining, 10),
				common.RateLimitRetryAfterHeaderKey: strconv.FormatInt(result.RetryAfter.Milliseconds(), 10),
				common.RateLimitResetAfterHeaderKey: strconv.FormatInt(result.ResetAfter.Milliseconds(), 10),
			},
		),
	)
}

type Limiter struct {
	l *redislimiter.Limiter

	lv1 map[string][]rateConfig // 环境级（产品指定环境才适用的配置）
	lv2 map[string][]rateConfig // 产品级（产品下各环境都适用的配置）
	lv3 map[string][]rateConfig // 全局（各产品都适用的配置）
}

type rateConfig struct {
	match Match
	rate  redislimiter.Rate
	retry int64
}

func NewLimiter(cs []RateLimitConfig, rc redis.RedisConf) *Limiter {
	l := &Limiter{
		l: redislimiter.NewLimiter(qetredis.NewClient(rc)),

		lv1: make(map[string][]rateConfig, len(cs)),
		lv2: make(map[string][]rateConfig, len(cs)),
		lv3: make(map[string][]rateConfig, len(cs)),
	}

	reCache := make(map[string]*regexp.Regexp, len(cs))
	for _, c := range cs {
		var (
			key   string
			cache map[string][]rateConfig
		)
		if c.Environment != "" { // 支持非产品客户端，所以这里不判断`ProductName`是否为空
			key = string(c.ProductName) + constSep + c.Environment
			cache = l.lv1
		} else if c.ProductName != "" {
			key = string(c.ProductName)
			cache = l.lv2
		} else {
			key = constGlobal
			cache = l.lv3
		}

		var (
			re *regexp.Regexp
			ok bool
		)
		if c.MatchType == regex {
			re, ok = reCache[c.Pattern]
			if !ok {
				re = regexp.MustCompile(c.Pattern)
				reCache[c.Pattern] = re
			}
		}

		cache[key] = append(
			cache[key], rateConfig{
				match: Match{
					MatchConfig: c.MatchConfig,
					re:          re,
				},
				rate:  redislimiter.PerDuration(c.Requests, c.Duration),
				retry: c.RetryTimes,
			},
		)
	}

	return l
}

func (l *Limiter) Take(
	ctx context.Context, productName types.ProductName, environment, target string,
) (*redislimiter.Result, error) {
	var (
		key    string
		config rateConfig
		found  bool
	)

	for _, item := range []struct {
		rcs map[string][]rateConfig
		key string
	}{
		{
			rcs: l.lv1,
			key: string(productName) + constSep + environment,
		},
		{
			rcs: l.lv2,
			key: string(productName),
		},
		{
			rcs: l.lv3,
			key: constGlobal,
		},
	} {
		config, found = find(item.rcs, item.key, target)
		if found {
			key = item.key
			break
		}
	}

	if found {
		var times int64
		for {
			times += 1

			result, err := l.l.Allow(
				ctx, strings.Join(
					[]string{
						key, string(config.match.MatchType), config.match.Pattern,
						strconv.FormatInt(config.rate.Limit(), 10), config.rate.Period().String(),
					}, constSep,
				), config.rate,
			)
			if err != nil || result.Allowed >= 1 || config.retry == 0 || (config.retry > 0 && times > config.retry) {
				// error or allowed or no retry or reach retry times
				return result, err
			}

			if result != nil && result.RetryAfter > 0 {
				time.Sleep(result.RetryAfter)
			}
		}
	}

	// no need for rate limiting
	return nil, nil
}

func find(rcs map[string][]rateConfig, key, target string) (config rateConfig, found bool) {
	for k, vs := range rcs {
		if k != key {
			continue
		}

		for _, v := range vs {
			if v.match.IsMatch(target) {
				config = v
				found = true
				return
			}
		}
	}

	return
}
