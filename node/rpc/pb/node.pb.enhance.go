package pb

import (
	"fmt"
	"slices"
)

func (x *SearchClient) Key() (key string) {
	if x.GetCid() != "" {
		return x.GetCid()
	}

	return x.GetCreate().Key()
}

func (x *CreateClientReq) Key() (key string) {
	if x == nil {
		return ""
	}

	fields := x.<PERSON>ustomFields().GetFields()
	keys := make([]string, 0, len(fields))
	for k := range fields {
		keys = append(keys, k)
	}

	slices.Sort[[]string, string](keys)
	for _, k := range keys {
		v, ok := fields[k]
		if !ok {
			continue
		}

		vv := v.AsInterface()
		if key == "" {
			key = fmt.Sprintf("%s=%v", k, vv)
		} else {
			key = fmt.Sprintf("%s:%s=%v", key, k, vv)
		}
	}

	typ := x.GetType()
	url := x.GetUrl()
	if key == "" {
		key = fmt.Sprintf("type=%s:url=%s", typ, url)
	} else {
		key = fmt.Sprintf("type=%s:url=%s:%s", typ, url, key)
	}

	return key
}
