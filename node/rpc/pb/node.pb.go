// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: node/node.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	http "google.golang.org/genproto/googleapis/rpc/http"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"

	pb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SearchClient 搜索客户端条件
type SearchClient struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Condition:
	//
	//	*SearchClient_Cid
	//	*SearchClient_Create
	Condition     isSearchClient_Condition `protobuf_oneof:"condition"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchClient) Reset() {
	*x = SearchClient{}
	mi := &file_node_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchClient) ProtoMessage() {}

func (x *SearchClient) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchClient.ProtoReflect.Descriptor instead.
func (*SearchClient) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{0}
}

func (x *SearchClient) GetCondition() isSearchClient_Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *SearchClient) GetCid() string {
	if x != nil {
		if x, ok := x.Condition.(*SearchClient_Cid); ok {
			return x.Cid
		}
	}
	return ""
}

func (x *SearchClient) GetCreate() *CreateClientReq {
	if x != nil {
		if x, ok := x.Condition.(*SearchClient_Create); ok {
			return x.Create
		}
	}
	return nil
}

type isSearchClient_Condition interface {
	isSearchClient_Condition()
}

type SearchClient_Cid struct {
	Cid string `protobuf:"bytes,1,opt,name=cid,proto3,oneof"` // 通过客户端ID查找客户端
}

type SearchClient_Create struct {
	Create *CreateClientReq `protobuf:"bytes,2,opt,name=create,proto3,oneof"` // 通过创建客户端的请求信息查找客户端
}

func (*SearchClient_Cid) isSearchClient_Condition() {}

func (*SearchClient_Create) isSearchClient_Condition() {}

type CreateClientReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                     // 客户端类型
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`                                       // 服务端连接地址
	CustomFields  *structpb.Struct       `protobuf:"bytes,3,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"` // 客户端的请求自定义字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateClientReq) Reset() {
	*x = CreateClientReq{}
	mi := &file_node_node_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateClientReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClientReq) ProtoMessage() {}

func (x *CreateClientReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClientReq.ProtoReflect.Descriptor instead.
func (*CreateClientReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{1}
}

func (x *CreateClientReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateClientReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreateClientReq) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

type CreateClientResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *pb.ClientInfo         `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateClientResp) Reset() {
	*x = CreateClientResp{}
	mi := &file_node_node_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateClientResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClientResp) ProtoMessage() {}

func (x *CreateClientResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClientResp.ProtoReflect.Descriptor instead.
func (*CreateClientResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{2}
}

func (x *CreateClientResp) GetItem() *pb.ClientInfo {
	if x != nil {
		return x.Item
	}
	return nil
}

type DeleteClientReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cid           string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"` // 客户端ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteClientReq) Reset() {
	*x = DeleteClientReq{}
	mi := &file_node_node_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteClientReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClientReq) ProtoMessage() {}

func (x *DeleteClientReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClientReq.ProtoReflect.Descriptor instead.
func (*DeleteClientReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteClientReq) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

type DeleteClientResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *pb.ClientInfo         `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteClientResp) Reset() {
	*x = DeleteClientResp{}
	mi := &file_node_node_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteClientResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClientResp) ProtoMessage() {}

func (x *DeleteClientResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClientResp.ProtoReflect.Descriptor instead.
func (*DeleteClientResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteClientResp) GetItem() *pb.ClientInfo {
	if x != nil {
		return x.Item
	}
	return nil
}

type ViewClientReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cid           string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"` // 客户端ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ViewClientReq) Reset() {
	*x = ViewClientReq{}
	mi := &file_node_node_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ViewClientReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewClientReq) ProtoMessage() {}

func (x *ViewClientReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewClientReq.ProtoReflect.Descriptor instead.
func (*ViewClientReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{5}
}

func (x *ViewClientReq) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

type ViewClientResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *pb.ClientInfo         `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ViewClientResp) Reset() {
	*x = ViewClientResp{}
	mi := &file_node_node_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ViewClientResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewClientResp) ProtoMessage() {}

func (x *ViewClientResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewClientResp.ProtoReflect.Descriptor instead.
func (*ViewClientResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{6}
}

func (x *ViewClientResp) GetItem() *pb.ClientInfo {
	if x != nil {
		return x.Item
	}
	return nil
}

type ApiCallReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cid           string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"`                                       // 客户端ID
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`                                       // 请求路径
	Method        string                 `protobuf:"bytes,3,opt,name=method,proto3" json:"method,omitempty"`                                 // 请求方法
	Headers       []*http.HttpHeader     `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty"`                               // 请求头
	Body          *structpb.Value        `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`                                     // 请求体
	Authority     string                 `protobuf:"bytes,6,opt,name=authority,proto3" json:"authority,omitempty"`                           // gRPC服务器名称
	CustomFields  *structpb.Struct       `protobuf:"bytes,7,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"` // 客户端的请求自定义字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiCallReq) Reset() {
	*x = ApiCallReq{}
	mi := &file_node_node_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiCallReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiCallReq) ProtoMessage() {}

func (x *ApiCallReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiCallReq.ProtoReflect.Descriptor instead.
func (*ApiCallReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{7}
}

func (x *ApiCallReq) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *ApiCallReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ApiCallReq) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ApiCallReq) GetHeaders() []*http.HttpHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *ApiCallReq) GetBody() *structpb.Value {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *ApiCallReq) GetAuthority() string {
	if x != nil {
		return x.Authority
	}
	return ""
}

func (x *ApiCallReq) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

type ApiCallResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientInfo    *pb.ClientInfo         `protobuf:"bytes,1,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	CustomFields  *structpb.Struct       `protobuf:"bytes,2,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	CallResp      *ApiCallResp_CallResp  `protobuf:"bytes,3,opt,name=call_resp,json=callResp,proto3" json:"call_resp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiCallResp) Reset() {
	*x = ApiCallResp{}
	mi := &file_node_node_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiCallResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiCallResp) ProtoMessage() {}

func (x *ApiCallResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiCallResp.ProtoReflect.Descriptor instead.
func (*ApiCallResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{8}
}

func (x *ApiCallResp) GetClientInfo() *pb.ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

func (x *ApiCallResp) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *ApiCallResp) GetCallResp() *ApiCallResp_CallResp {
	if x != nil {
		return x.CallResp
	}
	return nil
}

type UpdateProtoReq struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	ProductName   string                          `protobuf:"bytes,1,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"` // 产品名称
	Branch        string                          `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`                              // 分支名称
	Projects      []*UpdateProtoReq_ProjectConfig `protobuf:"bytes,3,rep,name=projects,proto3" json:"projects,omitempty"`                          // 项目配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoReq) Reset() {
	*x = UpdateProtoReq{}
	mi := &file_node_node_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoReq) ProtoMessage() {}

func (x *UpdateProtoReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoReq.ProtoReflect.Descriptor instead.
func (*UpdateProtoReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateProtoReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *UpdateProtoReq) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *UpdateProtoReq) GetProjects() []*UpdateProtoReq_ProjectConfig {
	if x != nil {
		return x.Projects
	}
	return nil
}

type UpdateProtoResp struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	ProductName   string                         `protobuf:"bytes,1,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"` // 产品名称
	Branch        string                         `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`                              // 分支名称
	Projects      []*UpdateProtoResp_ProjectInfo `protobuf:"bytes,3,rep,name=projects,proto3" json:"projects,omitempty"`                          // 项目信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoResp) Reset() {
	*x = UpdateProtoResp{}
	mi := &file_node_node_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoResp) ProtoMessage() {}

func (x *UpdateProtoResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoResp.ProtoReflect.Descriptor instead.
func (*UpdateProtoResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateProtoResp) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *UpdateProtoResp) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *UpdateProtoResp) GetProjects() []*UpdateProtoResp_ProjectInfo {
	if x != nil {
		return x.Projects
	}
	return nil
}

type JsonToProtoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductName   string                 `protobuf:"bytes,1,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"` // 产品名称
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                            // 目标信息名称
	Data          *structpb.Value        `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                  // 待转换数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JsonToProtoReq) Reset() {
	*x = JsonToProtoReq{}
	mi := &file_node_node_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JsonToProtoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JsonToProtoReq) ProtoMessage() {}

func (x *JsonToProtoReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JsonToProtoReq.ProtoReflect.Descriptor instead.
func (*JsonToProtoReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{11}
}

func (x *JsonToProtoReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *JsonToProtoReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JsonToProtoReq) GetData() *structpb.Value {
	if x != nil {
		return x.Data
	}
	return nil
}

type JsonToProtoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProtoString   string                 `protobuf:"bytes,1,opt,name=proto_string,json=protoString,proto3" json:"proto_string,omitempty"` // 转换后的数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JsonToProtoResp) Reset() {
	*x = JsonToProtoResp{}
	mi := &file_node_node_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JsonToProtoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JsonToProtoResp) ProtoMessage() {}

func (x *JsonToProtoResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JsonToProtoResp.ProtoReflect.Descriptor instead.
func (*JsonToProtoResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{12}
}

func (x *JsonToProtoResp) GetProtoString() string {
	if x != nil {
		return x.ProtoString
	}
	return ""
}

type ProtoToJsonReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductName   string                 `protobuf:"bytes,1,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"` // 产品名称
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                            // 目标信息名称
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                  // 待转换数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtoToJsonReq) Reset() {
	*x = ProtoToJsonReq{}
	mi := &file_node_node_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtoToJsonReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtoToJsonReq) ProtoMessage() {}

func (x *ProtoToJsonReq) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtoToJsonReq.ProtoReflect.Descriptor instead.
func (*ProtoToJsonReq) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{13}
}

func (x *ProtoToJsonReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ProtoToJsonReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ProtoToJsonReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type ProtoToJsonResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JsonData      *structpb.Value        `protobuf:"bytes,1,opt,name=json_data,json=jsonData,proto3" json:"json_data,omitempty"` // 转换后的数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtoToJsonResp) Reset() {
	*x = ProtoToJsonResp{}
	mi := &file_node_node_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtoToJsonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtoToJsonResp) ProtoMessage() {}

func (x *ProtoToJsonResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtoToJsonResp.ProtoReflect.Descriptor instead.
func (*ProtoToJsonResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{14}
}

func (x *ProtoToJsonResp) GetJsonData() *structpb.Value {
	if x != nil {
		return x.JsonData
	}
	return nil
}

type ApiCallResp_CallResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        []*http.HttpHeader     `protobuf:"bytes,1,rep,name=header,proto3" json:"header,omitempty"`  // 响应头
	Body          *structpb.Value        `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`      // 响应体
	Status        int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"` // 响应状态码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiCallResp_CallResp) Reset() {
	*x = ApiCallResp_CallResp{}
	mi := &file_node_node_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiCallResp_CallResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiCallResp_CallResp) ProtoMessage() {}

func (x *ApiCallResp_CallResp) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiCallResp_CallResp.ProtoReflect.Descriptor instead.
func (*ApiCallResp_CallResp) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ApiCallResp_CallResp) GetHeader() []*http.HttpHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ApiCallResp_CallResp) GetBody() *structpb.Value {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *ApiCallResp_CallResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UpdateProtoReq_ProjectConfig struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	ProjectName   string                             `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"`    // 项目名称
	GitUrl        string                             `protobuf:"bytes,2,opt,name=git_url,json=gitUrl,proto3" json:"git_url,omitempty"`                   // Git地址
	Branch        string                             `protobuf:"bytes,3,opt,name=branch,proto3" json:"branch,omitempty"`                                 // 分支名称
	ImportPath    string                             `protobuf:"bytes,4,opt,name=import_path,json=importPath,proto3" json:"import_path,omitempty"`       // 导入路径
	ExcludePaths  []string                           `protobuf:"bytes,5,rep,name=exclude_paths,json=excludePaths,proto3" json:"exclude_paths,omitempty"` // 排除的路径
	ExcludeFiles  []string                           `protobuf:"bytes,6,rep,name=exclude_files,json=excludeFiles,proto3" json:"exclude_files,omitempty"` // 排除的文件
	Dependencies  []*UpdateProtoReq_DependenceConfig `protobuf:"bytes,7,rep,name=dependencies,proto3" json:"dependencies,omitempty"`                     // 依赖配置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoReq_ProjectConfig) Reset() {
	*x = UpdateProtoReq_ProjectConfig{}
	mi := &file_node_node_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoReq_ProjectConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoReq_ProjectConfig) ProtoMessage() {}

func (x *UpdateProtoReq_ProjectConfig) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoReq_ProjectConfig.ProtoReflect.Descriptor instead.
func (*UpdateProtoReq_ProjectConfig) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{9, 0}
}

func (x *UpdateProtoReq_ProjectConfig) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *UpdateProtoReq_ProjectConfig) GetGitUrl() string {
	if x != nil {
		return x.GitUrl
	}
	return ""
}

func (x *UpdateProtoReq_ProjectConfig) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *UpdateProtoReq_ProjectConfig) GetImportPath() string {
	if x != nil {
		return x.ImportPath
	}
	return ""
}

func (x *UpdateProtoReq_ProjectConfig) GetExcludePaths() []string {
	if x != nil {
		return x.ExcludePaths
	}
	return nil
}

func (x *UpdateProtoReq_ProjectConfig) GetExcludeFiles() []string {
	if x != nil {
		return x.ExcludeFiles
	}
	return nil
}

func (x *UpdateProtoReq_ProjectConfig) GetDependencies() []*UpdateProtoReq_DependenceConfig {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

type UpdateProtoReq_DependenceConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Config:
	//
	//	*UpdateProtoReq_DependenceConfig_Git
	//	*UpdateProtoReq_DependenceConfig_LocalPath
	Config        isUpdateProtoReq_DependenceConfig_Config `protobuf_oneof:"config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoReq_DependenceConfig) Reset() {
	*x = UpdateProtoReq_DependenceConfig{}
	mi := &file_node_node_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoReq_DependenceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoReq_DependenceConfig) ProtoMessage() {}

func (x *UpdateProtoReq_DependenceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoReq_DependenceConfig.ProtoReflect.Descriptor instead.
func (*UpdateProtoReq_DependenceConfig) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{9, 1}
}

func (x *UpdateProtoReq_DependenceConfig) GetConfig() isUpdateProtoReq_DependenceConfig_Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *UpdateProtoReq_DependenceConfig) GetGit() *UpdateProtoReq_GitConfig {
	if x != nil {
		if x, ok := x.Config.(*UpdateProtoReq_DependenceConfig_Git); ok {
			return x.Git
		}
	}
	return nil
}

func (x *UpdateProtoReq_DependenceConfig) GetLocalPath() string {
	if x != nil {
		if x, ok := x.Config.(*UpdateProtoReq_DependenceConfig_LocalPath); ok {
			return x.LocalPath
		}
	}
	return ""
}

type isUpdateProtoReq_DependenceConfig_Config interface {
	isUpdateProtoReq_DependenceConfig_Config()
}

type UpdateProtoReq_DependenceConfig_Git struct {
	Git *UpdateProtoReq_GitConfig `protobuf:"bytes,1,opt,name=git,proto3,oneof"` // Git配置
}

type UpdateProtoReq_DependenceConfig_LocalPath struct {
	LocalPath string `protobuf:"bytes,2,opt,name=local_path,json=localPath,proto3,oneof"` // 本地路径
}

func (*UpdateProtoReq_DependenceConfig_Git) isUpdateProtoReq_DependenceConfig_Config() {}

func (*UpdateProtoReq_DependenceConfig_LocalPath) isUpdateProtoReq_DependenceConfig_Config() {}

type UpdateProtoReq_GitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GitUrl        string                 `protobuf:"bytes,1,opt,name=git_url,json=gitUrl,proto3" json:"git_url,omitempty"`             // Git地址
	Branch        string                 `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`                           // 分支名称
	ImportPath    string                 `protobuf:"bytes,3,opt,name=import_path,json=importPath,proto3" json:"import_path,omitempty"` // 导入路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoReq_GitConfig) Reset() {
	*x = UpdateProtoReq_GitConfig{}
	mi := &file_node_node_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoReq_GitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoReq_GitConfig) ProtoMessage() {}

func (x *UpdateProtoReq_GitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoReq_GitConfig.ProtoReflect.Descriptor instead.
func (*UpdateProtoReq_GitConfig) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{9, 2}
}

func (x *UpdateProtoReq_GitConfig) GetGitUrl() string {
	if x != nil {
		return x.GitUrl
	}
	return ""
}

func (x *UpdateProtoReq_GitConfig) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *UpdateProtoReq_GitConfig) GetImportPath() string {
	if x != nil {
		return x.ImportPath
	}
	return ""
}

type UpdateProtoResp_ProjectInfo struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	ProjectName   string                  `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"` // 项目名称
	Branch        string                  `protobuf:"bytes,2,opt,name=branch,proto3" json:"branch,omitempty"`                              // 分支名称
	Commit        *UpdateProtoResp_Commit `protobuf:"bytes,3,opt,name=commit,proto3" json:"commit,omitempty"`                              // 提交信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoResp_ProjectInfo) Reset() {
	*x = UpdateProtoResp_ProjectInfo{}
	mi := &file_node_node_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoResp_ProjectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoResp_ProjectInfo) ProtoMessage() {}

func (x *UpdateProtoResp_ProjectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoResp_ProjectInfo.ProtoReflect.Descriptor instead.
func (*UpdateProtoResp_ProjectInfo) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{10, 0}
}

func (x *UpdateProtoResp_ProjectInfo) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *UpdateProtoResp_ProjectInfo) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *UpdateProtoResp_ProjectInfo) GetCommit() *UpdateProtoResp_Commit {
	if x != nil {
		return x.Commit
	}
	return nil
}

type UpdateProtoResp_Commit struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          string                 `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`       // 提交哈希
	Author        string                 `protobuf:"bytes,2,opt,name=author,proto3" json:"author,omitempty"`   // 提交作者
	Date          string                 `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`       // 提交日期
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"` // 提交信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProtoResp_Commit) Reset() {
	*x = UpdateProtoResp_Commit{}
	mi := &file_node_node_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProtoResp_Commit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProtoResp_Commit) ProtoMessage() {}

func (x *UpdateProtoResp_Commit) ProtoReflect() protoreflect.Message {
	mi := &file_node_node_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProtoResp_Commit.ProtoReflect.Descriptor instead.
func (*UpdateProtoResp_Commit) Descriptor() ([]byte, []int) {
	return file_node_node_proto_rawDescGZIP(), []int{10, 1}
}

func (x *UpdateProtoResp_Commit) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *UpdateProtoResp_Commit) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *UpdateProtoResp_Commit) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *UpdateProtoResp_Commit) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_node_node_proto protoreflect.FileDescriptor

var file_node_node_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x1a, 0x1b, 0x62, 0x75, 0x66, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x68,
	0x74, 0x74, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x60, 0x0a, 0x0c,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x03,
	0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x12, 0x2f, 0x0a, 0x06, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x48, 0x00, 0x52, 0x06, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x93,
	0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48,
	0x08, 0xd8, 0x01, 0x01, 0x72, 0x03, 0x88, 0x01, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x44,
	0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x42, 0x06,
	0xba, 0x48, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x22, 0x3a, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d,
	0x22, 0x3e, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x2b, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x19, 0xba, 0x48, 0x16, 0x72, 0x14, 0x32, 0x12, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x22, 0x3a, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x3c, 0x0a, 0x0d,
	0x56, 0x69, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2b, 0x0a,
	0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0xba, 0x48, 0x16, 0x72,
	0x14, 0x32, 0x12, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x03, 0x63, 0x69, 0x64, 0x22, 0x38, 0x0a, 0x0e, 0x56, 0x69,
	0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x04,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x69, 0x74, 0x65, 0x6d, 0x22, 0xdb, 0x02, 0x0a, 0x0a, 0x41, 0x70, 0x69, 0x43, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x2b, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x19, 0xba, 0x48, 0x16, 0x72, 0x14, 0x32, 0x12, 0x28, 0x3f, 0x3a, 0x5e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xba,
	0x48, 0x08, 0xd8, 0x01, 0x01, 0x72, 0x03, 0x90, 0x01, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x1e, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x06, 0xba, 0x48, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x3d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x48, 0x74,
	0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05,
	0x22, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x32,
	0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x06, 0xba, 0x48, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x12, 0x28, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x44, 0x0a, 0x0d,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x42, 0x06, 0xba, 0x48,
	0x03, 0xd8, 0x01, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x22, 0xb9, 0x02, 0x0a, 0x0b, 0x41, 0x70, 0x69, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e,
	0x41, 0x70, 0x69, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x1a, 0x7e,
	0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x04, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8f,
	0x06, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x02,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba,
	0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x4b,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05, 0x22, 0x03, 0xd8, 0x01,
	0x01, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x1a, 0xe8, 0x02, 0x0a, 0x0d,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x67, 0x69, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xba, 0x48, 0x05, 0x72,
	0x03, 0x88, 0x01, 0x01, 0x52, 0x06, 0x67, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x06,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x2b, 0x0a,
	0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x0d, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05, 0x22, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x0c,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x30, 0x0a, 0x0d,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x0b, 0xba, 0x48, 0x08, 0x92, 0x01, 0x05, 0x22, 0x03, 0xd8, 0x01, 0x01,
	0x52, 0x0c, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x56,
	0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x44, 0x65, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0b, 0xba, 0x48, 0x08,
	0x92, 0x01, 0x05, 0x22, 0x03, 0xd8, 0x01, 0x01, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x1a, 0x7a, 0x0a, 0x10, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x03, 0x67, 0x69,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x47, 0x69,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x03, 0x67, 0x69, 0x74, 0x12, 0x28,
	0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x09, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x42, 0x08, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x1a, 0x7c, 0x0a, 0x09, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x21, 0x0a, 0x07, 0x67, 0x69, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xba, 0x48, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01, 0x52, 0x06, 0x67, 0x69, 0x74, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x62, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x12, 0x2b, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xba, 0x48, 0x07, 0xd8, 0x01, 0x01,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x22, 0xef, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12,
	0x3d, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x1a, 0x7e,
	0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x1a, 0x62,
	0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x0e, 0x4a, 0x73, 0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x34, 0x0a, 0x0f, 0x4a, 0x73, 0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x73, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x54,
	0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xba, 0x48, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x46, 0x0a, 0x0f, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x54, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33,
	0x0a, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6a, 0x73, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x32, 0xa8, 0x03, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x15, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x6e, 0x6f, 0x64,
	0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3d, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x12, 0x15, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x6e, 0x6f, 0x64, 0x65,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x37, 0x0a, 0x0a, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x13, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x56, 0x69, 0x65, 0x77,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07, 0x41, 0x70,
	0x69, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x10, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x41, 0x70, 0x69,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x41,
	0x70, 0x69, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x2e, 0x6e, 0x6f, 0x64, 0x65,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0b, 0x4a, 0x73, 0x6f, 0x6e, 0x54, 0x6f,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x4a, 0x73, 0x6f,
	0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x6e, 0x6f,
	0x64, 0x65, 0x2e, 0x4a, 0x73, 0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x54, 0x6f, 0x4a, 0x73, 0x6f,
	0x6e, 0x12, 0x14, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x54, 0x6f,
	0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x54, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x42, 0x3a,
	0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x6e,
	0x6f, 0x64, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_node_node_proto_rawDescOnce sync.Once
	file_node_node_proto_rawDescData = file_node_node_proto_rawDesc
)

func file_node_node_proto_rawDescGZIP() []byte {
	file_node_node_proto_rawDescOnce.Do(func() {
		file_node_node_proto_rawDescData = protoimpl.X.CompressGZIP(file_node_node_proto_rawDescData)
	})
	return file_node_node_proto_rawDescData
}

var file_node_node_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_node_node_proto_goTypes = []any{
	(*SearchClient)(nil),                    // 0: node.SearchClient
	(*CreateClientReq)(nil),                 // 1: node.CreateClientReq
	(*CreateClientResp)(nil),                // 2: node.CreateClientResp
	(*DeleteClientReq)(nil),                 // 3: node.DeleteClientReq
	(*DeleteClientResp)(nil),                // 4: node.DeleteClientResp
	(*ViewClientReq)(nil),                   // 5: node.ViewClientReq
	(*ViewClientResp)(nil),                  // 6: node.ViewClientResp
	(*ApiCallReq)(nil),                      // 7: node.ApiCallReq
	(*ApiCallResp)(nil),                     // 8: node.ApiCallResp
	(*UpdateProtoReq)(nil),                  // 9: node.UpdateProtoReq
	(*UpdateProtoResp)(nil),                 // 10: node.UpdateProtoResp
	(*JsonToProtoReq)(nil),                  // 11: node.JsonToProtoReq
	(*JsonToProtoResp)(nil),                 // 12: node.JsonToProtoResp
	(*ProtoToJsonReq)(nil),                  // 13: node.ProtoToJsonReq
	(*ProtoToJsonResp)(nil),                 // 14: node.ProtoToJsonResp
	(*ApiCallResp_CallResp)(nil),            // 15: node.ApiCallResp.CallResp
	(*UpdateProtoReq_ProjectConfig)(nil),    // 16: node.UpdateProtoReq.ProjectConfig
	(*UpdateProtoReq_DependenceConfig)(nil), // 17: node.UpdateProtoReq.DependenceConfig
	(*UpdateProtoReq_GitConfig)(nil),        // 18: node.UpdateProtoReq.GitConfig
	(*UpdateProtoResp_ProjectInfo)(nil),     // 19: node.UpdateProtoResp.ProjectInfo
	(*UpdateProtoResp_Commit)(nil),          // 20: node.UpdateProtoResp.Commit
	(*structpb.Struct)(nil),                 // 21: google.protobuf.Struct
	(*pb.ClientInfo)(nil),                   // 22: common.ClientInfo
	(*http.HttpHeader)(nil),                 // 23: google.rpc.HttpHeader
	(*structpb.Value)(nil),                  // 24: google.protobuf.Value
}
var file_node_node_proto_depIdxs = []int32{
	1,  // 0: node.SearchClient.create:type_name -> node.CreateClientReq
	21, // 1: node.CreateClientReq.custom_fields:type_name -> google.protobuf.Struct
	22, // 2: node.CreateClientResp.item:type_name -> common.ClientInfo
	22, // 3: node.DeleteClientResp.item:type_name -> common.ClientInfo
	22, // 4: node.ViewClientResp.item:type_name -> common.ClientInfo
	23, // 5: node.ApiCallReq.headers:type_name -> google.rpc.HttpHeader
	24, // 6: node.ApiCallReq.body:type_name -> google.protobuf.Value
	21, // 7: node.ApiCallReq.custom_fields:type_name -> google.protobuf.Struct
	22, // 8: node.ApiCallResp.client_info:type_name -> common.ClientInfo
	21, // 9: node.ApiCallResp.custom_fields:type_name -> google.protobuf.Struct
	15, // 10: node.ApiCallResp.call_resp:type_name -> node.ApiCallResp.CallResp
	16, // 11: node.UpdateProtoReq.projects:type_name -> node.UpdateProtoReq.ProjectConfig
	19, // 12: node.UpdateProtoResp.projects:type_name -> node.UpdateProtoResp.ProjectInfo
	24, // 13: node.JsonToProtoReq.data:type_name -> google.protobuf.Value
	24, // 14: node.ProtoToJsonResp.json_data:type_name -> google.protobuf.Value
	23, // 15: node.ApiCallResp.CallResp.header:type_name -> google.rpc.HttpHeader
	24, // 16: node.ApiCallResp.CallResp.body:type_name -> google.protobuf.Value
	17, // 17: node.UpdateProtoReq.ProjectConfig.dependencies:type_name -> node.UpdateProtoReq.DependenceConfig
	18, // 18: node.UpdateProtoReq.DependenceConfig.git:type_name -> node.UpdateProtoReq.GitConfig
	20, // 19: node.UpdateProtoResp.ProjectInfo.commit:type_name -> node.UpdateProtoResp.Commit
	1,  // 20: node.NodeService.CreateClient:input_type -> node.CreateClientReq
	3,  // 21: node.NodeService.DeleteClient:input_type -> node.DeleteClientReq
	5,  // 22: node.NodeService.ViewClient:input_type -> node.ViewClientReq
	7,  // 23: node.NodeService.ApiCall:input_type -> node.ApiCallReq
	9,  // 24: node.NodeService.UpdateProto:input_type -> node.UpdateProtoReq
	11, // 25: node.NodeService.JsonToProto:input_type -> node.JsonToProtoReq
	13, // 26: node.NodeService.ProtoToJson:input_type -> node.ProtoToJsonReq
	2,  // 27: node.NodeService.CreateClient:output_type -> node.CreateClientResp
	4,  // 28: node.NodeService.DeleteClient:output_type -> node.DeleteClientResp
	6,  // 29: node.NodeService.ViewClient:output_type -> node.ViewClientResp
	8,  // 30: node.NodeService.ApiCall:output_type -> node.ApiCallResp
	10, // 31: node.NodeService.UpdateProto:output_type -> node.UpdateProtoResp
	12, // 32: node.NodeService.JsonToProto:output_type -> node.JsonToProtoResp
	14, // 33: node.NodeService.ProtoToJson:output_type -> node.ProtoToJsonResp
	27, // [27:34] is the sub-list for method output_type
	20, // [20:27] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_node_node_proto_init() }
func file_node_node_proto_init() {
	if File_node_node_proto != nil {
		return
	}
	file_node_node_proto_msgTypes[0].OneofWrappers = []any{
		(*SearchClient_Cid)(nil),
		(*SearchClient_Create)(nil),
	}
	file_node_node_proto_msgTypes[17].OneofWrappers = []any{
		(*UpdateProtoReq_DependenceConfig_Git)(nil),
		(*UpdateProtoReq_DependenceConfig_LocalPath)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_node_node_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_node_node_proto_goTypes,
		DependencyIndexes: file_node_node_proto_depIdxs,
		MessageInfos:      file_node_node_proto_msgTypes,
	}.Build()
	File_node_node_proto = out.File
	file_node_node_proto_rawDesc = nil
	file_node_node_proto_goTypes = nil
	file_node_node_proto_depIdxs = nil
}
