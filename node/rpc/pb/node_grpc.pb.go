// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: node/node.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	NodeService_CreateClient_FullMethodName = "/node.NodeService/CreateClient"
	NodeService_DeleteClient_FullMethodName = "/node.NodeService/DeleteClient"
	NodeService_ViewClient_FullMethodName   = "/node.NodeService/ViewClient"
	NodeService_ApiCall_FullMethodName      = "/node.NodeService/ApiCall"
	NodeService_UpdateProto_FullMethodName  = "/node.NodeService/UpdateProto"
	NodeService_JsonToProto_FullMethodName  = "/node.NodeService/JsonToProto"
	NodeService_ProtoToJson_FullMethodName  = "/node.NodeService/ProtoToJson"
)

// NodeServiceClient is the client API for NodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// NodeService 节点服务
type NodeServiceClient interface {
	// CreateClient 创建客户端
	CreateClient(ctx context.Context, in *CreateClientReq, opts ...grpc.CallOption) (*CreateClientResp, error)
	// DeleteClient 删除客户端
	DeleteClient(ctx context.Context, in *DeleteClientReq, opts ...grpc.CallOption) (*DeleteClientResp, error)
	// ViewClient 查看客户端
	ViewClient(ctx context.Context, in *ViewClientReq, opts ...grpc.CallOption) (*ViewClientResp, error)
	// ApiCall 接口调用
	ApiCall(ctx context.Context, in *ApiCallReq, opts ...grpc.CallOption) (*ApiCallResp, error)
	// UpdateProto 更新`proto`项目
	UpdateProto(ctx context.Context, in *UpdateProtoReq, opts ...grpc.CallOption) (*UpdateProtoResp, error)
	// JsonToProto `json`数据转换成`proto`字符串
	JsonToProto(ctx context.Context, in *JsonToProtoReq, opts ...grpc.CallOption) (*JsonToProtoResp, error)
	// ProtoToJson `proto`字符串转换成`json`数据
	ProtoToJson(ctx context.Context, in *ProtoToJsonReq, opts ...grpc.CallOption) (*ProtoToJsonResp, error)
}

type nodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNodeServiceClient(cc grpc.ClientConnInterface) NodeServiceClient {
	return &nodeServiceClient{cc}
}

func (c *nodeServiceClient) CreateClient(ctx context.Context, in *CreateClientReq, opts ...grpc.CallOption) (*CreateClientResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateClientResp)
	err := c.cc.Invoke(ctx, NodeService_CreateClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) DeleteClient(ctx context.Context, in *DeleteClientReq, opts ...grpc.CallOption) (*DeleteClientResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteClientResp)
	err := c.cc.Invoke(ctx, NodeService_DeleteClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ViewClient(ctx context.Context, in *ViewClientReq, opts ...grpc.CallOption) (*ViewClientResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ViewClientResp)
	err := c.cc.Invoke(ctx, NodeService_ViewClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ApiCall(ctx context.Context, in *ApiCallReq, opts ...grpc.CallOption) (*ApiCallResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiCallResp)
	err := c.cc.Invoke(ctx, NodeService_ApiCall_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) UpdateProto(ctx context.Context, in *UpdateProtoReq, opts ...grpc.CallOption) (*UpdateProtoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateProtoResp)
	err := c.cc.Invoke(ctx, NodeService_UpdateProto_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) JsonToProto(ctx context.Context, in *JsonToProtoReq, opts ...grpc.CallOption) (*JsonToProtoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JsonToProtoResp)
	err := c.cc.Invoke(ctx, NodeService_JsonToProto_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nodeServiceClient) ProtoToJson(ctx context.Context, in *ProtoToJsonReq, opts ...grpc.CallOption) (*ProtoToJsonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProtoToJsonResp)
	err := c.cc.Invoke(ctx, NodeService_ProtoToJson_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NodeServiceServer is the server API for NodeService service.
// All implementations must embed UnimplementedNodeServiceServer
// for forward compatibility.
//
// NodeService 节点服务
type NodeServiceServer interface {
	// CreateClient 创建客户端
	CreateClient(context.Context, *CreateClientReq) (*CreateClientResp, error)
	// DeleteClient 删除客户端
	DeleteClient(context.Context, *DeleteClientReq) (*DeleteClientResp, error)
	// ViewClient 查看客户端
	ViewClient(context.Context, *ViewClientReq) (*ViewClientResp, error)
	// ApiCall 接口调用
	ApiCall(context.Context, *ApiCallReq) (*ApiCallResp, error)
	// UpdateProto 更新`proto`项目
	UpdateProto(context.Context, *UpdateProtoReq) (*UpdateProtoResp, error)
	// JsonToProto `json`数据转换成`proto`字符串
	JsonToProto(context.Context, *JsonToProtoReq) (*JsonToProtoResp, error)
	// ProtoToJson `proto`字符串转换成`json`数据
	ProtoToJson(context.Context, *ProtoToJsonReq) (*ProtoToJsonResp, error)
	mustEmbedUnimplementedNodeServiceServer()
}

// UnimplementedNodeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNodeServiceServer struct{}

func (UnimplementedNodeServiceServer) CreateClient(context.Context, *CreateClientReq) (*CreateClientResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateClient not implemented")
}
func (UnimplementedNodeServiceServer) DeleteClient(context.Context, *DeleteClientReq) (*DeleteClientResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteClient not implemented")
}
func (UnimplementedNodeServiceServer) ViewClient(context.Context, *ViewClientReq) (*ViewClientResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewClient not implemented")
}
func (UnimplementedNodeServiceServer) ApiCall(context.Context, *ApiCallReq) (*ApiCallResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApiCall not implemented")
}
func (UnimplementedNodeServiceServer) UpdateProto(context.Context, *UpdateProtoReq) (*UpdateProtoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProto not implemented")
}
func (UnimplementedNodeServiceServer) JsonToProto(context.Context, *JsonToProtoReq) (*JsonToProtoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonToProto not implemented")
}
func (UnimplementedNodeServiceServer) ProtoToJson(context.Context, *ProtoToJsonReq) (*ProtoToJsonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtoToJson not implemented")
}
func (UnimplementedNodeServiceServer) mustEmbedUnimplementedNodeServiceServer() {}
func (UnimplementedNodeServiceServer) testEmbeddedByValue()                     {}

// UnsafeNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NodeServiceServer will
// result in compilation errors.
type UnsafeNodeServiceServer interface {
	mustEmbedUnimplementedNodeServiceServer()
}

func RegisterNodeServiceServer(s grpc.ServiceRegistrar, srv NodeServiceServer) {
	// If the following call pancis, it indicates UnimplementedNodeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&NodeService_ServiceDesc, srv)
}

func _NodeService_CreateClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).CreateClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_CreateClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).CreateClient(ctx, req.(*CreateClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_DeleteClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).DeleteClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_DeleteClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).DeleteClient(ctx, req.(*DeleteClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ViewClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ViewClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ViewClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ViewClient(ctx, req.(*ViewClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ApiCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApiCallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ApiCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ApiCall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ApiCall(ctx, req.(*ApiCallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_UpdateProto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProtoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).UpdateProto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_UpdateProto_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).UpdateProto(ctx, req.(*UpdateProtoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_JsonToProto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonToProtoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).JsonToProto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_JsonToProto_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).JsonToProto(ctx, req.(*JsonToProtoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NodeService_ProtoToJson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProtoToJsonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NodeServiceServer).ProtoToJson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NodeService_ProtoToJson_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NodeServiceServer).ProtoToJson(ctx, req.(*ProtoToJsonReq))
	}
	return interceptor(ctx, in, info, handler)
}

// NodeService_ServiceDesc is the grpc.ServiceDesc for NodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "node.NodeService",
	HandlerType: (*NodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateClient",
			Handler:    _NodeService_CreateClient_Handler,
		},
		{
			MethodName: "DeleteClient",
			Handler:    _NodeService_DeleteClient_Handler,
		},
		{
			MethodName: "ViewClient",
			Handler:    _NodeService_ViewClient_Handler,
		},
		{
			MethodName: "ApiCall",
			Handler:    _NodeService_ApiCall_Handler,
		},
		{
			MethodName: "UpdateProto",
			Handler:    _NodeService_UpdateProto_Handler,
		},
		{
			MethodName: "JsonToProto",
			Handler:    _NodeService_JsonToProto_Handler,
		},
		{
			MethodName: "ProtoToJson",
			Handler:    _NodeService_ProtoToJson_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "node/node.proto",
}
