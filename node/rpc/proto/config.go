package proto

import (
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

type InitProtoConfig struct {
	RootPath string
	Products []ProductConfig `json:",optional"`
}

type ProductConfig struct {
	ProductName types.ProductName // 产品名称
	Branch      string            // 分支名称
	Projects    []ProjectConfig   // 项目列表
}

type ProjectConfig struct {
	ProjectName string // 项目名称

	GitConfig // Git配置

	ExcludePaths []string           `json:",optional"` // 排除路径
	ExcludeFiles []string           `json:",optional"` // 排除文件
	Dependencies []DependenceConfig `json:",optional"` // 依赖列表
}

type GitConfig struct {
	GitURL     string // Git地址
	Branch     string `json:",default=main"` // 分支名称
	ImportPath string `json:",default=."`    // 导入路径

	commit *Commit // 提交信息
}

type Commit struct {
	// Hash of the commit object.
	Hash plumbing.Hash
	// Author is the original author of the commit.
	Author object.Signature
	// Message is the commit message, contains arbitrary text.
	Message string
}

type DependenceConfig struct {
	Git   GitDependenceConfig   `json:",omitempty,optional"`
	Local LocalDependenceConfig `json:",omitempty,optional"`
}

type GitDependenceConfig struct {
	GitConfig
}

type LocalDependenceConfig struct {
	LocalPath string
}

type mapperConfig struct {
	RootPath      string
	ProductName   types.ProductName
	ProductBranch string

	ProjectConfig
}

type reducerConfig struct {
	ProductName   types.ProductName
	ProductBranch string

	ProjectConfig
}

func (c ProductConfig) Key() string {
	return string(c.ProductName) + constColon + c.Branch
}

func (c ProjectConfig) Key() string {
	return c.ProjectName + constColon + c.Branch
}

func (c GitConfig) Key() string {
	return c.GitURL + constColon + c.Branch
}

func (c GitConfig) Commit() *Commit {
	return c.commit
}
