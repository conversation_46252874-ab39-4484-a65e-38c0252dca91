package proto

import (
	"time"
)

type Operation string

const (
	Initialize Operation = "initialize"
	Update     Operation = "update"
)

type Event struct {
	Operator  string                   `json:"operator"`   // 操作者（节点服务ID）
	Operation Operation                `json:"operation"`  // 操作，如：初始化、更新
	StartedAt time.Time                `json:"started_at"` // 操作开始时间
	EndedAt   time.Time                `json:"ended_at"`   // 操作结束时间
	Products  map[string]ProductConfig `json:"products"`   // 操作对象（产品配置列表，兼容初始化和更新）
}
