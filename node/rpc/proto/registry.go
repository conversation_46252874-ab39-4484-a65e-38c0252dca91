package proto

import (
	"context"
	es "errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/pkg/errors"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/syncx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redisqueue/stream"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/types"
)

const (
	constSliceCapacity   = 8
	constStreamMaxLength = 100

	constInitLockKeyPrefix   = "lock:protoRegistry:initProto"
	constUpdateLockKeyPrefix = "lock:protoRegistry:updateProto"

	constInitTimeout    = 3 * time.Minute
	constUpdateInterval = 10 * time.Minute

	constColon     = ":"
	constSlash     = "/"
	constUnderline = "_"
	constDotGit    = ".git"

	payloadOfMessageValuesKey = "payload"
)

var (
	ErrProtoRegistryNotInit = errors.New("the proto registry is not initialized")

	errEmptyGitURL       = errors.New("the git url is empty")
	errEmptyBranch       = errors.New("the branch is empty")
	errNullMessage       = errors.New("the stream message is null")
	errNullMessageValues = errors.New("the values of stream message is null")
	errQuitWhileCloning  = errors.New("got a quit signal while waiting the event of completing code cloning")

	once     sync.Once
	registry *Registry
)

// InitProtoRegistry init the proto registry by init proto config
func InitProtoRegistry(c InitProtoConfig, rc redis.RedisConf) (err error) {
	once.Do(
		func() {
			registry, err = NewRegistry(c, rc)
		},
	)

	return
}

// GetRegistry returns a global Registry.
func GetRegistry() *Registry {
	return registry
}

// GetProtoManager get a proto manager by product name
func GetProtoManager(name types.ProductName) (pm *protobuf.ProtoManager, ok bool) {
	if registry == nil {
		return nil, false
	}

	return registry.Get(name)
}

// ReloadProtoManager reloads the proto manager of specified product and branch
func ReloadProtoManager(ctx context.Context, product ProductConfig) (ProductConfig, error) {
	if registry == nil {
		return ProductConfig{}, ErrProtoRegistryNotInit
	}

	return registry.Update(ctx, product)
}

func Close() {
	if registry != nil {
		registry.close()
	}
}

type Registry struct {
	logx.Logger
	ctx context.Context

	c InitProtoConfig

	r   *redis.Redis
	rdb red.UniversalClient

	producer *stream.Producer
	consumer *stream.Consumer

	nid          string
	initialized  *atomic.Bool
	quitChan     chan lang.PlaceholderType
	initChan     chan *Event
	updateChan   chan *Event
	singleFlight syncx.SingleFlight

	pcs map[string]ProductConfig
	pms map[types.ProductName]*protobuf.ProtoManager

	once  sync.Once
	mutex sync.RWMutex
}

func NewRegistry(c InitProtoConfig, rc redis.RedisConf) (*Registry, error) {
	r, err := newRegistry(c, rc)
	if err != nil {
		return nil, err
	}

	err = r.init()
	if err != nil {
		return nil, err
	}

	proc.AddShutdownListener(r.close)
	return r, nil
}

func newRegistry(c InitProtoConfig, rc redis.RedisConf) (*Registry, error) {
	r, err := redis.NewRedis(rc, redis.WithDB(rc.DB))
	if err != nil {
		return nil, err
	}

	rdb := qetredis.NewClient(rc)
	producer, err := stream.NewProducerWithOptions(
		stream.ProducerOptions{
			MaxLength:   constStreamMaxLength,
			Approximate: true,
			RedisClient: rdb,
		},
	)
	if err != nil {
		return nil, err
	}

	nid := common.ID()
	consumer, err := stream.NewConsumerWithOptions(
		stream.ConsumerOptions{
			Name:              nid,
			GroupName:         nid,
			VisibilityTimeout: 60 * time.Second,
			BlockingTimeout:   5 * time.Second,
			ReclaimInterval:   1 * time.Second,
			BufferSize:        100,
			Concurrency:       10,
			AutoDestroy:       true,
			RedisClient:       rdb,
		},
	)
	if err != nil {
		return nil, err
	}

	ctx := context.Background()
	return &Registry{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, nid)),
		ctx:    ctx,

		c: c,

		r:   r,
		rdb: rdb,

		producer: producer,
		consumer: consumer,

		nid:          nid,
		initialized:  new(atomic.Bool),
		quitChan:     make(chan lang.PlaceholderType, 1),
		initChan:     make(chan *Event, 1),
		updateChan:   make(chan *Event, 1),
		singleFlight: syncx.NewSingleFlight(),

		pcs: distinct(c.Products),
		pms: make(map[types.ProductName]*protobuf.ProtoManager, len(c.Products)),
	}, nil
}

func (r *Registry) init() (err error) {
	defer func() {
		if err == nil {
			r.initialized.Store(true)
		}
		r.drainInitChan()
	}()

	ctx, cancel := context.WithTimeout(r.ctx, constInitTimeout)
	defer cancel()

	// 初始化消费者
	r.initConsumer()

	var pcs map[string]ProductConfig
	fn := func() error {
		// generate an event of init proto registry
		event := &Event{
			Operator:  r.nid,
			Operation: Initialize,
			StartedAt: time.Now(),
		}

		if err = mkdir(r.c.RootPath); err != nil {
			return err
		}

		// concurrent clone code
		pcs, err = r.cloneOrUpdate(ctx, r.pcs, false, false)
		if err != nil {
			return err
		}

		// update the `products` and `endedAt` of event
		event.Products = pcs
		event.EndedAt = time.Now()

		// broadcast the event of completing code cloning
		if err = r.producer.Enqueue(
			ctx, &stream.Message{
				Stream: common.ProtoRegistryKey,
				Values: map[string]any{
					payloadOfMessageValuesKey: jsonx.MarshalToStringIgnoreError(event),
				},
			},
		); err != nil {
			return err
		}

		return nil
	}
	err = caller.LockDo(r.r, constInitLockKeyPrefix, fn, int(constInitTimeout.Seconds()))
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			r.Errorf("failed to clone proto project code, error: %v", err)
			return err
		}

		r.Info("another service is cloning proto project code, wait for the execution result")

		// waiting the event of completing code cloning
		select {
		case msg, ok := <-r.initChan:
			if ok {
				r.Infof("the result of cloning proto project code: %s", jsonx.MarshalIgnoreError(msg))
				if msg != nil {
					pcs = msg.Products
				}
			} else {
				r.Error("the channel of initialization has been closed")
			}
		case <-ctx.Done():
			return errors.Errorf(
				"waiting for the result of cloning proto project code timed out, error: %v", ctx.Err(),
			)
		case <-r.quitChan:
			return errQuitWhileCloning
		}
	}

	return r.load(pcs)
}

func (r *Registry) initConsumer() {
	r.consumer.RegisterWithLastID(common.ProtoRegistryKey, "$", r.handler)

	threading.GoSafe(r.consumer.Run)
	threading.GoSafe(
		func() {
			for {
				select {
				case err, ok := <-r.consumer.Errors:
					if ok {
						r.Errorf("got an error from stream consumer, error: %+v", err)
					} else {
						r.Error("the error channel of stream consumer has been closed")
						return
					}
				case <-r.quitChan:
					r.Info("got a quit signal while running stream consumer")
					return
				}
			}
		},
	)
}

func (r *Registry) cloneOrUpdate(
	ctx context.Context, productConfigs map[string]ProductConfig, update, failFast bool,
) (map[string]ProductConfig, error) {
	if len(productConfigs) == 0 {
		return productConfigs, nil
	}

	var (
		cache sync.Map

		out = make(map[string]ProductConfig, len(productConfigs))
	)

	if err := mr.MapReduceVoid[mapperConfig, reducerConfig](
		func(source chan<- mapperConfig) {
			for _, productConfig := range productConfigs {
				for _, projectConfig := range productConfig.Projects {
					source <- mapperConfig{
						RootPath:      r.c.RootPath,
						ProductName:   productConfig.ProductName,
						ProductBranch: productConfig.Branch,
						ProjectConfig: projectConfig,
					}
				}
			}
		},
		func(item mapperConfig, writer mr.Writer[reducerConfig], cancel func(error)) {
			var (
				c *object.Commit
				e error
			)
			defer func() {
				if e != nil {
					r.Error(e)

					if failFast {
						cancel(e)
					}
				}
			}()

			key1 := string(item.ProductName) + constColon + item.ProjectConfig.Key()
			if _, ok1 := cache.LoadOrStore(key1, lang.Placeholder); !ok1 {
				// ${RootPath}/${ProductName}
				productPath := filepath.Join(r.c.RootPath, string(item.ProductName))
				if e = mkdir(productPath); e != nil {
					return
				}

				// ${RootPath}/${ProductName}/${ProjectName}:${ProjectBranch}
				projectPath := filepath.Join(productPath, toPath(item.Key()))
				if c, e = r.getDataByGitURLAndBranch(
					ctx, projectPath, item.ProjectConfig.GitURL, item.ProjectConfig.Branch, false, update,
				); e != nil {
					return
				} else {
					item.ProjectConfig.commit = &Commit{
						Hash:    c.Hash,
						Author:  c.Author,
						Message: c.Message,
					}
				}

				for _, dependenceConfig := range item.Dependencies {
					if dependenceConfig.Git.GitURL == "" || dependenceConfig.Git.Branch == "" {
						continue
					}

					key2 := string(item.ProductName) + constColon + dependenceConfig.Git.Key()
					if _, ok2 := cache.LoadOrStore(key2, lang.Placeholder); ok2 {
						continue
					}

					// ${RootPath}/${ProductName}/${GitProjectName}:${GitProjectBranch}
					if _, e = r.getDataByGitURLAndBranch(
						ctx, productPath, dependenceConfig.Git.GitURL, dependenceConfig.Git.Branch, true, update,
					); e != nil {
						break
					}
				}
				if e != nil {
					return
				}
			}

			writer.Write(
				reducerConfig{
					ProductName:   item.ProductName,
					ProductBranch: item.ProductBranch,
					ProjectConfig: item.ProjectConfig,
				},
			)
		},
		func(pipe <-chan reducerConfig, cancel func(error)) {
			for item := range pipe {
				key := string(item.ProductName) + constColon + item.ProductBranch
				val, ok := out[key]
				if !ok {
					val = ProductConfig{
						ProductName: item.ProductName,
						Branch:      item.ProductBranch,
						Projects:    make([]ProjectConfig, 0, constSliceCapacity),
					}
				}
				val.Projects = append(val.Projects, item.ProjectConfig)
				out[key] = val
			}
		}, mr.WithContext(ctx),
	); err != nil {
		return nil, err
	}

	return out, nil
}

func (r *Registry) getDataByGitURLAndBranch(ctx context.Context, path, gitURL, branch string, makeSubDir, update bool) (
	commit *object.Commit, err error,
) {
	if ctx == nil {
		ctx = r.ctx
	}

	subPath := path
	if makeSubDir {
		subPath, err = getPathByGitURLAndBranch(path, gitURL, branch)
		if err != nil {
			return nil, err
		}
	}

	_, err = os.Stat(subPath)
	if err != nil && !os.IsNotExist(err) {
		// if it is not a 'not exist' error, return directly
		return nil, err
	} else if os.IsNotExist(err) {
		// target path doesn't exist, clone the git repo from remote
		r.Infof("begin to clone the git repo, git: %s, path: %s, branch: %s", gitURL, subPath, branch)
		commit, err = utils.CloneWithContext(ctx, gitURL, subPath, branch, utils.WithCloneUseCommand())
	} else if update {
		// target path exists, and the `update` flag is true, pull the git repo
		r.Infof("begin to pull the git repo, git: %s, path: %s, branch: %s", gitURL, subPath, branch)
		commit, err = utils.PullWithContext(ctx, subPath, utils.WithPullUseCommand())
	} else {
		commit, err = utils.GetLastCommit(subPath)
	}

	if err != nil {
		return nil, err
	}

	r.Infof(
		"finish to clone or pull or view the git repo, git: %s, path: %s, branch: %s, commit: %q",
		gitURL, subPath, branch, commit.String(),
	)

	return commit, nil
}

func (r *Registry) getProductByProductConfig(rootPath string, productConfig ProductConfig) protobuf.Product {
	product := protobuf.Product{
		Name:     string(productConfig.ProductName),
		Branch:   productConfig.Branch,
		Projects: make([]protobuf.Project, 0, len(productConfig.Projects)),
	}

	for _, projectConfig := range productConfig.Projects {
		// ${RootPath}/${ProductName}
		productPath := filepath.Join(rootPath, string(productConfig.ProductName))
		// ${RootPath}/${ProductName}/${ProjectName}:${ProjectBranch}
		projectPath := filepath.Join(productPath, toPath(projectConfig.Key()))
		if !utils.Exists(projectPath) {
			r.Warnf("target path not exists: %s", projectPath)
			continue
		}

		// ${RootPath}/${ProductName}/${ProjectName}:${ProjectBranch}/${ProjectImportPath}
		importPath := filepath.Join(projectPath, projectConfig.ImportPath)

		var (
			depPath string
			depErr  error

			depImportPaths = make([]string, 0, len(projectConfig.Dependencies))
		)

		for _, dependenceConfig := range projectConfig.Dependencies {
			if dependenceConfig.Local.LocalPath != "" {
				depImportPaths = append(depImportPaths, dependenceConfig.Local.LocalPath)
			} else if dependenceConfig.Git.GitURL != "" && dependenceConfig.Git.Branch != "" {
				// ${RootPath}/${ProductName}/${GitProjectName}:${GitProjectBranch}
				depPath, depErr = getPathByGitURLAndBranch(
					productPath, dependenceConfig.Git.GitURL, dependenceConfig.Git.Branch,
				)
				if depErr != nil {
					break
				} else if !utils.Exists(depPath) {
					depErr = errors.Errorf("target path not exists: %s", depPath)
					break
				}

				depImportPaths = append(depImportPaths, filepath.Join(depPath, dependenceConfig.Git.ImportPath))
			}
		}
		if depErr != nil {
			r.Warn(depErr)
			continue
		}

		product.Projects = append(
			product.Projects, protobuf.Project{
				Name:         projectConfig.ProjectName,
				Branch:       projectConfig.Branch,
				Path:         importPath,
				ImportPaths:  depImportPaths,
				ExcludePaths: projectConfig.ExcludePaths,
				ExcludeFiles: projectConfig.ExcludeFiles,
			},
		)
	}

	return product
}

type productItem struct {
	name     types.ProductName
	products []protobuf.Product
}

func (r *Registry) load(productConfigs map[string]ProductConfig) error {
	key := ""
	cache := make(map[types.ProductName][]protobuf.Product, len(productConfigs))
	for _, productConfig := range productConfigs {
		// ignore duplicate product
		if pm, ok := r.Get(productConfig.ProductName); ok && pm != nil {
			continue
		}

		if _, ok := cache[productConfig.ProductName]; !ok {
			key = string(productConfig.ProductName) + "|"
			cache[productConfig.ProductName] = make([]protobuf.Product, 0, len(productConfigs))
		}

		product := r.getProductByProductConfig(r.c.RootPath, productConfig)
		cache[productConfig.ProductName] = append(cache[productConfig.ProductName], product)
	}

	_, err := r.singleFlight.Do(
		key, func() (any, error) {
			err := mr.MapReduceVoid[*productItem, any](
				func(source chan<- *productItem) {
					for name, products := range cache {
						if len(products) == 0 {
							continue
						}

						source <- &productItem{
							name:     name,
							products: products,
						}
					}
				},
				func(item *productItem, writer mr.Writer[any], cancel func(error)) {
					if item == nil {
						return
					}

					startedAt := time.Now()
					strOfProducts := jsonx.MarshalIgnoreError(item.products)
					r.Infof("prepare to create proto manager, product name: %s, products: %s", item.name, strOfProducts)

					pm, err := protobuf.NewProtoManager(protobuf.WithProducts(item.products...))
					elapsed := time.Since(startedAt)
					if err != nil {
						r.Warnf(
							"failed to create proto manager, product name: %s, products: %s, elapsed: %s, error: %+v",
							item.name, strOfProducts, elapsed, err,
						)
						return
					}
					r.Infof(
						"finish to create proto manager, product name: %s, products: %s, elapsed: %s",
						item.name, strOfProducts, elapsed,
					)

					r.Register(item.name, pm)
					r.Infof("finish to register proto manager, product name: %s, products: %s", item.name, strOfProducts)
				},
				func(pipe <-chan any, cancel func(error)) {
				},
				mr.WithContext(r.ctx),
			)
			return nil, err
		},
	)

	return err
}

func (r *Registry) handler(message *stream.Message) error {
	if message == nil {
		return errNullMessage
	} else if message.Stream != common.ProtoRegistryKey {
		return errors.Errorf("invalid stream, expected: %s, but got: %s", common.ProtoRegistryKey, message.Stream)
	} else if message.Values == nil {
		return errNullMessageValues
	}

	value, ok := message.Values[payloadOfMessageValuesKey]
	if !ok {
		return errors.Errorf(
			"invalid message, not found the %q key, message: %s",
			payloadOfMessageValuesKey, jsonx.MarshalIgnoreError(message),
		)
	}
	payload, ok := value.(string)
	if !ok {
		return errors.Errorf("invalid payload type, expected: string, but got %T", payload)
	}
	r.Infof("got a stream message: %s", jsonx.MarshalIgnoreError(message))

	var event Event
	if err := jsonx.UnmarshalFromString(payload, &event); err != nil {
		return err
	} else if r.nid == event.Operator {
		// ignore self event
		return nil
	}

	switch event.Operation {
	case Initialize:
		if !r.initialized.Load() {
			r.initChan <- &event
		} else {
			r.Warnf("the proto registry has been initialized, event: %s", jsonx.MarshalIgnoreError(event))
		}
	case Update:
		return r.update(event.Products)
	default:
		return errors.Errorf("unknown operation, event: %s", jsonx.MarshalIgnoreError(event))
	}

	return nil
}

func (r *Registry) update(productConfigs map[string]ProductConfig) error {
	var err error

	for _, productConfig := range productConfigs {
		pm, ok := r.Get(productConfig.ProductName)
		if !ok {
			err = es.Join(err, errors.Errorf("unknown product name: %s", productConfig.ProductName))
			continue
		}

		product := r.getProductByProductConfig(r.c.RootPath, productConfig)
		if e := pm.ReloadByProduct(product); e != nil {
			err = es.Join(err, e)
		}
	}

	return err
}

func (r *Registry) close() {
	r.once.Do(
		func() {
			r.Info("closing the proto registry")

			close(r.quitChan)

			r.consumer.Shutdown()

			if r.rdb != nil {
				_ = r.rdb.Close()
			}
		},
	)
}

func (r *Registry) Register(name types.ProductName, pm *protobuf.ProtoManager) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.pms[name] = pm
}

func (r *Registry) Get(name types.ProductName) (pm *protobuf.ProtoManager, ok bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	pm, ok = r.pms[name]
	return
}

func (r *Registry) Update(ctx context.Context, product ProductConfig) (config ProductConfig, err error) {
	var (
		key = product.Key()
		now = time.Now()
	)

	r.mutex.RLock()
	v, ok := r.pcs[key]
	r.mutex.RUnlock()
	if !ok {
		return config, errors.Errorf("unknown product, name: %s, branch: %s", product.ProductName, product.Branch)
	}

	if len(product.Projects) == 0 {
		product.Projects = make([]ProjectConfig, len(v.Projects))
		copy(product.Projects, v.Projects)
	}

	// generate an event of update proto registry
	event := &Event{
		Operator:  r.nid,
		Operation: Update,
		StartedAt: now,
	}

	config = ProductConfig{
		ProductName: product.ProductName,
		Branch:      product.Branch,
		Projects:    make([]ProjectConfig, 0, len(product.Projects)),
	}
	for _, project := range product.Projects {
		// set interval locks to avoid consecutive updates
		lockKey := fmt.Sprintf(
			"%s:%s:%s:%s", constUpdateLockKeyPrefix, product.ProductName, project.ProjectName, project.Branch,
		)
		ret, e := r.r.SetnxExCtx(
			ctx, lockKey, strconv.FormatInt(now.UnixMilli(), 10), int(constUpdateInterval.Seconds()),
		)
		if e != nil {
			err = es.Join(err, e)
			continue
		}

		if !ret {
			val, e := r.r.GetCtx(ctx, lockKey)
			if e != nil {
				err = es.Join(err, e)
				continue
			}

			ttl, e := r.r.TtlCtx(ctx, lockKey)
			if e != nil {
				err = es.Join(err, e)
				continue
			}

			err = es.Join(
				err, errors.Errorf(
					"cooldown time has not elapsed, product: %s, project: %s, branch: %s, updatedAt: %s, ttl: %d",
					product.ProductName, project.ProjectName, project.Branch, val, ttl,
				),
			)
			continue
		}

		config.Projects = append(config.Projects, project)
	}

	if len(config.Projects) == 0 {
		return config, err
	}

	pcs := map[string]ProductConfig{key: config}
	pcs, e := r.cloneOrUpdate(ctx, pcs, true, true)
	if e != nil {
		err = es.Join(err, e)
		return config, err
	}

	// update the `products` and `endedAt` of event
	event.Products = pcs
	event.EndedAt = time.Now()

	// broadcast the event of completing code updating
	if e = r.producer.Enqueue(
		ctx, &stream.Message{
			Stream: common.ProtoRegistryKey,
			Values: map[string]any{
				payloadOfMessageValuesKey: jsonx.MarshalToStringIgnoreError(event),
			},
		},
	); e != nil {
		err = es.Join(err, e)
		return config, err
	}

	if e = r.update(pcs); e != nil {
		err = es.Join(err, e)
		return config, err
	}

	config, ok = pcs[key]
	if !ok {
		r.Warnf("not found product config, key: %s", key)
	}

	return config, nil
}

func (r *Registry) drainInitChan() {
	// drain the init channel
	for {
		select {
		case <-r.initChan:
		case <-r.quitChan:
			return
		default:
			return
		}
	}
}

func distinct(configs []ProductConfig) map[string]ProductConfig {
	out := make(map[string]ProductConfig, len(configs))

	for _, config := range configs {
		key := config.Key()
		if _, ok := out[key]; !ok {
			out[key] = config
		}
	}

	return out
}

func mkdir(path string) error {
	fi, err := os.Stat(path)
	if err != nil {
		if !os.IsNotExist(err) {
			return err
		}

		err = os.MkdirAll(path, 0o755)
	} else if !fi.IsDir() {
		err = errors.Errorf("the path[%s] is not a directory", path)
	}

	return err
}

func toPath(name string) string {
	return strings.ReplaceAll(name, constSlash, constUnderline)
}

func getNameFromGitURL(gitURL string) (string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return "", err
	}

	path := u.Path

	// 移除开头的斜杠
	path = strings.TrimLeft(path, constSlash)

	// 移除`.git`扩展名
	path = strings.TrimSuffix(path, constDotGit)

	// 分割路径，获取最后一个部分，即目录名称
	parts := strings.Split(path, constSlash)
	name := parts[len(parts)-1]

	return name, nil
}

func getPathByGitURLAndBranch(path, gitURL, branch string) (string, error) {
	if gitURL == "" {
		return "", errEmptyGitURL
	} else if branch == "" {
		return "", errEmptyBranch
	}

	name, err := getNameFromGitURL(gitURL)
	if err != nil {
		return "", err
	}

	subPath := filepath.Join(path, toPath(name+constColon+branch))
	return subPath, nil
}
