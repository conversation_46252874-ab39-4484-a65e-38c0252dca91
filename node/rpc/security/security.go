package security

import (
	"net/http"
	"sync"

	httppb "google.golang.org/genproto/googleapis/rpc/http"
)

type (
	// Deprecated: use `security.Config` in `gitlab.ttyuyin.com/TestDevelopment/qet-backend-common` instead.
	Config struct {
		Enabled bool        `json:",default=true"`
		Headers http.Header `json:",optional,omitempty"`
	}

	Handler struct {
		enabled bool
		headers http.Header
	}
)

var (
	once    sync.Once
	handler *Handler
)

// InitSecurityHandler init the security handler by config
// Deprecated: use `security.InitSecurityHandler` in `gitlab.ttyuyin.com/TestDevelopment/qet-backend-common` instead.
func InitSecurityHandler(config Config) {
	once.Do(
		func() {
			handler = NewHandler(config)
		},
	)
}

// HandleHeaders add the security header to the request headers
// Deprecated: use `security.HandleHeaders` in `gitlab.ttyuyin.com/TestDevelopment/qet-backend-common` instead.
func HandleHeaders(headers []*httppb.HttpHeader) []*httppb.HttpHeader {
	return handler.Handle(headers)
}

func NewHandler(config Config) *Handler {
	return &Handler{
		enabled: config.Enabled,
		headers: config.Headers,
	}
}

func (h *Handler) Handle(headers []*httppb.HttpHeader) []*httppb.HttpHeader {
	if !h.enabled {
		return headers
	}

	for k, vs := range h.headers {
		if len(k) == 0 {
			continue
		}

		for _, v := range vs {
			headers = append(
				headers, &httppb.HttpHeader{
					Key:   k,
					Value: v,
				},
			)
		}
	}

	return headers
}
