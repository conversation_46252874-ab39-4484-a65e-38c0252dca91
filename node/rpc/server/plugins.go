package server

import (
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/bpm"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/currency"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/http"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/recommend"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tdun"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt"
	ttlogic "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/tt/logic"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clienttypes/wefly"
)

func init() {
	http.Init()      // 注册`HTTP`客户端
	tt.Init()        // 注册`TT`客户端
	bpm.Init()       // 注册`bpm(企业效能)`客户端
	wefly.Init()     // 注册`wefly(起飞中台)`客户端
	tdun.Init()      // 注册`tdun(T盾)`客户端
	currency.Init()  // 注册`currency(货币中台)`客户端
	recommend.Init() // 注册`recommend(推荐中台)`客户端

	ttlogic.Init() // 注册`TT`自定义逻辑
}
