package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/cleaner"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/clientpool"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/config"
	nodeservice "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/server/nodeservice"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/limiter"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/proto"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/registry"
)

// NewRpcServer for single server startup
func NewRpcServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, server.TearDownFunc, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, nil, errors.Errorf("failed to new rpc server, cause by the config[%T] isn't a rpc config", c)
	}

	ctx := svc.NewServiceContext(cc)

	rs := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			pb.RegisterNodeServiceServer(grpcServer, nodeservice.NewNodeServiceServer(ctx))

			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)
	// 添加`gRPC`服务器选项
	rs.AddOptions(buildGRPCServerOptions(cc.ServerOptions)...)

	sid := common.ID()
	s := registry.NewPubServer(
		registry.Config{
			Key:       common.NodeInfoKey(sid),
			ListenOn:  cc.ListenOn(),
			RedisConf: cc.Redis.RedisConf,

			WrappedService: rs,
			StopFunc: func() {
				clientpool.Close()
				proto.Close()
			},
		},
		registry.WithServerID(sid),
	)

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	// 执行前置操作
	if err := setupOperation(cc); err != nil {
		return nil, nil, errors.Errorf("failed to setup operation, error: %+v", err)
	}

	tdf := server.TearDownFunc(
		func() {
			rs.AddUnaryInterceptors(serverinterceptors.ValidateWithCustomValidatorUnaryServerInterceptor(ctx.Validator))
			rs.AddStreamInterceptors(serverinterceptors.ValidateWithCustomValidatorStreamServerInterceptor(ctx.Validator))
		},
	)

	return s, tdf, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithRpcServerMiddlewaresConf(
			server.RpcServerInterceptorsConf{
				UnUseUserInfo: true,
			},
		),
		server.WithNewServiceAndTearDownFunc(NewServer),
	}
}

// buildGRPCServerOptions 构建gRPC服务器选项以防止30分钟连接重置
func buildGRPCServerOptions(opts common.ServerOptions) []grpc.ServerOption {
	serverOpts := make([]grpc.ServerOption, 0, 2)

	// 配置keepalive参数
	parameters := keepalive.ServerParameters{
		Time:    opts.KeepaliveTime,
		Timeout: opts.KeepaliveTimeout,
	}

	// 如果MaxConnectionAge为0，表示禁用连接年龄限制
	if opts.MaxConnectionAge > 0 {
		parameters.MaxConnectionAge = opts.MaxConnectionAge
	}

	// 如果MaxConnectionIdle为0，表示禁用空闲连接超时
	if opts.MaxConnectionIdle > 0 {
		parameters.MaxConnectionIdle = opts.MaxConnectionIdle
	}

	// 如果MaxConnectionAgeGrace为0，表示禁用连接年龄宽限期
	if opts.MaxConnectionAgeGrace > 0 {
		parameters.MaxConnectionAgeGrace = opts.MaxConnectionAgeGrace
	}
	serverOpts = append(serverOpts, grpc.KeepaliveParams(parameters))

	// 配置keepalive enforcement policy
	policy := keepalive.EnforcementPolicy{
		MinTime:             opts.KeepaliveMinTime,
		PermitWithoutStream: opts.KeepalivePermitWithoutStream,
	}
	serverOpts = append(serverOpts, grpc.KeepaliveEnforcementPolicy(policy))

	return serverOpts
}

func setupOperation(c config.Config) error {
	// 初始化客户端池
	if err := clientpool.InitClientPool(c.ClientPool, c.Redis.RedisConf); err != nil {
		return err
	}

	// 初始化`proto`注册中心
	if err := proto.InitProtoRegistry(c.Proto, c.Redis.RedisConf); err != nil {
		return err
	}

	// 初始化限流器
	limiter.InitRateLimiter(c.Limit, c.Redis.RedisConf)

	// 初始化安全处理器
	security.InitSecurityHandler(c.Security)

	// 初始化清理器
	if err := cleaner.InitCleaner(c.Cleaner, c.Redis.RedisConf); err != nil {
		return err
	}

	return nil
}
