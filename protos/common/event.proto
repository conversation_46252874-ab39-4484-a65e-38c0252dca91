syntax = "proto3";

package common;

import "common/info.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb";

// Operate 操作类型
enum OperateType {
  OT_UNKNOWN = 0;

  CREATE = 1; // 增
  DELETE = 2; // 删
  MODIFY = 3; // 改
}

// TargetType 对象类型
enum TargetType {
  TT_UNKNOWN = 0;

  ROUTER = 1; // 路由服务
  NODE = 2; // 节点服务
  CLIENT = 3; // 客户端
}

// Event 事件
message Event {
  OperateType operate = 1; // 操作类型
  TargetType target = 2; // 对象类型
  oneof item {
    ServerInfo server_info = 3;
    ClientInfo client_info = 4;
  }
}
