project=$(basename $(dirname $(realpath $0)))
base_path=$(dirname $(dirname $(dirname $(realpath $0))))
echo "Project: ${project}\nBase Path: ${base_path}\n"

module=""

function current_module() {
    module=$(grep -E "^module" "${base_path}/go.mod" | awk '{print $2}')
    if [ "${module}" == "" ]; then
      echo "current module not found, please check the file[${base_path}/go.mod]"
      exit 1
    fi
    echo "current module: ${module}"
}

function rpc() {
  input_path="${base_path}/protos/${project}"
  output_path="${base_path}/${project}/pb"
  module_prefix="${module}/${project}/pb"

  if [ ! -d "${output_path}" ]; then
    mkdir -p "${output_path}"
  fi

  # third_party_proto_paths=$(go list -m -f '{{.Dir}}' 'github.com/envoyproxy/protoc-gen-validate' | xargs -I {} echo -n "--proto_path='{}' ")
  third_party_proto_paths="--proto_path=${base_path}/dep_protos/googleapis --proto_path=${base_path}/dep_protos/protovalidate/proto/protovalidate"
  private_proto_paths=$(go list -m -f '{{.Dir}}' 'gitlab.ttyuyin.com/TestDevelopment/qet-backend-common' ${module} | xargs -I {} echo -n "--proto_path='{}/protos' ")

  protoc_cmd="protoc ${third_party_proto_paths} ${private_proto_paths} --go_out=${output_path} --go-grpc_out=${output_path} --go_opt=module=${module_prefix} --go-grpc_opt=module=${module_prefix} ${input_path}/*.proto"
  inject_tag_cmd="protoc-go-inject-tag -input=\"${output_path}/*.pb.go\" -remove_tag_comment"
  format_cmd="go list -f {{.Dir}} ${output_path}/... | xargs gofmt -s -w; goimports -l -w -local ${module%[\\/]*} ${output_path}/"

  eval ${protoc_cmd}
  type protoc-go-inject-tag > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    eval ${inject_tag_cmd}
  fi
  eval ${format_cmd}
}

current_module
rpc
