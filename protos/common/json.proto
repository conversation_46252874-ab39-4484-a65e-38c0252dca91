syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb";

// `Value` represents a dynamically typed value which can be either
// null, a number, a string, a boolean, a recursive struct value, or a
// list of values.
//
// The JSON representation for `Value` is JSON value.
message Value {
  enum Type {
    NULL = 0;
    BOOL = 1;
    INT = 2;
    UINT = 3;
    FLOAT = 4;
    STRING = 5;
    OBJECT = 6;
    ARRAY = 7;
  }

  Type type = 1;
  bytes value = 2;
}

// `Object` represents a structured data value, consisting of fields
// which map to dynamically typed values.
//
// The JSON representation for `Object` is JSON object.
message Object {
  map<string, Value> fields = 1;
}

// `Array` is a wrapper around a repeated field of values.
//
// The JSON representation for `Array` is JSON array.
message Array {
  repeated Value items = 1;
}
