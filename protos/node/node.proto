syntax = "proto3";

package node;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb";

import "buf/validate/validate.proto";
import "google/protobuf/struct.proto";
import "google/rpc/http.proto";

import "common/info.proto";


// SearchClient 搜索客户端条件
message SearchClient {
  oneof condition {
    string cid = 1; // 通过客户端ID查找客户端
    CreateClientReq create = 2; // 通过创建客户端的请求信息查找客户端
  }
}

// NodeService 节点服务
service NodeService {
  //CreateClient 创建客户端
  rpc CreateClient(CreateClientReq) returns (CreateClientResp);
  //DeleteClient 删除客户端
  rpc DeleteClient(DeleteClientReq) returns (DeleteClientResp);
  //ViewClient 查看客户端
  rpc ViewClient(ViewClientReq) returns (ViewClientResp);
  //ApiCall 接口调用
  rpc ApiCall(ApiCallReq) returns (ApiCallResp);
  //UpdateProto 更新`proto`项目
  rpc UpdateProto(UpdateProtoReq) returns (UpdateProtoResp);
  //JsonToProto `json`数据转换成`proto`字符串
  rpc JsonToProto(JsonToProtoReq) returns (JsonToProtoResp);
  //ProtoToJson `proto`字符串转换成`json`数据
  rpc ProtoToJson(ProtoToJsonReq) returns (ProtoToJsonResp);
}

message CreateClientReq {
  string type = 1 [(buf.validate.field).string = {min_len: 1}]; // 客户端类型
  string url = 2 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {uri: true}}]; // 服务端连接地址
  google.protobuf.Struct custom_fields = 3 [(buf.validate.field).ignore = IGNORE_IF_UNPOPULATED]; // 客户端的请求自定义字段
}
message CreateClientResp {
  common.ClientInfo item = 1;
}

message DeleteClientReq {
  string cid = 1 [(buf.validate.field).string = {pattern: "(?:^client_id:.+?)"}]; // 客户端ID
}
message DeleteClientResp {
  common.ClientInfo item = 1;
}

message ViewClientReq {
  string cid = 1 [(buf.validate.field).string = {pattern: "(?:^client_id:.+?)"}]; // 客户端ID
}
message ViewClientResp {
  common.ClientInfo item = 1;
}

message ApiCallReq {
  string cid = 1 [(buf.validate.field).string = {pattern: "(?:^client_id:.+?)"}]; // 客户端ID
  string url = 2 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {uri_ref: true}}]; // 请求路径
  string method = 3 [(buf.validate.field).ignore = IGNORE_IF_UNPOPULATED]; // 请求方法
  repeated google.rpc.HttpHeader headers = 4 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 请求头
  google.protobuf.Value body = 5 [(buf.validate.field).ignore = IGNORE_IF_UNPOPULATED]; // 请求体
  string authority = 6 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // gRPC服务器名称
  google.protobuf.Struct custom_fields = 7 [(buf.validate.field).ignore = IGNORE_IF_UNPOPULATED]; // 客户端的请求自定义字段
}
message ApiCallResp {
  message CallResp {
    repeated google.rpc.HttpHeader header = 1; // 响应头
    google.protobuf.Value body = 2; // 响应体
    int32 status = 3; // 响应状态码
  }

  common.ClientInfo client_info = 1;
  google.protobuf.Struct custom_fields = 2;
  CallResp call_resp = 3;
}

message UpdateProtoReq {
  message ProjectConfig {
    string project_name = 1 [(buf.validate.field) = {string: {min_len: 1}}]; // 项目名称
    string git_url = 2 [(buf.validate.field) = {string: {uri: true}}]; // Git地址
    string branch = 3 [(buf.validate.field) = {string: {min_len: 1}}]; // 分支名称
    string import_path = 4 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // 导入路径
    repeated string exclude_paths = 5 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 排除的路径
    repeated string exclude_files = 6 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 排除的文件
    repeated DependenceConfig dependencies = 7 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 依赖配置
  }
  message DependenceConfig {
    oneof config {
      GitConfig git = 1; // Git配置
      string local_path = 2 [(buf.validate.field) = {string: {min_len: 1}}]; // 本地路径
    }
  }
  message GitConfig {
    string git_url = 1 [(buf.validate.field) = {string: {uri: true}}]; // Git地址
    string branch = 2 [(buf.validate.field) = {string: {min_len: 1}}]; // 分支名称
    string import_path = 3 [(buf.validate.field) = {ignore: IGNORE_IF_UNPOPULATED, string: {min_len: 1}}]; // 导入路径
  }

  string product_name = 1 [(buf.validate.field) = {string: {min_len: 2}}]; // 产品名称
  string branch = 2 [(buf.validate.field) = {string: {min_len: 1}}]; // 分支名称
  repeated ProjectConfig projects = 3 [(buf.validate.field).repeated.items.ignore = IGNORE_IF_UNPOPULATED]; // 项目配置
}
message UpdateProtoResp {
  message ProjectInfo {
    string project_name = 1; // 项目名称
    string branch = 2; // 分支名称
    Commit commit = 3; // 提交信息
  }
  message Commit {
    string hash = 1;    // 提交哈希
    string author = 2;  // 提交作者
    string date = 3;    // 提交日期
    string message = 4; // 提交信息
  }

  string product_name = 1; // 产品名称
  string branch = 2; // 分支名称
  repeated ProjectInfo projects = 3; // 项目信息
}

message JsonToProtoReq {
  string product_name = 1 [(buf.validate.field).string = {min_len: 1}]; // 产品名称
  string message = 2 [(buf.validate.field).string = {min_len: 1}]; // 目标信息名称
  google.protobuf.Value data = 3; // 待转换数据
}
message JsonToProtoResp {
  string proto_string = 1; // 转换后的数据
}

message ProtoToJsonReq {
  string product_name = 1 [(buf.validate.field).string = {min_len: 1}]; // 产品名称
  string message = 2 [(buf.validate.field).string = {min_len: 1}]; // 目标信息名称
  string data = 3; // 待转换数据
}
message ProtoToJsonResp {
  google.protobuf.Value json_data = 1; // 转换后的数据
}
