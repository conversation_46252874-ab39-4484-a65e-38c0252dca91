package registry

import (
	"fmt"
	"sync"

	zeroredis "github.com/zeromicro/go-zero/core/stores/redis"
)

var (
	cache = make(map[string]*Registry)
	mutex sync.RWMutex
)

func GetRegistry(c zeroredis.RedisConf, opts ...RegOption) *Registry {
	key := getCacheKey(c, opts...)
	mutex.RLock()
	r, ok := cache[key]
	mutex.RUnlock()
	if ok {
		return r
	}

	defer func() {
		if len(key) > 0 && r != nil {
			setRegistry(key, r)
		}
	}()
	r = NewRegistry(c, opts...)
	return r
}

func setRegistry(key string, r *Registry) {
	if len(key) == 0 || r == nil {
		return
	}

	mutex.Lock()
	defer mutex.Unlock()

	cache[key] = r
}

func delRegistry(r *Registry) {
	mutex.Lock()
	defer mutex.Unlock()

	for k, v := range cache {
		if v == r {
			delete(cache, k)
			break
		}
	}
}

func getCacheKey(c zeroredis.RedisConf, opts ...RegOption) string {
	key := genKeyByRedisConf(c)
	r := &Registry{}
	for _, opt := range opts {
		opt(r)
	}

	return fmt.Sprintf("%s?key=%s&toSub=%t", key, r.key, r.toSub)
}
