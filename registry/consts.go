package registry

const (
	RouterEventRedisChannel = "router:event:channel"
	NodeEventRedisChannel   = "node:event:channel"
	ClientEventRedisChannel = "client:event:channel"
)

type Operation string

const (
	Register  Operation = "register"  // 注册
	Keepalive Operation = "keepalive" // 保活
)

type Operate int

const (
	Create Operate = iota + 1 // 增
	Delete                    // 删
	Modify                    // 改
)
