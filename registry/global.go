package registry

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

var (
	globalRegistry *Registry
	globalOnce     sync.Once

	errRegistryUninitialized = errors.New("the global registry is uninitialized")
)

func InitServerRegistry(c redis.RedisConf) {
	globalOnce.Do(
		func() {
			globalRegistry = NewRegistry(c)
		},
	)
}

func getRegistry() (*Registry, error) {
	if globalRegistry == nil {
		return nil, errRegistryUninitialized
	}

	return globalRegistry, nil
}

func RegisterServerInfo(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.RegisterServerInfo(context.Background(), info)
}

func RegisterRouterInfoAndBroadcast(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.RegisterRouterInfoAndBroadcast(context.Background(), info)
}

func RegisterNodeInfoAndBroadcast(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.RegisterNodeInfoAndBroadcast(context.Background(), info)
}

func UnregisterServerInfo(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterServerInfo(context.Background(), info)
}

func UnregisterRouterInfoAndBroadcast(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterRouterInfoAndBroadcast(context.Background(), info)
}

func UnregisterNodeInfoAndBroadcast(info *pb.ServerInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterNodeInfoAndBroadcast(context.Background(), info)
}

func RegisterClientInfo(info *pb.ClientInfo, operation Operation) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.RegisterClientInfo(context.Background(), info, operation)
}

func RegisterClientInfoAndBroadcast(info *pb.ClientInfo, operation Operation) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.RegisterClientInfoAndBroadcast(context.Background(), info, operation)
}

func UnregisterClientInfo(info *pb.ClientInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterClientInfo(context.Background(), info)
}

func UnregisterClientInfoAndBroadcast(info *pb.ClientInfo) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterClientInfoAndBroadcast(context.Background(), info)
}

func UnregisterAllClientInfo(nid string) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.UnregisterAllClientInfo(context.Background(), nid)
}

func GetAllRouterInfo() ([]*pb.RouterServerInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetAllRouterInfo(context.Background())
}

func GetAllNodeInfo(ctx context.Context) ([]*pb.NodeServerInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetAllNodeInfo(context.Background())
}

func GetRouterInfoByServerID(rid string) (*pb.RouterServerInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetRouterInfoByServerID(context.Background(), rid)
}

func GetNodeInfoByServerID(nid string) (*pb.NodeServerInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetNodeInfoByServerID(context.Background(), nid)
}

func GetClientInfoByClientID(cid string) (*pb.ClientInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetClientInfoByClientID(context.Background(), cid)
}

func GetUnmanagedClientInfoByClientID(cid string) (*pb.ClientInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetUnmanagedClientInfoByClientID(context.Background(), cid)
}

func GetUserInfoByUserID(uid string) (*pb.UserInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetUserInfoByUserID(context.Background(), uid)
}

func GetNodeInfoByClientID(cid string) (*pb.NodeServerInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetNodeInfoByClientID(context.Background(), cid)
}

func GetNodeInfoByUserID(uid string) (serverInfo *pb.NodeServerInfo, err error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetNodeInfoByUserID(context.Background(), uid)
}

func GetAllClientInfoByNodeID(nid string) ([]*pb.ClientInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetAllClientInfoByNodeID(context.Background(), nid)
}

func GetAllNodeAttrInfo() ([]*pb.AttrInfo, error) {
	r, err := getRegistry()
	if err != nil {
		return nil, err
	}

	return r.GetAllNodeAttrInfo(context.Background())
}

func KeepaliveAllInfoByNodeID(nid string) error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.KeepaliveAllInfoByNodeID(context.Background(), nid)
}

func GetChannel(opts ...red.ChannelOption) <-chan *red.Message {
	r, err := getRegistry()
	if err != nil {
		return nil
	}

	return r.GetChannel(opts...)
}

func Close() error {
	r, err := getRegistry()
	if err != nil {
		return err
	}

	return r.Close()
}
