package registry

import (
	"context"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/netx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/rest"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

const (
	constAllEths    = "0.0.0.0"
	constLocalEths  = "127.0.0.1"
	constEnvPodIp   = "POD_IP"
	constEnvMyPodIp = "MY_POD_IP"

	intervalOfKeepalive = time.Second
)

type (
	PubOption func(*pb.ServerInfo)
)

var (
	_ server.ApiService = (*keepAliveServer)(nil)
	_ server.RpcService = (*keepAliveServer)(nil)
)

// NewPubServer returns a service.Service with given config of c and options defined in opts.
func NewPubServer(c Config, opts ...PubOption) service.Service {
	host, port := handleListenOn(c.ListenOn)
	info := &pb.ServerInfo{
		Key:      c.Key,
		ServerId: common.GenerateServerID(),
		Host:     host,
		Port:     int32(port),
	}

	for _, opt := range opts {
		opt(info)
	}

	var typ string
	if strings.HasPrefix(info.GetKey(), common.RouterInfoKeyPrefix()) {
		typ = common.RouterServiceName
	} else if strings.HasPrefix(info.GetKey(), common.NodeInfoKeyPrefix()) {
		typ = common.NodeServiceName
	}

	ctx := context.Background()
	reg := GetRegistry(c.RedisConf)
	info.Registry = reg.Key()

	return &keepAliveServer{
		Logger:  logx.WithContext(ctx),
		Service: c.WrappedService,

		ctx:  ctx,
		typ:  typ,
		reg:  reg,
		info: info,
		quit: make(chan lang.PlaceholderType),

		startFunc: c.StartFunc,
		stopFunc:  c.StopFunc,
	}
}

// WithServerID returns an PubOption function to set the ServerInfo.ServerID.
func WithServerID(id string) PubOption {
	return func(info *pb.ServerInfo) {
		info.ServerId = id
	}
}

// WithHost returns an PubOption function to set the ServerInfo.Host.
func WithHost(host string) PubOption {
	return func(info *pb.ServerInfo) {
		info.Host = host
	}
}

// WithPort returns an PubOption function to set the ServerInfo.Port.
func WithPort(port int32) PubOption {
	return func(info *pb.ServerInfo) {
		info.Port = port
	}
}

type keepAliveServer struct {
	logx.Logger
	service.Service

	ctx  context.Context
	typ  string
	reg  *Registry
	info *pb.ServerInfo
	quit chan lang.PlaceholderType

	startFunc func()
	stopFunc  func()
}

func (s *keepAliveServer) Start() {
	s.info.StartedAt = timestamppb.New(time.Now().In(time.Local))

	if s.startFunc != nil {
		s.startFunc()
	}

	if err := s.register(true); err != nil {
		logx.Must(errors.Wrap(err, "failed to register the keepalive server"))
	}

	threading.GoSafe(s.keepalive)
	s.Service.Start()
}

func (s *keepAliveServer) Stop() {
	if s.stopFunc != nil {
		s.stopFunc()
	}

	if err := s.unregister(); err != nil {
		s.Errorf("failed to unregister, error: %+v", err)
	}

	s.Service.Stop()
}

func (s *keepAliveServer) register(broadcast bool) error {
	if broadcast && s.typ != "" {
		switch s.typ {
		case common.RouterServiceName:
			return s.reg.RegisterRouterInfoAndBroadcast(s.ctx, s.info)
		case common.NodeServiceName:
			return s.reg.RegisterNodeInfoAndBroadcast(s.ctx, s.info)
		}
	}

	return s.reg.RegisterServerInfo(s.ctx, s.info)
}

func (s *keepAliveServer) unregister() error {
	defer func() {
		_ = s.reg.Close()
	}()

	close(s.quit)
	switch s.typ {
	case common.RouterServiceName:
		return s.reg.UnregisterRouterInfoAndBroadcast(s.ctx, s.info)
	case common.NodeServiceName:
		return s.reg.UnregisterNodeInfoAndBroadcast(s.ctx, s.info)
	default:
		return s.reg.UnregisterServerInfo(s.ctx, s.info)
	}
}

func (s *keepAliveServer) keepalive() {
	ticker := timewheel.NewTicker(intervalOfKeepalive)
	defer ticker.Stop()

	for {
		select {
		case <-s.quit:
			return
		case <-ticker.C:
			if err := s.register(false); err != nil {
				s.Errorf("failed to keepalive for the server info, error: %+v", err)
			}
		}
	}
}

func (s *keepAliveServer) Use(middleware rest.Middleware) {
	ss, ok := s.Service.(server.ApiService)
	if ok {
		ss.Use(middleware)
	}
}

func (s *keepAliveServer) AddOptions(options ...grpc.ServerOption) {
	ss, ok := s.Service.(server.RpcService)
	if ok {
		ss.AddOptions(options...)
	}
}

func (s *keepAliveServer) AddStreamInterceptors(interceptors ...grpc.StreamServerInterceptor) {
	ss, ok := s.Service.(server.RpcService)
	if ok {
		ss.AddStreamInterceptors(interceptors...)
	}
}

func (s *keepAliveServer) AddUnaryInterceptors(interceptors ...grpc.UnaryServerInterceptor) {
	ss, ok := s.Service.(server.RpcService)
	if ok {
		ss.AddUnaryInterceptors(interceptors...)
	}
}

func handleListenOn(listenOn string) (host string, port int) {
	fields := strings.Split(listenOn, ":")
	if len(fields) == 0 {
		return
	}

	host = fields[0]
	port = common.ConstPort80
	if len(fields) >= 2 {
		port, _ = strconv.Atoi(fields[1])
	}

	if len(host) > 0 && host != constAllEths && host != constLocalEths {
		return host, port
	}

	if ip := os.Getenv(constEnvPodIp); len(ip) > 0 {
		return ip, port
	}
	if ip := os.Getenv(constEnvMyPodIp); len(ip) > 0 {
		return ip, port
	}
	if ip := netx.InternalIp(); len(ip) > 0 {
		return ip, port
	}

	return host, port
}
