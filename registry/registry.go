package registry

import (
	"context"
	es "errors"
	"fmt"
	"strings"
	"sync"

	"github.com/pkg/errors"
	goredis "github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/breaker"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	zeroredis "github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

const (
	defaultExpireTime = 30 // unit: second (增加到30秒，提供更多容错时间)

	asterisk = "*"
)

type (
	RegOption func(*Registry)
)

func WithKey(key string) RegOption {
	return func(r *Registry) {
		r.key = key
	}
}

func WithSubscribe() RegOption {
	return func(r *Registry) {
		r.toSub = true
	}
}

type Registry struct {
	logx.Logger

	ctx context.Context
	rdb goredis.UniversalClient
	brk breaker.Breaker

	key   string
	toSub bool
	ps    *goredis.PubSub

	closeOnce sync.Once
	closeErr  error
}

func NewRegistry(c zeroredis.RedisConf, opts ...RegOption) *Registry {
	ctx := context.Background()
	r := &Registry{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, common.ID())),

		ctx: ctx,
		brk: breaker.NewBreaker(),

		key: genKeyByRedisConf(c),
	}

	for _, opt := range opts {
		opt(r)
	}

	r.initRDB(c)

	proc.AddShutdownListener(
		func() {
			_ = r.Close()
		},
	)

	return r
}

func (r *Registry) initRDB(c zeroredis.RedisConf) {
	if r.toSub {
		r.rdb = qetredis.NewClient(c)
		r.initPubSub()
	} else {
		r.rdb, _ = qetredis.GetClient(c)
	}
}

func (r *Registry) initPubSub() {
	switch v := r.rdb.(type) {
	case *goredis.Client:
		r.ps = v.WithTimeout(0).Subscribe(r.ctx, NodeEventRedisChannel, ClientEventRedisChannel)
	case *goredis.ClusterClient:
		_ = v.ForEachMaster(
			r.ctx, func(ctx context.Context, client *goredis.Client) error {
				r.ps = client.WithTimeout(0).Subscribe(ctx, NodeEventRedisChannel, ClientEventRedisChannel)
				return nil
			},
		)
	default:
		logx.Must(
			errors.Errorf(
				"invalid redis client type, expected: %T or %T, but got: %T",
				(*goredis.Client)(nil), (*goredis.ClusterClient)(nil), v,
			),
		)
	}
}

func (r *Registry) runScript(ctx context.Context, script *goredis.Script, keys []string, args ...any) (
	val any, err error,
) {
	err = r.brk.DoWithAcceptable(
		func() error {
			val, err = script.Run(ctx, r.rdb, keys, args...).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)

	return
}

func (r *Registry) publish(ctx context.Context, channel string, message any) (val int64, err error) {
	err = r.brk.DoWithAcceptable(
		func() error {
			val, err = r.rdb.Publish(ctx, channel, message).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)

	return val, err
}

func (r *Registry) Key() string {
	return r.key
}

func (r *Registry) RegisterServerInfo(ctx context.Context, info *pb.ServerInfo) (err error) {
	_, err = r.runScript(
		ctx, common.ServerRegisterScript, []string{info.GetKey()},
		protobuf.MarshalJSONToStringIgnoreError(info), defaultExpireTime,
	)

	return err
}

func (r *Registry) RegisterRouterInfoAndBroadcast(ctx context.Context, info *pb.ServerInfo) (err error) {
	err = r.RegisterServerInfo(ctx, info)
	if err != nil {
		return err
	}

	// publish a create router event
	val, err := r.publish(
		ctx, RouterEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_CREATE,
				Target:  pb.TargetType_ROUTER,
				Item: &pb.Event_ServerInfo{
					ServerInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a create router event, router info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), RouterEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a create router event, router info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), RouterEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) RegisterNodeInfoAndBroadcast(ctx context.Context, info *pb.ServerInfo) (err error) {
	err = r.RegisterServerInfo(ctx, info)
	if err != nil {
		return err
	}

	// publish a create node event
	val, err := r.publish(
		ctx, NodeEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_CREATE,
				Target:  pb.TargetType_NODE,
				Item: &pb.Event_ServerInfo{
					ServerInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a create node event, node info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), NodeEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a create node event, node info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), NodeEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) UnregisterServerInfo(ctx context.Context, info *pb.ServerInfo) (err error) {
	return r.brk.DoWithAcceptable(
		func() error {
			return r.rdb.Del(ctx, info.GetKey()).Err()
		}, acceptable,
	)
}

func (r *Registry) UnregisterRouterInfoAndBroadcast(ctx context.Context, info *pb.ServerInfo) (err error) {
	err = r.UnregisterServerInfo(ctx, info)
	if err != nil {
		return err
	}

	// publish a delete router event
	val, err := r.publish(
		ctx, RouterEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_DELETE,
				Target:  pb.TargetType_ROUTER,
				Item: &pb.Event_ServerInfo{
					ServerInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a delete router event, router info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), RouterEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a delete router event, router info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), RouterEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) UnregisterNodeInfoAndBroadcast(ctx context.Context, info *pb.ServerInfo) (err error) {
	err = r.UnregisterServerInfo(ctx, info)
	if err != nil {
		return err
	}

	// publish a delete node event
	val, err := r.publish(
		ctx, NodeEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_DELETE,
				Target:  pb.TargetType_NODE,
				Item: &pb.Event_ServerInfo{
					ServerInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a delete node event, node info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), NodeEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a delete node event, node info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), NodeEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) RegisterClientInfo(ctx context.Context, info *pb.ClientInfo, operation Operation) (err error) {
	if operation != Keepalive {
		operation = Register
	}

	ui := info.GetUserInfo()
	_, err = r.runScript(
		ctx, common.ClientRegisterScriptV3, []string{
			common.ConstClientsInfoKey,
			common.NodeWithClientsKey(info.GetNid()),
			common.ConstUsersInfoKey,
			common.NodeWithUsersKey(info.GetNid()),
			common.ConstUnmanagedClientsInfoKey,
		},
		string(operation),
		info.GetCid(),
		protobuf.MarshalJSONToStringIgnoreError(info),
		ui.GetUid(),
		protobuf.MarshalJSONToStringIgnoreError(ui),
		defaultExpireTime*2,
		defaultExpireTime*6,
	)

	return err
}

func (r *Registry) RegisterClientInfoAndBroadcast(
	ctx context.Context, info *pb.ClientInfo, operation Operation,
) (err error) {
	err = r.RegisterClientInfo(ctx, info, operation)
	if err != nil {
		return err
	}

	// publish a create client event
	val, err := r.publish(
		ctx, ClientEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_CREATE,
				Target:  pb.TargetType_CLIENT,
				Item: &pb.Event_ClientInfo{
					ClientInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a create client event, client info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), ClientEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a create client event, client info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), ClientEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) UnregisterClientInfo(ctx context.Context, info *pb.ClientInfo) (err error) {
	_, err = r.runScript(
		ctx, common.ClientUnregisterScriptV2, []string{
			common.ConstClientsInfoKey,
			common.NodeWithClientsKey(info.GetNid()),
			common.ConstUsersInfoKey,
			common.NodeWithUsersKey(info.GetNid()),
		},
		info.GetCid(),
		info.GetUserInfo().GetUid(),
	)

	return err
}

func (r *Registry) UnregisterClientInfoAndBroadcast(ctx context.Context, info *pb.ClientInfo) (err error) {
	err = r.UnregisterClientInfo(ctx, info)
	if err != nil {
		return err
	}

	// publish a delete client event
	val, err := r.publish(
		ctx, ClientEventRedisChannel, protobuf.MarshalJSONToStringIgnoreError(
			&pb.Event{
				Operate: pb.OperateType_DELETE,
				Target:  pb.TargetType_CLIENT,
				Item: &pb.Event_ClientInfo{
					ClientInfo: info,
				},
			},
		),
	)
	if err != nil {
		r.Errorf(
			"failed to publish a delete client event, client info: %s, channel: %q, error: %+v",
			protobuf.MarshalJSONToStringIgnoreError(info), ClientEventRedisChannel, err,
		)
	} else {
		r.Infof(
			"succeeded to publish a delete client event, client info: %s, channel: %q, number of subscriber: %d",
			protobuf.MarshalJSONToStringIgnoreError(info), ClientEventRedisChannel, val,
		)
	}

	return err
}

func (r *Registry) UnregisterAllClientInfo(ctx context.Context, nid string) (err error) {
	_, err = r.runScript(
		ctx, common.AllClientsUnregisterScriptV3, []string{
			common.ConstClientsInfoKey,
			common.NodeWithClientsKey(nid),
			common.ConstUsersInfoKey,
			common.NodeWithUsersKey(nid),
			common.ConstUnmanagedClientsInfoKey,
		},
	)

	return err
}

func (r *Registry) GetAllRouterInfo(ctx context.Context) (val []*pb.RouterServerInfo, err error) {
	infos, err := r.getMatchServerInfo(ctx, common.RouterInfoKeyPrefix())
	if err != nil {
		return nil, err
	}

	val = make([]*pb.RouterServerInfo, 0, len(infos))
	for _, info := range infos {
		val = append(
			val, &pb.RouterServerInfo{
				Info:     info,
				Metadata: &pb.RouterServerInfo_Metadata{},
			},
		)
	}

	return val, nil
}

func (r *Registry) GetAllNodeInfo(ctx context.Context) (val []*pb.NodeServerInfo, err error) {
	infos, err := r.getMatchServerInfo(ctx, common.NodeInfoKeyPrefix())
	if err != nil {
		return nil, err
	}

	val = make([]*pb.NodeServerInfo, 0, len(infos))
	err = mr.MapReduceVoid[*pb.ServerInfo, *pb.NodeServerInfo](
		func(source chan<- *pb.ServerInfo) {
			for _, info := range infos {
				if info == nil {
					continue
				}

				source <- info
			}
		},
		func(item *pb.ServerInfo, writer mr.Writer[*pb.NodeServerInfo], cancel func(error)) {
			if item == nil {
				return
			}

			info := &pb.NodeServerInfo{
				Info:     item,
				Metadata: &pb.NodeServerInfo_Metadata{},
			}
			number, e := r.rdb.HLen(ctx, common.NodeWithClientsKey(item.GetServerId())).Result()
			if e != nil {
				r.Errorf("failed to count the number of clients, server_id: %s, error: %+v", item.GetServerId(), e)
			} else {
				info.Metadata.Clients = number
			}

			number, e = r.rdb.HLen(ctx, common.NodeWithUsersKey(item.GetServerId())).Result()
			if e != nil {
				r.Errorf("failed to count the number of users, server_id: %s, error: %+v", item.GetServerId(), e)
			} else {
				info.Metadata.Users = number
			}

			writer.Write(info)
		},
		func(pipe <-chan *pb.NodeServerInfo, cancel func(error)) {
			for item := range pipe {
				val = append(val, item)
			}
		},
		mr.WithContext(ctx),
	)

	return val, err
}

func (r *Registry) getMatchServerInfo(ctx context.Context, prefix string) (val []*pb.ServerInfo, err error) {
	if !strings.HasSuffix(prefix, asterisk) {
		prefix = prefix + asterisk
	}

	result, err := r.runScript(ctx, common.GetMatchServerScript, []string{prefix})
	if err != nil {
		return nil, err
	}

	values, err := cast.ToStringSliceE(result)
	if err != nil {
		return nil, err
	}

	val = make([]*pb.ServerInfo, 0, len(values))
	for _, value := range values {
		serverInfo := &pb.ServerInfo{}
		if err = protobuf.UnmarshalJSONFromString(value, serverInfo); err != nil {
			r.Errorf("failed to unmarshal the value to *pb.ServerInfo, value: %s, error: %+v", value, err)
			continue
		}

		val = append(val, serverInfo)
	}

	return val, nil
}

func (r *Registry) GetRouterInfoByServerID(ctx context.Context, rid string) (val *pb.RouterServerInfo, err error) {
	var result string
	err = r.brk.DoWithAcceptable(
		func() error {
			result, err = r.rdb.Get(ctx, common.RouterInfoKey(rid)).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return nil, err
	} else if len(result) == 0 {
		return nil, errors.Errorf("router info not found, server_id: %s", rid)
	}

	info := &pb.ServerInfo{}
	if err = protobuf.UnmarshalJSONFromString(result, info); err != nil {
		return nil, err
	}

	return &pb.RouterServerInfo{
		Info:     info,
		Metadata: &pb.RouterServerInfo_Metadata{},
	}, err
}

func (r *Registry) GetNodeInfoByServerID(ctx context.Context, nid string) (val *pb.NodeServerInfo, err error) {
	var result string
	err = r.brk.DoWithAcceptable(
		func() error {
			result, err = r.rdb.Get(ctx, common.NodeInfoKey(nid)).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return nil, err
	} else if len(result) == 0 {
		return nil, errors.Errorf("node info not found, server_id: %s", nid)
	}

	info := &pb.ServerInfo{}
	if err = protobuf.UnmarshalJSONFromString(result, info); err != nil {
		return nil, err
	}

	val = &pb.NodeServerInfo{
		Info:     info,
		Metadata: &pb.NodeServerInfo_Metadata{},
	}
	val.Metadata.Clients, err = r.rdb.HLen(ctx, common.NodeWithClientsKey(nid)).Result()
	if err != nil {
		r.Errorf("failed to count the number of clients, server_id: %s, error: %+v", nid, err)
	}
	val.Metadata.Users, err = r.rdb.HLen(ctx, common.NodeWithUsersKey(nid)).Result()
	if err != nil {
		r.Errorf("failed to count the number of users, server_id: %s, error: %+v", nid, err)
	}

	return val, err
}

func (r *Registry) GetClientInfoByClientID(ctx context.Context, cid string) (val *pb.ClientInfo, err error) {
	var result string
	err = r.brk.DoWithAcceptable(
		func() error {
			result, err = r.rdb.HGet(ctx, common.ConstClientsInfoKey, cid).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return nil, err
	} else if len(result) == 0 {
		return nil, errors.Errorf("client info not found, client id: %s", cid)
	}

	val = &pb.ClientInfo{}
	err = protobuf.UnmarshalJSONFromString(result, val)
	return val, err
}

func (r *Registry) GetUnmanagedClientInfoByClientID(ctx context.Context, cid string) (val *pb.ClientInfo, err error) {
	var result string
	err = r.brk.DoWithAcceptable(
		func() error {
			result, err = r.rdb.HGet(ctx, common.ConstUnmanagedClientsInfoKey, cid).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return nil, err
	} else if len(result) == 0 {
		return nil, errors.Errorf("unmanaged client info not found, client id: %s", cid)
	}

	val = &pb.ClientInfo{}
	err = protobuf.UnmarshalJSONFromString(result, val)
	return val, err
}

func (r *Registry) GetUserInfoByUserID(ctx context.Context, uid string) (val *pb.UserInfo, err error) {
	var result string
	err = r.brk.DoWithAcceptable(
		func() error {
			result, err = r.rdb.HGet(ctx, common.ConstUsersInfoKey, uid).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return nil, err
	} else if len(result) == 0 {
		return nil, errors.Errorf("user info not found, user id: %s", uid)
	}

	val = &pb.UserInfo{}
	err = protobuf.UnmarshalJSONFromString(result, val)
	return val, err
}

func (r *Registry) GetNodeInfoByClientID(ctx context.Context, cid string) (val *pb.NodeServerInfo, err error) {
	clientInfo, err := r.GetClientInfoByClientID(ctx, cid)
	if err != nil {
		return nil, err
	}

	return r.GetNodeInfoByServerID(ctx, clientInfo.GetNid())
}

func (r *Registry) GetNodeInfoByUserID(ctx context.Context, uid string) (val *pb.NodeServerInfo, err error) {
	userInfo, err := r.GetUserInfoByUserID(ctx, uid)
	if err != nil {
		return nil, err
	}

	return r.GetNodeInfoByServerID(ctx, userInfo.GetNid())
}

func (r *Registry) GetNodeAttrInfoByNodeID(ctx context.Context, nid string) (val *pb.AttrInfo, err error) {
	node, err := r.GetNodeInfoByServerID(ctx, nid)
	if err != nil {
		return nil, err
	}

	cis, err := r.GetAllClientInfoByNodeID(ctx, nid)
	if err != nil {
		return nil, err
	}
	cs := make(map[string]*pb.ClientInfo, len(cis))
	for _, ci := range cis {
		cs[ci.GetCid()] = ci
	}

	uis, err := r.GetAllUserInfoByNodeID(ctx, nid)
	if err != nil {
		return nil, err
	}
	us := make(map[string]*pb.UserInfo, len(uis))
	for _, ui := range uis {
		us[ui.GetUid()] = ui
	}

	return &pb.AttrInfo{
		Node:    node.GetInfo(),
		Clients: cs,
		Users:   us,
	}, nil
}

func (r *Registry) GetAllClientInfoByNodeID(ctx context.Context, nid string) (val []*pb.ClientInfo, err error) {
	result, err := r.runScript(ctx, common.GetAllClientsOrUsersScript, []string{common.NodeWithClientsKey(nid)})
	if err != nil {
		return nil, err
	}

	values, err := cast.ToStringSliceE(result)
	if err != nil {
		return nil, err
	}

	val = make([]*pb.ClientInfo, 0, len(values))
	for _, value := range values {
		ci := &pb.ClientInfo{}
		if err = protobuf.UnmarshalJSONFromString(value, ci); err != nil {
			r.Errorf("failed to unmarshal the value to *pb.ClientInfo, value: %s, error: %+v", value, err)
			continue
		}

		val = append(val, ci)
	}

	return val, nil
}

func (r *Registry) GetAllUserInfoByNodeID(ctx context.Context, nid string) (val []*pb.UserInfo, err error) {
	result, err := r.runScript(ctx, common.GetAllClientsOrUsersScript, []string{common.NodeWithUsersKey(nid)})
	if err != nil {
		return nil, err
	}

	values, err := cast.ToStringSliceE(result)
	if err != nil {
		return nil, err
	}

	val = make([]*pb.UserInfo, 0, len(values))
	for _, value := range values {
		ui := &pb.UserInfo{}
		if err = protobuf.UnmarshalJSONFromString(value, ui); err != nil {
			r.Errorf("failed to unmarshal the value to *pb.UserInfo, value: %s, error: %+v", value, err)
			continue
		}

		val = append(val, ui)
	}

	return val, nil
}

func (r *Registry) GetClientNumberByNodeID(ctx context.Context, nid string) (number int64, err error) {
	err = r.brk.DoWithAcceptable(
		func() error {
			number, err = r.rdb.HLen(ctx, common.NodeWithClientsKey(nid)).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return 0, err
	}

	return number, nil
}

func (r *Registry) GetUserNumberByNodeID(ctx context.Context, nid string) (number int64, err error) {
	err = r.brk.DoWithAcceptable(
		func() error {
			number, err = r.rdb.HLen(ctx, common.NodeWithUsersKey(nid)).Result()
			if errors.Is(err, goredis.Nil) {
				err = nil
			}

			return err
		}, acceptable,
	)
	if err != nil {
		return 0, err
	}

	return number, nil
}

func (r *Registry) GetAllNodeAttrInfo(ctx context.Context) (val []*pb.AttrInfo, err error) {
	infos, err := r.GetAllNodeInfo(ctx)
	if err != nil {
		return nil, err
	}

	val = make([]*pb.AttrInfo, 0, len(infos))
	if err = mr.MapReduceVoid[*pb.ServerInfo, any](
		func(source chan<- *pb.ServerInfo) {
			for _, info := range infos {
				if info == nil {
					continue
				}

				source <- info.GetInfo()
			}
		},
		func(item *pb.ServerInfo, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			cis, err := r.GetAllClientInfoByNodeID(ctx, item.GetServerId())
			if err != nil {
				return
			}
			cs := make(map[string]*pb.ClientInfo, len(cis))
			for _, ci := range cis {
				cs[ci.GetCid()] = ci
			}

			uis, err := r.GetAllUserInfoByNodeID(ctx, item.GetServerId())
			if err != nil {
				return
			}
			us := make(map[string]*pb.UserInfo, len(uis))
			for _, ui := range uis {
				us[ui.GetUid()] = ui
			}

			val = append(
				val, &pb.AttrInfo{
					Node:    item,
					Clients: cs,
					Users:   us,
				},
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
		mr.WithContext(ctx),
	); err != nil {
		return nil, err
	}

	return val, nil
}

func (r *Registry) KeepaliveAllInfoByNodeID(ctx context.Context, nid string) (err error) {
	_, err = r.runScript(
		ctx, common.KeepaliveAllInfoScript, []string{
			common.ConstClientsInfoKey,
			common.NodeWithClientsKey(nid),
			common.ConstUsersInfoKey,
			common.NodeWithUsersKey(nid),
		},
		defaultExpireTime*2,
		defaultExpireTime*6,
	)

	return err
}

func (r *Registry) GetChannel(opts ...goredis.ChannelOption) <-chan *goredis.Message {
	if !r.toSub || r.ps == nil {
		return nil
	}

	return r.ps.Channel(opts...)
}

func (r *Registry) Close() error {
	r.closeOnce.Do(
		func() {
			r.Info("closing the server registry")
			delRegistry(r)

			var e1, e2 error

			if r.ps != nil {
				e1 = r.ps.Close()
			}
			if r.rdb != nil && r.toSub {
				e2 = r.rdb.Close()
			}

			if e1 != nil && e2 != nil {
				r.closeErr = es.Join(e1, e2)
			} else if e1 != nil {
				r.closeErr = e1
			} else if e2 != nil {
				r.closeErr = e2
			}
		},
	)

	return r.closeErr
}

func genKeyByRedisConf(c zeroredis.RedisConf) string {
	scheme := constants.REDIS
	if c.Type == zeroredis.ClusterType {
		scheme = constants.REDIS_CLUSTER
	}

	var authority string
	if len(c.User) > 0 {
		authority = c.User
	}
	if len(c.Pass) > 0 {
		authority += ":" + c.Pass
	}

	if len(authority) > 0 {
		return fmt.Sprintf("%s://%s@%s/%d", scheme, authority, c.Host, c.DB)
	}
	return fmt.Sprintf("%s://%s/%d", scheme, c.Host, c.DB)
}

func acceptable(err error) bool {
	return err == nil || errors.Is(err, goredis.Nil) || errors.Is(err, context.Canceled)
}
