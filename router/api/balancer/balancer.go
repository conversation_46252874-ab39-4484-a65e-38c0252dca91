package balancer

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	grpcbalancer "google.golang.org/grpc/balancer"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/resolver"
	"google.golang.org/grpc/serviceconfig"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

const (
	// Name is the name of the api proxy balancer.
	Name   = "api_proxy_balancer"
	NameV1 = Name + "_deprecated"
)

var (
	_ grpcbalancer.Builder      = bb{}
	_ grpcbalancer.ConfigParser = bb{}
	_ grpcbalancer.Balancer     = (*balancer)(nil)

	errZeroAddresses = errors.New("resolver produced zero addresses")
)

func init() {
	grpcbalancer.Register(bb{})
}

type bb struct{}

func (bb) Build(cc grpcbalancer.ClientConn, opts grpcbalancer.BuildOptions) grpcbalancer.Balancer {
	return &balancer{
		Logger: logx.WithContext(context.Background()).WithFields(
			logx.Field(
				common.ConstServerIDLogFieldKey, common.ID(),
			),
		),

		cc: cc,

		csEvaluator: &grpcbalancer.ConnectivityStateEvaluator{},
		state:       connectivity.Connecting,

		subConns: resolver.NewAddressMap(),
		scMap:    make(map[grpcbalancer.SubConn]*subConn),
		// Initialize picker to a picker that always returns ErrNoSubConnAvailable,
		// because when state of a SubConn changes, it may call UpdateState with this picker.
		// picker: NewErrPicker(grpcbalancer.ErrNoSubConnAvailable),
	}
}

func (bb) Name() string {
	return Name
}

func (bb) ParseConfig(c json.RawMessage) (serviceconfig.LoadBalancingConfig, error) {
	var config LBConfig
	if err := json.Unmarshal(c, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

type balancer struct {
	logx.Logger

	cc grpcbalancer.ClientConn

	csEvaluator *grpcbalancer.ConnectivityStateEvaluator
	state       connectivity.State

	subConns *resolver.AddressMap
	scMap    map[grpcbalancer.SubConn]*subConn
	// picker   grpcbalancer.Picker
	config *LBConfig

	resolverErr error // the last error reported by the resolver; cleared on successful resolution
	connErr     error // the last connection error; cleared upon leaving TransientFailure

	// stopPicker func()
}

func (b *balancer) UpdateClientConnState(s grpcbalancer.ClientConnState) error {
	b.Infof("update client connection state: %s", jsonx.MarshalIgnoreError(s))

	// Successful resolution; clear resolver error and ensure we return nil.
	b.resolverErr = nil

	config, ok := s.BalancerConfig.(*LBConfig)
	if !ok {
		return errors.Errorf(
			"received illegal balancer config, expected: %T, but got: %T, config data: %v",
			(*LBConfig)(nil), s.BalancerConfig, s.BalancerConfig,
		)
	}

	b.config = config
	b.updateAddresses(s.ResolverState.Addresses)

	// If resolver state contains no addresses, return an error so ClientConn
	// will trigger re-resolve. Also records this as a resolver error, so when
	// the overall state turns transient failure, the error message will have
	// the zero address information.
	if len(s.ResolverState.Addresses) == 0 {
		b.ResolverError(errZeroAddresses)
		return grpcbalancer.ErrBadResolverState
	}

	b.regeneratePicker()

	return nil
}

func (b *balancer) updateAddresses(addresses []resolver.Address) {
	b.Infof("update addresses: %s", jsonx.MarshalIgnoreError(addresses))

	// addressSet is the set converted from addresses, it's used for quick lookup of an address.
	addressSet := resolver.NewAddressMap()

	// loop through new address list and create sub conns for any new addresses.
	for _, address := range addresses {
		if _, ok := addressSet.Get(address); ok {
			// redundant address; skip.
			continue
		}
		addressSet.Set(address, nil)

		if _, ok := b.subConns.Get(address); !ok {
			b.Infof("create new sub conn, address: %v", address)

			// address is a new address (not existing in b.subConns).
			var (
				conn grpcbalancer.SubConn
				err  error
			)
			conn, err = b.cc.NewSubConn(
				[]resolver.Address{address},
				grpcbalancer.NewSubConnOptions{
					HealthCheckEnabled: true,
					StateListener: func(scs grpcbalancer.SubConnState) {
						b.updateSubConnState(conn, scs)
					},
				},
			)
			if err != nil {
				b.Warnf("failed to create new SubConn, address: %v, error: %v", addresses, err)
				continue
			}

			sc := &subConn{
				SubConn: conn,

				address:           address,
				connectivityState: connectivity.Idle,
			}
			b.subConns.Set(address, sc)
			b.scMap[conn] = sc
			b.csEvaluator.RecordTransition(connectivity.Shutdown, connectivity.Idle)
			conn.Connect()
		} else {
			b.Infof("sub conn already exists, address: %v", address)
		}
	}

	// loop through existing sub conns and remove ones that are not in addresses.
	for _, address := range b.subConns.Keys() {
		if _, ok := addressSet.Get(address); ok {
			// existing address also in new address list; skip.
			b.Infof("update sub conn, address: %v", address)
			continue
		} else {
			b.Infof("delete sub conn, address: %v", address)
		}

		// address was removed by resolver.
		sci, _ := b.subConns.Get(address)
		sc := sci.(*subConn)
		sc.SubConn.Shutdown()
		b.subConns.Delete(address)
	}
}

func (b *balancer) ResolverError(err error) {
	b.resolverErr = err

	if b.subConns.Len() == 0 {
		b.state = connectivity.TransientFailure
	}

	if b.state != connectivity.TransientFailure {
		// the picker will not change since no error is being returned.
		return
	}

	b.regeneratePicker()
}

// UpdateSubConnState is a nop because a StateListener is always set in NewSubConn.
func (b *balancer) UpdateSubConnState(sc grpcbalancer.SubConn, state grpcbalancer.SubConnState) {
	b.Errorf("update sub conn state called unexpectedly: %v, %+v", sc, state)
}

func (b *balancer) updateSubConnState(sc grpcbalancer.SubConn, state grpcbalancer.SubConnState) {
	cs := state.ConnectivityState

	conn, ok := b.scMap[sc]
	if !ok {
		b.Errorf("got state changes for an unknown SubConn: %p, %v", sc, state)
		return
	}
	b.Infof("update sub conn state: %s, %v -> %v", conn.address.Addr, conn.connectivityState, cs)

	if cs == connectivity.TransientFailure {
		// save error to be reported via picker.
		b.connErr = state.ConnectionError
	}
	if cs == connectivity.Shutdown {
		// the sub conn was removed from b.subConns when the address was removed in updateAddresses.
		delete(b.scMap, sc)
	}

	oldS := conn.updateConnectivityState(cs)
	b.state = b.csEvaluator.RecordTransition(oldS, cs)

	// Regenerate picker when one of the following happens:
	//  - this sc entered or left ready
	//  - the aggregated state of balancer is TransientFailure (may need to update error message)
	if (cs == connectivity.Ready) != (oldS == connectivity.Ready) ||
		b.state == connectivity.TransientFailure {
		b.regeneratePicker()
	}
}

// Close is a nop because base balancer doesn't have internal state to clean up,
// and it doesn't need to call Shutdown for the SubConns.
func (b *balancer) Close() {
	//if b.stopPicker != nil {
	//	b.stopPicker()
	//	b.stopPicker = nil
	//}

	for _, sc := range b.scMap {
		// ensure any lingering OOB watchers are stopped.
		sc.updateConnectivityState(connectivity.Shutdown)
	}
}

// ExitIdle is ignored; we always connect to all SubConns at all times.
func (b *balancer) ExitIdle() {}

func (b *balancer) readySubConns() []*subConn {
	var (
		readySCs  []*subConn
		addresses []string
		states    []string
	)

	for _, v := range b.subConns.Values() {
		sc, ok := v.(*subConn)
		if !ok {
			continue
		}

		states = append(states, fmt.Sprintf("%s:%s", sc.address.Addr, sc.connectivityState.String()))
		if sc.connectivityState == connectivity.Ready {
			readySCs = append(readySCs, sc)
			addresses = append(addresses, sc.address.Addr)
		}
	}

	b.Infof(
		"ready sub conns: [%d, %s], all states: %s",
		len(readySCs), jsonx.MarshalIgnoreError(addresses), jsonx.MarshalIgnoreError(states),
	)
	return readySCs
}

// mergeErrors builds an error from the last connection error and the last
// resolver error.  Must only be called if b.state is TransientFailure.
func (b *balancer) mergeErrors() error {
	// connErr must always be non-nil unless there are no SubConns, in which
	// case resolverErr must be non-nil.
	if b.connErr == nil {
		return errors.Errorf("last resolver error: %v", b.resolverErr)
	}
	if b.resolverErr == nil {
		return errors.Errorf("last connection error: %v", b.connErr)
	}
	return errors.Errorf("last connection error: %v; last resolver error: %v", b.connErr, b.resolverErr)
}

// regeneratePicker generates a picker.
func (b *balancer) regeneratePicker() {
	//if b.stopPicker != nil {
	//	b.stopPicker()
	//	b.stopPicker = nil
	//}

	switch b.state {
	case connectivity.TransientFailure:
		b.cc.UpdateState(
			grpcbalancer.State{
				ConnectivityState: connectivity.TransientFailure,
				Picker:            NewErrPicker(b.mergeErrors()),
			},
		)
		return
	case connectivity.Connecting, connectivity.Idle:
		// Idle could happen very briefly if all subconns are Idle, and we've
		// asked them to connect, but they haven't reported Connecting yet.
		// Report the same as Connecting since this is temporary.
		b.cc.UpdateState(
			grpcbalancer.State{
				ConnectivityState: connectivity.Connecting,
				Picker:            NewErrPicker(grpcbalancer.ErrNoSubConnAvailable),
			},
		)
		return
	case connectivity.Ready:
		b.connErr = nil
	default:
	}

	p := newPicker(b.config.RedisConf, b.readySubConns())
	// b.stopPicker = p.Stop
	b.cc.UpdateState(
		grpcbalancer.State{
			ConnectivityState: b.state,
			Picker:            p,
		},
	)
}
