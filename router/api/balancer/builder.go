package balancer

import (
	"context"
	mathrand "math/rand"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	grpcbalancer "google.golang.org/grpc/balancer"
	grpcbalancerbase "google.golang.org/grpc/balancer/base"
	"google.golang.org/grpc/connectivity"
)

var _ grpcbalancerbase.PickerBuilder = (*pickerBuilder)(nil)

func init() {
	grpcbalancer.Register(newBuilder())
}

func newBuilder() grpcbalancer.Builder {
	return grpcbalancerbase.NewBalancerBuilder(NameV1, new(pickerBuilder), grpcbalancerbase.Config{HealthCheck: true})
}

// Deprecated: use balancer instead.
type pickerBuilder struct{}

func (*pickerBuilder) Build(info grpcbalancerbase.PickerBuildInfo) grpcbalancer.Picker {
	if len(info.ReadySCs) == 0 {
		return NewErrPicker(grpcbalancer.ErrNoSubConnAvailable)
	}

	subConns := make([]*subConn, 0, len(info.ReadySCs))
	addresses := make([]string, 0, len(info.ReadySCs))
	for conn, connInfo := range info.ReadySCs {
		sc := &subConn{
			SubConn:           conn,
			address:           connInfo.Address,
			connectivityState: connectivity.Ready,
		}
		subConns = append(subConns, sc)
		addresses = append(addresses, connInfo.Address.Addr)
	}

	return &picker{
		r:      mathrand.New(mathrand.NewSource(time.Now().UnixNano())),
		logger: logx.WithContext(context.Background()),

		subConns:  subConns,
		addresses: addresses,
	}
}
