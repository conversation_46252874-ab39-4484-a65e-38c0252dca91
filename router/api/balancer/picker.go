package balancer

import (
	"context"
	cryptorand "crypto/rand"
	"fmt"
	"math/big"
	mathrand "math/rand"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	zeroredis "github.com/zeromicro/go-zero/core/stores/redis"
	grpcbalancer "google.golang.org/grpc/balancer"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	nodepb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/registry"
)

var (
	_ grpcbalancer.Picker = (*picker)(nil)

	emptyPickResult grpcbalancer.PickResult

	errRedisConfNotFound = errors.New("redis conf not found in the context")
	errRegistryIsNull    = errors.New("the registry cannot be null")
	errClientIDNotFound  = errors.New("client id not found in the context")

	reg *registry.Registry
)

type picker struct {
	// mutex  sync.Mutex
	r      *mathrand.Rand
	reg    *registry.Registry
	logger logx.Logger

	subConns  []*subConn
	addresses []string
}

func NewPicker(config zeroredis.RedisConf, subConns []*subConn) grpcbalancer.Picker {
	return newPicker(config, subConns)
}

func newPicker(config zeroredis.RedisConf, subConns []*subConn) *picker {
	addresses := make([]string, 0, len(subConns))
	for _, sc := range subConns {
		addresses = append(addresses, sc.address.Addr)
	}

	return &picker{
		r:      mathrand.New(mathrand.NewSource(time.Now().UnixNano())),
		reg:    registry.GetRegistry(config),
		logger: logx.WithContext(context.Background()),

		subConns:  subConns,
		addresses: addresses,
	}
}

func (p *picker) Pick(info grpcbalancer.PickInfo) (result grpcbalancer.PickResult, err error) {
	logger := p.logger.WithContext(info.Ctx)

	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("get an panic while picking, method: %s, error: %v", info.FullMethodName, r)
			if err == nil {
				err = fmt.Errorf("%v", r)
			}
		}
	}()

	startedAt := time.Now()
	logger.Infof("start to pick, method: %s, started: %s", info.FullMethodName, startedAt.Format(time.DateTime))
	defer func() {
		d := time.Since(startedAt)
		if d >= time.Second {
			logger.Infof("finish to pick, method: %s, elapsed: %s", info.FullMethodName, d)
		} else {
			logger.Debugf("finish to pick, method: %s, elapsed: %s", info.FullMethodName, d)
		}
	}()

	if len(p.subConns) == 0 {
		logger.Info("zero sub conns in picker")
		return emptyPickResult, grpcbalancer.ErrNoSubConnAvailable
	}

	if p.reg == nil {
		if err = p.initRegistry(info.Ctx); err != nil {
			return emptyPickResult, err
		}
	}

	var md metadata.MD
	switch info.FullMethodName {
	case nodepb.NodeService_CreateClient_FullMethodName:
		// 找不到用户则按策略进行选择
		username, ok := info.Ctx.Value(common.UsernameContextKey).(string)
		logger.Infof("pick by username, username: %s, method: %s", username, info.FullMethodName)
		if ok && len(username) != 0 {
			if result, err = p.pickByUsername(info.Ctx, username, md); err == nil {
				return result, nil
			} else if s, _ := status.FromError(err); s.Code() != codes.NotFound {
				return emptyPickResult, err
			}
		}
	case nodepb.NodeService_DeleteClient_FullMethodName,
		nodepb.NodeService_ViewClient_FullMethodName,
		nodepb.NodeService_ApiCall_FullMethodName:
		// 找不到客户端ID则返回错误
		clientID, ok := info.Ctx.Value(common.ClientIDContextKey).(string)
		logger.Infof("pick by client id, cid: %s, method: %s", clientID, info.FullMethodName)
		if ok && len(clientID) != 0 {
			if result, err = p.pickByClientID(info.Ctx, clientID, md); err == nil {
				return result, nil
			} else if clientInfo, e := p.getUnmanagedClientInfo(info.Ctx, clientID); e != nil {
				logger.Errorf("failed to get the unmanaged client info, cid: %s, error: %v", clientID, e)
				return result, err
			} else {
				logger.Infof(
					"pick by unmanaged client id, cid: %s, method: %s, info: %s",
					clientID, info.FullMethodName, protobuf.MarshalJSONIgnoreError(clientInfo),
				)
				md = metadata.Pairs(
					common.ConstClientInfoMetadataKey, protobuf.MarshalJSONToStringIgnoreError(clientInfo),
				)
			}
		} else {
			return emptyPickResult, errClientIDNotFound
		}
	}

	if result, err = p.pickByClientNumber(info.Ctx, md); err == nil {
		return result, nil
	}

	return p.pickByRandom(info.Ctx, md)
}

func (p *picker) pickByUsername(ctx context.Context, username string, md metadata.MD) (
	grpcbalancer.PickResult, error,
) {
	info, err := p.reg.GetNodeInfoByUserID(ctx, username)
	if err != nil {
		return emptyPickResult, status.Errorf(
			codes.NotFound, "node info not found, username: %s, error: %v", username, err,
		)
	}

	var (
		logger = p.logger.WithContext(ctx)

		serverID = info.GetInfo().GetServerId()
		address  = info.GetInfo().Address()
	)

	for _, v := range p.subConns {
		if v == nil {
			continue
		}

		if strings.EqualFold(v.address.Addr, address) {
			logger.Infof("pick by username, username: %s, node_id: %s, address: %s", username, serverID, address)
			return grpcbalancer.PickResult{
				SubConn:  v.SubConn,
				Metadata: md,
			}, nil
		}
	}

	return emptyPickResult, status.Errorf(
		codes.NotFound,
		"sub conn not found, username: %s, node_id: %s, address: %s, sub conns: [%d, %s]",
		username, serverID, address, len(p.subConns), jsonx.MarshalIgnoreError(p.addresses),
	)
}

func (p *picker) pickByClientID(ctx context.Context, clientID string, md metadata.MD) (grpcbalancer.PickResult, error) {
	info, err := p.reg.GetNodeInfoByClientID(ctx, clientID)
	if err != nil {
		return emptyPickResult, status.Errorf(
			codes.NotFound, "node info not found, client_id: %s, error: %v", clientID, err,
		)
	}

	var (
		logger = p.logger.WithContext(ctx)

		serverID = info.GetInfo().GetServerId()
		address  = info.GetInfo().Address()
	)

	for _, v := range p.subConns {
		if v == nil {
			continue
		}

		if strings.EqualFold(v.address.Addr, address) {
			logger.Infof("pick by client id, client_id: %s, node_id: %s, address: %s", clientID, serverID, address)
			return grpcbalancer.PickResult{
				SubConn:  v.SubConn,
				Metadata: md,
			}, nil
		}
	}

	return emptyPickResult, status.Errorf(
		codes.NotFound,
		"sub conn not found, client_id: %s, node_id: %s, address: %s, sub conns: [%d, %s]",
		clientID, serverID, address, len(p.subConns), jsonx.MarshalIgnoreError(p.addresses),
	)
}

func (p *picker) getUnmanagedClientInfo(ctx context.Context, clientID string) (*pb.ClientInfo, error) {
	return p.reg.GetUnmanagedClientInfoByClientID(ctx, clientID)
}

//func (p *picker) Stop() {
//	p.mutex.Lock()
//	defer p.mutex.Unlock()
//
//	p.r = nil
//	p.subConns = nil
//	p.addresses = nil
//	p.reg = nil // 由于是通过`registry.GetRegistry`获取注册器的，所以这里不需要关闭
//}

func (p *picker) initRegistry(ctx context.Context) (err error) {
	if p.reg != nil {
		return nil
	} else if reg != nil {
		p.reg = reg
		return nil
	}

	c, ok := ctx.Value(common.RedisConfContextKey).(zeroredis.RedisConf)
	if !ok {
		return errRedisConfNotFound
	} else {
		reg = registry.GetRegistry(c)
	}
	if reg == nil {
		return errRegistryIsNull
	}

	p.reg = reg
	return nil
}

func (p *picker) pickByClientNumber(ctx context.Context, md metadata.MD) (grpcbalancer.PickResult, error) {
	var (
		logger = p.logger.WithContext(ctx)

		sc *subConn
		n  int64
	)

	for i := range p.subConns {
		if p.subConns[i] == nil {
			continue
		}

		info, ok := p.subConns[i].address.Attributes.Value(common.NodeInfoAttributeKey).(*pb.NodeServerInfo)
		if !ok {
			continue
		}

		serverID := info.GetInfo().GetServerId()
		info, err := p.reg.GetNodeInfoByServerID(ctx, serverID)
		if err != nil {
			logger.Warnf("failed to get the node info, server_id: %s, error: %v", serverID, err)
			continue
		}

		count := info.GetMetadata().GetClients()
		if sc == nil || count < n {
			sc = p.subConns[i]
			n = count
		}
		if n == 0 {
			// 存在客户端数量为0的节点，则直接选择该节点
			break
		}
	}
	if sc == nil {
		return emptyPickResult, status.Errorf(
			codes.NotFound, "sub conn not found, sub conns: [%d, %s]",
			len(p.subConns), jsonx.MarshalIgnoreError(p.addresses),
		)
	}

	logger.Infof("pick by client number, number: %d, address: %s", n, sc.address.Addr)
	return grpcbalancer.PickResult{
		SubConn:  sc.SubConn,
		Metadata: md,
	}, nil
}

func (p *picker) pickByRandom(ctx context.Context, md metadata.MD) (grpcbalancer.PickResult, error) {
	logger := p.logger.WithContext(ctx)

	n := p.rand(ctx)
	if n < 0 {
		logger.Errorf("get an negative integer from random function, n: %d", n)
		return emptyPickResult, grpcbalancer.ErrNoSubConnAvailable
	}

	logger.Infof("pick by random, rand: %d, address: %s", n, p.subConns[n].address.Addr)
	return grpcbalancer.PickResult{
		SubConn:  p.subConns[n].SubConn,
		Metadata: md,
	}, nil
}

func (p *picker) rand(ctx context.Context) (n int) {
	logger := p.logger.WithContext(ctx)

	defer func() {
		r := recover()
		if r != nil {
			logger.Errorf(
				"get an panic while generating a random value, sub conns: [%d, %s], error: %v",
				len(p.subConns), jsonx.MarshalIgnoreError(p.addresses), r,
			)
			n = 0
		}
	}()

	if i, err := cryptorand.Int(cryptorand.Reader, big.NewInt(int64(len(p.subConns)))); err == nil {
		return int(i.Int64())
	}

	return p.r.Intn(len(p.subConns))
}

// NewErrPicker returns a Picker that always returns err on Pick().
func NewErrPicker(err error) grpcbalancer.Picker {
	return &errPicker{err: err}
}

type errPicker struct {
	err error // Pick() always returns this err.
}

func (p *errPicker) Pick(_ grpcbalancer.PickInfo) (grpcbalancer.PickResult, error) {
	return grpcbalancer.PickResult{}, p.err
}

func (p *errPicker) Stop() {}
