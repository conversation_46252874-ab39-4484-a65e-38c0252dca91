package balancer

import (
	"fmt"

	grpcbalancer "google.golang.org/grpc/balancer"
	"google.golang.org/grpc/connectivity"
	grpcresolver "google.golang.org/grpc/resolver"
)

type subConn struct {
	grpcbalancer.SubConn

	address           grpcresolver.Address
	connectivityState connectivity.State
}

func (c *subConn) updateConnectivityState(s connectivity.State) connectivity.State {
	if s == connectivity.Idle {
		// always reconnect when idle.
		c.SubConn.Connect()
	}

	oldS := c.connectivityState
	if oldS == connectivity.TransientFailure &&
		(s == connectivity.Connecting || s == connectivity.Idle) {
		// Once a subconn enters TRANSIENT_FAILURE, ignore subsequent IDLE or
		// CONNECTING transitions to prevent the aggregated state from being
		// always CONNECTING when many backends exist but are all down.
		return oldS
	}
	c.connectivityState = s

	return oldS
}

func (c *subConn) String() string {
	return fmt.Sprintf(`{"address": %s, "state": %s}`, c.address.Addr, c.connectivityState.String())
}
