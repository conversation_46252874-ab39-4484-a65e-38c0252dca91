syntax = "v1"

import "client_types.api"

@server (
	prefix: router/v1
	group: client

	tags: "客户端相关"
)
service router {
	@doc "create client"
	@handler createClient
	post /client/create (CreateClientReq) returns (CreateClientResp)

	@doc "delete client"
	@handler deleteClient
	delete /client/delete (DeleteClientReq) returns (DeleteClientResp)

	@doc "view client"
	@handler viewClient
	get /client/view (ViewClientReq) returns (ViewClientResp)
}
