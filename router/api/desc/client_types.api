syntax = "v1"

type UserInfo {
	CID string `json:"cid"` // 客户端ID
	NID string `json:"nid"` // 所在的节点服务ID
	UID string `json:"uid"` // 用户唯一标识
}

type ClientBasicInfo {
	Cid             string `json:"cid"`               // 客户端ID
	Nid             string `json:"nid"`               // 所在的节点服务ID
	Product         string `json:"product"`           // 产品名称
	Type            string `json:"type"`              // 客户端类型
	Status          string `json:"status"`            // 【待定】客户端状态
	CreatedAt       int64  `json:"created_at"`        // 客户端的创建时间
	LastCreatedAt   int64  `json:"last_created_at"`   // 最近一次客户端的创建时间
	LastRequestedAt int64  `json:"last_requested_at"` // 最近一次请求时间
	NumOfRequest    uint64 `json:"num_of_request"`    // 【待定】请求次数
}

type ClientInfo {
	ClientBasicInfo

	CustomFields map[string]interface{} `json:"custom_fields"` // 客户端的响应自定义字段
	LoginResp    map[string]interface{} `json:"login_resp"`    // 登录响应信息
	UserInfo     *UserInfo              `json:"user_info"`     // 用户基础信息
}

// 创建客户端
type (
	CreateClientReq {
		Type         string                 `json:"type" validate:"required" zh:"客户端类型"`
		Url          string                 `json:"url,omitempty,optional" zh:"服务端连接地址"`
		CustomFields map[string]interface{} `json:"custom_fields,omitempty,optional" zh:"客户端的请求自定义字段"`
	}
	CreateClientResp {
		*ClientInfo
	}
)

// 删除客户端
type (
	DeleteClientReq {
		Cid string `form:"cid" validate:"required" zh:"客户端ID"`
	}
	DeleteClientResp {
		*ClientInfo
	}
)

// 查看客户端
type (
	ViewClientReq {
		Cid string `form:"cid" validate:"required" zh:"客户端ID"`
	}
	ViewClientResp {
		*ClientInfo
	}
)