syntax = "v1"

import "common_types.api"

@server (
	prefix: router/v1
	group: common

	tags: "其它功能"
)
service router {
	@doc "api call"
	@handler apiCall
	post /common/api/call (ApiCallReq) returns (ApiCallResp)

	@doc "json to proto string"
	@handler jsonToProto
	post /common/proto/jsontoproto (JsonToProtoReq) returns (JsonToProtoResp)

	@doc "proto string to json"
	@handler protoToJson
	post /common/proto/prototojson (ProtoToJsonReq) returns (ProtoToJsonResp)
}

@server (
	prefix: router/v1
	group: common
	timeout: 6m

	tags: "其它功能"
)
service router {
	@doc "update proto"
	@handler updateProto
	post /common/proto/update (UpdateProtoReq) returns (UpdateProtoResp)
}