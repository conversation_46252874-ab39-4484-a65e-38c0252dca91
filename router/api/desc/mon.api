syntax = "v1"

import "mon_types.api"

@server (
	prefix: router/v1
	group: mon

	tags: "监控相关（未实现）"
)
service router {
	@doc "get router list"
	@handler getRouterList
	get /mon/router/list (GetRouterListReq) returns (GetRouterListResp)

	@doc "get node list"
	@handler getNodeList
	get /mon/node/list (GetNodeListReq) returns (GetNodeListResp)

	@doc "get node stats"
	@handler getNodeStat
	get /mon/node/stats (GetNodeStatsReq) returns (GetNodeStatsResp)
}