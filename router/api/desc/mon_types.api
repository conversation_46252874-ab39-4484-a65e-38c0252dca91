syntax = "v1"

type Router {
	Rid       string `json:"rid"`        // 路由服务ID
	Host      string `json:"host"`       // 服务地址（主机名称或者IP）
	Port      int64  `json:"port"`       // 服务端口
	StartedAt int64  `json:"started_at"` // 启动时间
}

type Node {
	Nid       string `json:"nid"`        // 节点服务ID
	Host      string `json:"host"`       // 服务地址（主机名称或者IP）
	Port      int64  `json:"port"`       // 服务端口
	StartedAt int64  `json:"started_at"` // 启动时间
}

// 获取路由服务列表
type (
	GetRouterListReq {
	}
	GetRouterListResp {
		Total int64    `json:"total"`
		Items []Router `json:"items"`
	}
)

// 获取节点服务列表
type (
	GetNodeListReq {
	}
	GetNodeListResp {
		Total int64  `json:"total"`
		Items []Node `json:"items"`
	}
)

// 获取节点服务统计信息
type (
	GetNodeStatsReq {
		Nid string `json:"nid" validate:"required"` // 节点服务ID
	}
	GetNodeStatsResp {
		Stat interface{} `json:"info"`
	}
)