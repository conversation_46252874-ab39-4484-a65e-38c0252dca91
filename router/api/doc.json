{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "swagger": "2.0", "info": {"title": "api-proxy router service", "version": "v1.6.5"}, "host": "127.0.0.1", "basePath": "/", "paths": {"/router/v1/client/create": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["客户端相关"], "summary": "create client", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["type"], "properties": {"custom_fields": {"type": "object", "additionalProperties": {}}, "type": {"type": "string"}, "url": {"type": "string"}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object"}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/client/delete": {"delete": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "tags": ["客户端相关"], "summary": "delete client", "parameters": [{"type": "string", "name": "cid", "in": "formData", "required": true}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object"}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/client/view": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["客户端相关"], "summary": "view client", "parameters": [{"type": "string", "name": "cid", "in": "query", "required": true}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object"}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/common/api/call": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["其它功能"], "summary": "api call", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["cid"], "properties": {"authority": {"description": "gRPC服务器名称", "type": "string"}, "body": {"description": "请求体"}, "cid": {"description": "客户端ID", "type": "string"}, "custom_fields": {"description": "客户端的请求自定义字段", "type": "object", "additionalProperties": {}}, "headers": {"description": "请求头", "type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "method": {"description": "请求方法", "type": "string"}, "url": {"description": "请求路径", "type": "string"}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"call_resp": {"type": "object", "required": ["header", "body", "status"], "properties": {"body": {"description": "响应体"}, "header": {"description": "响应头", "type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "status": {"description": "响应状态码", "type": "integer"}}}, "client_info": {"type": "object", "required": ["cid", "nid", "product", "type", "status", "created_at", "last_created_at", "last_requested_at", "num_of_request"], "properties": {"cid": {"description": "客户端ID", "type": "string"}, "created_at": {"description": "客户端的创建时间", "type": "integer"}, "last_created_at": {"description": "最近一次客户端的创建时间", "type": "integer"}, "last_requested_at": {"description": "最近一次请求时间", "type": "integer"}, "nid": {"description": "所在的节点服务ID", "type": "string"}, "num_of_request": {"description": "【待定】请求次数", "type": "integer"}, "product": {"description": "产品名称", "type": "string"}, "status": {"description": "【待定】客户端状态", "type": "string"}, "type": {"description": "客户端类型", "type": "string"}}}, "custom_fields": {"type": "object", "additionalProperties": {}}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/common/proto/jsontoproto": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["其它功能"], "summary": "json to proto string", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["product_name", "message", "data"], "properties": {"data": {"description": "待转换数据"}, "message": {"description": "目标消息名称", "type": "string"}, "product_name": {"description": "产品名称", "type": "string"}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"proto_string": {"description": "转换后的数据", "type": "string"}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/common/proto/prototojson": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["其它功能"], "summary": "proto string to json", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["product_name", "message", "data"], "properties": {"data": {"description": "待转换数据", "type": "string"}, "message": {"description": "目标消息名称", "type": "string"}, "product_name": {"description": "产品名称", "type": "string"}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"json_data": {"description": "转换后的数据"}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/common/proto/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "tags": ["其它功能"], "summary": "update proto", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["product_name", "branch"], "properties": {"branch": {"description": "分支名称", "type": "string", "default": "main", "example": "main"}, "product_name": {"description": "产品名称", "type": "string"}, "projects": {"description": "项目配置", "type": "array", "items": {"type": "object", "required": ["project_name", "git_url", "branch", "import_path"], "properties": {"branch": {"description": "分支名称", "type": "string", "default": "main", "example": "main"}, "dependencies": {"description": "依赖配置", "type": "array", "items": {"type": "object", "properties": {"git": {"type": "object", "required": ["git_url", "branch", "import_path"], "properties": {"branch": {"description": "分支名称", "type": "string", "default": "main", "example": "main"}, "git_url": {"description": "Git地址", "type": "string"}, "import_path": {"description": "导入路径", "type": "string", "default": ".", "example": "."}}}, "local_path": {"type": "string"}}}}, "exclude_files": {"description": "排除的文件", "type": "array", "items": {"type": "string"}}, "exclude_paths": {"description": "排除的路径", "type": "array", "items": {"type": "string"}}, "git_url": {"description": "Git地址", "type": "string"}, "import_path": {"description": "导入路径", "type": "string", "default": ".", "example": "."}, "project_name": {"description": "项目名称", "type": "string"}}}}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"branch": {"description": "分支", "type": "string"}, "product_name": {"description": "产品名称", "type": "string"}, "projects": {"description": "项目信息", "type": "array", "items": {"type": "object", "required": ["project_name", "branch", "commit"], "properties": {"branch": {"description": "分支名称", "type": "string"}, "commit": {"description": "提交信息", "type": "object", "required": ["hash", "author", "date", "message"], "properties": {"author": {"type": "string"}, "date": {"type": "string"}, "hash": {"type": "string"}, "message": {"type": "string"}}}, "project_name": {"description": "项目名称", "type": "string"}}}}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/mon/node/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["监控相关（未实现）"], "summary": "get node list", "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "required": ["nid", "host", "port", "started_at"], "properties": {"host": {"description": "服务地址（主机名称或者IP）", "type": "string"}, "nid": {"description": "节点服务ID", "type": "string"}, "port": {"description": "服务端口", "type": "integer"}, "started_at": {"description": "启动时间", "type": "integer"}}}}, "total": {"type": "integer"}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/mon/node/stats": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["监控相关（未实现）"], "summary": "get node stats", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["nid"], "properties": {"nid": {"description": "节点服务ID", "type": "string"}}}}], "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"info": {}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}, "/router/v1/mon/router/list": {"get": {"produces": ["application/json"], "schemes": ["https"], "tags": ["监控相关（未实现）"], "summary": "get router list", "responses": {"default": {"description": "", "schema": {"type": "object", "properties": {"code": {"description": "business code", "type": "integer", "example": 0}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "required": ["rid", "host", "port", "started_at"], "properties": {"host": {"description": "服务地址（主机名称或者IP）", "type": "string"}, "port": {"description": "服务端口", "type": "integer"}, "rid": {"description": "路由服务ID", "type": "string"}, "started_at": {"description": "启动时间", "type": "integer"}}}}, "total": {"type": "integer"}}}, "msg": {"description": "business message", "type": "string", "example": "ok"}}}}}}}}, "x-date": "2025-05-16 11:21:15", "x-description": "This is a goctl generated swagger file.", "x-github": "https://github.com/zeromicro/go-zero", "x-go-zero-doc": "https://go-zero.dev/", "x-goctl-version": "1.8.3"}