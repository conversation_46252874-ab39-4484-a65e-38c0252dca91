Name: api.router
Log:
  Encoding: plain
  Stat: false
Host: *************
Port: 8080
Verbose: true
Timeout: 0

Prometheus:
  Host: 0.0.0.0
  Port: 9101
  Path: /metrics

Redis:
  Key: api.router
  Host: 127.0.0.1:6379
  Type: node
  Pass:

NodeRPC:
  Target: redis://127.0.0.1:6379/0
  NonBlock: true
  Timeout: 0

# gRPC客户端keepalive配置
ClientOptions:
  KeepaliveTime: 20s  # 客户端keepalive间隔
  KeepaliveTimeout: 5s  # keepalive超时时间
  KeepalivePermitWithoutStream: true  # 允许在没有活跃流时发送keepalive

Validator:
  Locale: zh
