package config

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

type Config struct {
	rest.RestConf

	Redis redis.RedisConf

	NodeRPC       zrpc.RpcClientConf
	ClientOptions common.ClientOptions `json:",optional"`

	Validator types.ValidatorConfig
}

func (c Config) ListenOn() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

func (c Config) LogConfig() logx.LogConf {
	return c.RestConf.ServiceConf.Log
}
