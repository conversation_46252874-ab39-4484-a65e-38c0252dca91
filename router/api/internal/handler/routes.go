// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3

package handler

import (
	"net/http"
	"time"

	client "gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/handler/client"
	common "gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/handler/common"
	mon "gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/handler/mon"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// get router list
				Method:  http.MethodGet,
				Path:    "/mon/router/list",
				Handler: mon.GetRouterListHandler(serverCtx),
			},
			{
				// get node list
				Method:  http.MethodGet,
				Path:    "/mon/node/list",
				Handler: mon.GetNodeListHandler(serverCtx),
			},
			{
				// get node stats
				Method:  http.MethodGet,
				Path:    "/mon/node/stats",
				Handler: mon.GetNodeStatHandler(serverCtx),
			},
		},
		rest.WithPrefix("/router/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// api call
				Method:  http.MethodPost,
				Path:    "/common/api/call",
				Handler: common.ApiCallHandler(serverCtx),
			},
			{
				// json to proto string
				Method:  http.MethodPost,
				Path:    "/common/proto/jsontoproto",
				Handler: common.JsonToProtoHandler(serverCtx),
			},
			{
				// proto string to json
				Method:  http.MethodPost,
				Path:    "/common/proto/prototojson",
				Handler: common.ProtoToJsonHandler(serverCtx),
			},
		},
		rest.WithPrefix("/router/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// update proto
				Method:  http.MethodPost,
				Path:    "/common/proto/update",
				Handler: common.UpdateProtoHandler(serverCtx),
			},
		},
		rest.WithPrefix("/router/v1"),
		rest.WithTimeout(360000*time.Millisecond),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create client
				Method:  http.MethodPost,
				Path:    "/client/create",
				Handler: client.CreateClientHandler(serverCtx),
			},
			{
				// delete client
				Method:  http.MethodDelete,
				Path:    "/client/delete",
				Handler: client.DeleteClientHandler(serverCtx),
			},
			{
				// view client
				Method:  http.MethodGet,
				Path:    "/client/view",
				Handler: client.ViewClientHandler(serverCtx),
			},
		},
		rest.WithPrefix("/router/v1"),
	)
}
