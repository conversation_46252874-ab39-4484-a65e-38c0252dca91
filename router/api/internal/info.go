package internal

import (
	"context"
	"net/http"

	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

// WithClientBasicInfo returns a new context with types.ClientBasicInfo.
// Deprecated: use WithCustomHeaders instead.
func WithClientBasicInfo(ctx context.Context, info *types.ClientBasicInfo) context.Context {
	return context.WithValue(ctx, common.ClientInfoContextKey, info)
}

// ClientBasicInfoFromContext returns types.ClientBasicInfo from context.
// Deprecated: use CustomHeadersFromContext instead.
func ClientBasicInfoFromContext(ctx context.Context) *types.ClientBasicInfo {
	val := ctx.Value(common.ClientInfoContextKey)
	if val == nil {
		return nil
	}

	v, ok := val.(*types.ClientBasicInfo)
	if !ok {
		return nil
	}

	return v
}

// WithCustomHeaders returns a new context with http.Header.
func WithCustomHeaders(ctx context.Context, headers http.Header) context.Context {
	return context.WithValue(ctx, common.CustomHeadersContextKey, headers)
}

// CustomHeadersFromContext returns http.Header from context.
func CustomHeadersFromContext(ctx context.Context) http.Header {
	val := ctx.Value(common.CustomHeadersContextKey)
	if val == nil {
		return nil
	}

	v, ok := val.(http.Header)
	if !ok {
		return nil
	}

	return v
}

// UpdateCustomHeadersByHeaderOrTrailer merges the gRPC metadata into http.Header.
func UpdateCustomHeadersByHeaderOrTrailer(ctx context.Context, header, trailer metadata.MD) {
	headers := CustomHeadersFromContext(ctx)
	if headers == nil {
		return
	}

	for _, md := range []metadata.MD{header, trailer} {
		for k, vs := range md {
			for _, v := range vs {
				headers.Add(k, v)
			}
		}
	}
}
