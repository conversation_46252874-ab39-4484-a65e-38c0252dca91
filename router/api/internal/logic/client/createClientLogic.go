package client

import (
	"context"
	"net/url"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type CreateClientLogic struct {
	*BaseLogic
}

func NewCreateClientLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateClientLogic {
	return &CreateClientLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateClientLogic) CreateClient(req *types.CreateClientReq) (resp *types.CreateClientResp, err error) {
	in := &pb.CreateClientReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	if in.GetUrl() != "" {
		u, err := url.ParseRequestURI(in.GetUrl())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.ParseParamError, err.Error()),
				"failed to parse the url, url: %s, error: %+v",
				in.GetUrl(), err,
			)
		} else if u.User != nil {
			// set the `username` into the context
			l.ctx = context.WithValue(l.ctx, common.UsernameContextKey, u.User.Username())
		}
	}

	header := make(metadata.MD)
	trailer := make(metadata.MD)
	defer func() {
		// try to return custom headers
		internal.UpdateCustomHeadersByHeaderOrTrailer(l.ctx, header, trailer)
	}()

	out, err := l.svcCtx.NodeRPC.CreateClient(
		l.ctx, in, grpc.Header(&header), grpc.Trailer(&trailer), grpc.UseCompressor(gzip.Name),
	)
	if err != nil {
		return nil, err
	}

	resp = &types.CreateClientResp{}
	if err = utils.Copy(resp, out.GetItem(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}

func reqConvert(in *types.CreateClientReq) (out *pb.CreateClientReq, err error) {
	customFields, err := protobuf.NewStruct(in.CustomFields)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateClientReq{
		Type:         in.Type,
		Url:          in.Url,
		CustomFields: customFields,
	}

	return out, nil
}

func respConvert(in *pb.CreateClientResp) (out *types.CreateClientResp, err error) { //nolint:unparam
	v := in.GetItem()
	if v == nil {
		return nil, nil
	}

	out = &types.CreateClientResp{
		ClientInfo: &types.ClientInfo{
			ClientBasicInfo: types.ClientBasicInfo{
				Cid:             v.GetCid(),
				Nid:             v.GetNid(),
				Product:         v.GetProduct(),
				Type:            v.GetType(),
				Status:          v.GetStatus(),
				CreatedAt:       v.GetCreatedAt().AsTime().UnixMilli(),
				LastCreatedAt:   v.GetLastCreatedAt().AsTime().UnixMilli(),
				LastRequestedAt: v.GetLastRequestedAt().AsTime().UnixMilli(),
				NumOfRequest:    v.GetNumOfRequest(),
			},
			CustomFields: v.GetCustomInfo().AsMap(),
			LoginResp:    v.GetLoginResp().AsMap(),
			UserInfo:     userInfoConvert(v.GetUserInfo()),
		},
	}

	return out, nil
}

func userInfoConvert(in *commonpb.UserInfo) *types.UserInfo {
	if in == nil {
		return nil
	}

	return &types.UserInfo{
		CID: in.GetCid(),
		NID: in.GetNid(),
		UID: in.GetUid(),
	}
}
