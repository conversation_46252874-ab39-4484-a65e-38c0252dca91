package client

import (
	"fmt"
	"net/url"
	"testing"
)

func TestURLParse(t *testing.T) {
	pwdEscape := url.QueryEscape("k<5cV)4M")
	t.Logf("password escape: %s", pwdEscape)

	urlStr := fmt.Sprintf("tcp://91169112747:%<EMAIL>", pwdEscape)
	u, err := url.ParseRequestURI(urlStr)
	if err != nil {
		t.Fatal(err)
	}

	username := u.User.Username()
	password, _ := u.User.Password()
	t.Logf("user: %s, password: %s", username, password)
}
