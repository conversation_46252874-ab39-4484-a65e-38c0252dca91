package client

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type DeleteClientLogic struct {
	*BaseLogic
}

func NewDeleteClientLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteClientLogic {
	return &DeleteClientLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *DeleteClientLogic) DeleteClient(req *types.DeleteClientReq) (resp *types.DeleteClientResp, err error) {
	in := &pb.DeleteClientReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	header := make(metadata.MD)
	trailer := make(metadata.MD)

	defer func() {
		// try to return custom headers
		internal.UpdateCustomHeadersByHeaderOrTrailer(l.ctx, header, trailer)
	}()

	// set the `client_id` into the context
	l.ctx = context.WithValue(l.ctx, common.ClientIDContextKey, in.GetCid())
	out, err := l.svcCtx.NodeRPC.DeleteClient(
		l.ctx, in, grpc.Header(&header), grpc.Trailer(&trailer), grpc.UseCompressor(gzip.Name),
	)
	if err != nil {
		return nil, err
	}

	resp = &types.DeleteClientResp{}
	if err = utils.Copy(resp, out.GetItem(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
