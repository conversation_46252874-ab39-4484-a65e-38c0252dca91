package common

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type ApiCallLogic struct {
	*BaseLogic
}

func NewApiCallLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApiCallLogic {
	return &ApiCallLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ApiCallLogic) ApiCall(req *types.ApiCallReq) (resp *types.ApiCallResp, err error) {
	var in *pb.ApiCallReq
	in, err = reqConvert(req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	header := make(metadata.MD)
	trailer := make(metadata.MD)

	defer func() {
		// try to return custom headers
		internal.UpdateCustomHeadersByHeaderOrTrailer(l.ctx, header, trailer)
	}()

	// set the `client_id` into the context
	l.ctx = context.WithValue(l.ctx, common.ClientIDContextKey, in.GetCid())
	out, err := l.svcCtx.NodeRPC.ApiCall(
		l.ctx, in, grpc.Header(&header), grpc.Trailer(&trailer), grpc.UseCompressor(gzip.Name),
	)
	if err != nil {
		return nil, err
	}

	resp, err = respConvert(out)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func reqConvert(in *types.ApiCallReq) (out *pb.ApiCallReq, err error) {
	out = &pb.ApiCallReq{
		Cid:          in.Cid,
		Url:          in.Url,
		Method:       in.Method,
		Headers:      nil,
		Body:         nil,
		Authority:    in.Authority,
		CustomFields: nil,
	}

	out.Headers = make([]*http.HttpHeader, 0, len(in.Headers))
	for key, values := range in.Headers {
		for _, value := range values {
			out.Headers = append(
				out.Headers, &http.HttpHeader{
					Key:   key,
					Value: value,
				},
			)
		}
	}

	if in.ExtraOptions.UnUseJSONNumber {
		out.Body, err = protobuf.NewValue(in.Body, protobuf.WithJSONNumberToNumber())
	} else {
		out.Body, err = protobuf.NewValue(in.Body)
	}
	if err != nil {
		return nil, err
	}

	out.CustomFields, err = protobuf.NewStruct(in.CustomFields)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func respConvert(in *pb.ApiCallResp) (out *types.ApiCallResp, err error) { //nolint:unparam
	out = &types.ApiCallResp{}

	clientInfo := in.GetClientInfo()
	if clientInfo != nil {
		out.ClientInfo = &types.ClientBasicInfo{
			Cid:             clientInfo.GetCid(),
			Nid:             clientInfo.GetNid(),
			Product:         clientInfo.GetProduct(),
			Type:            clientInfo.GetType(),
			Status:          clientInfo.GetStatus(),
			CreatedAt:       clientInfo.GetCreatedAt().AsTime().UnixMilli(),
			LastCreatedAt:   clientInfo.GetLastCreatedAt().AsTime().UnixMilli(),
			LastRequestedAt: clientInfo.GetLastRequestedAt().AsTime().UnixMilli(),
			NumOfRequest:    clientInfo.GetNumOfRequest(),
		}
	}

	customFields := in.GetCustomFields()
	if customFields != nil {
		out.CustomFields = customFields.AsMap()
	}

	callResp := in.GetCallResp()
	if callResp != nil {
		out.CallResp = &types.CallResp{}

		header := callResp.GetHeader()
		if header != nil {
			out.CallResp.Header = make(map[string][]string, len(header))
			for _, h := range header {
				out.CallResp.Header[h.GetKey()] = append(out.CallResp.Header[h.GetKey()], h.GetValue())
			}
		}

		out.CallResp.Body = callResp.GetBody().AsInterface()
		out.CallResp.Status = callResp.GetStatus()
	}

	return out, nil
}
