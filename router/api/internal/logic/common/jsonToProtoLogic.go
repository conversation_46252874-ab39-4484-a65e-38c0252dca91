package common

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type JsonToProtoLogic struct {
	*BaseLogic
}

func NewJsonToProtoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *JsonToProtoLogic {
	return &JsonToProtoLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *JsonToProtoLogic) JsonToProto(req *types.JsonToProtoReq) (resp *types.JsonToProtoResp, err error) {
	data, err := protobuf.NewValue(req.Data)
	if err != nil {
		return nil, err
	}

	out, err := l.svcCtx.NodeRPC.JsonToProto(
		l.ctx, &pb.JsonToProtoReq{
			ProductName: req.ProductName,
			Message:     req.Message,
			Data:        data,
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.JsonToProtoResp{
		ProtoString: out.GetProtoString(),
	}, nil
}
