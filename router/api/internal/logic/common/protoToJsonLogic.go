package common

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type ProtoToJsonLogic struct {
	*BaseLogic
}

func NewProtoToJsonLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProtoToJsonLogic {
	return &ProtoToJsonLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ProtoToJsonLogic) ProtoToJson(req *types.ProtoToJsonReq) (resp *types.ProtoToJsonResp, err error) {
	out, err := l.svcCtx.NodeRPC.ProtoToJson(
		l.ctx, &pb.ProtoToJsonReq{
			ProductName: req.ProductName,
			Message:     req.Message,
			Data:        req.Data,
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.ProtoToJsonResp{
		JsonData: out.GetJsonData().AsInterface(),
	}, nil
}
