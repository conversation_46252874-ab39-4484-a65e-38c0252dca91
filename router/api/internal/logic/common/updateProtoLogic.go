package common

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type UpdateProtoLogic struct {
	*BaseLogic
}

func NewUpdateProtoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateProtoLogic {
	return &UpdateProtoLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateProtoLogic) UpdateProto(req *types.UpdateProtoReq) (resp *types.UpdateProtoResp, err error) {
	in := updateProtoReqConvert(req)

	out, err := l.svcCtx.NodeRPC.UpdateProto(l.ctx, in, zrpc.WithCallTimeout(constUpdateProtoTimeout))
	if err != nil {
		return nil, err
	}

	resp = &types.UpdateProtoResp{}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}

func updateProtoReqConvert(in *types.UpdateProtoReq) *pb.UpdateProtoReq {
	if in == nil {
		return nil
	}

	out := &pb.UpdateProtoReq{
		ProductName: in.ProductName,
		Branch:      in.Branch,
		Projects:    make([]*pb.UpdateProtoReq_ProjectConfig, 0, len(in.Projects)),
	}
	for _, project := range in.Projects {
		config := projectConfigConvert(project)
		if config == nil {
			continue
		}

		out.Projects = append(out.Projects, config)
	}

	return out
}

func projectConfigConvert(in *types.ProjectConfig) *pb.UpdateProtoReq_ProjectConfig {
	if in == nil {
		return nil
	}

	out := &pb.UpdateProtoReq_ProjectConfig{
		ProjectName:  in.ProjectName,
		GitUrl:       in.GitConfig.GitURL,
		Branch:       in.GitConfig.Branch,
		ImportPath:   in.GitConfig.ImportPath,
		ExcludePaths: in.ExcludePaths,
		ExcludeFiles: in.ExcludeFiles,
		Dependencies: make([]*pb.UpdateProtoReq_DependenceConfig, 0, len(in.Dependencies)),
	}
	for _, dependence := range in.Dependencies {
		config := dependenceConfigConvert(dependence)
		if config == nil {
			continue
		}

		out.Dependencies = append(out.Dependencies, config)
	}

	return out
}

func dependenceConfigConvert(in *types.DependenceConfig) *pb.UpdateProtoReq_DependenceConfig {
	if in == nil {
		return nil
	}

	if in.Git != nil {
		return &pb.UpdateProtoReq_DependenceConfig{
			Config: &pb.UpdateProtoReq_DependenceConfig_Git{
				Git: &pb.UpdateProtoReq_GitConfig{
					GitUrl:     in.Git.GitURL,
					Branch:     in.Git.Branch,
					ImportPath: in.Git.ImportPath,
				},
			},
		}
	} else if in.LocalPath != "" {
		return &pb.UpdateProtoReq_DependenceConfig{
			Config: &pb.UpdateProtoReq_DependenceConfig_LocalPath{
				LocalPath: in.LocalPath,
			},
		}
	}

	return nil
}
