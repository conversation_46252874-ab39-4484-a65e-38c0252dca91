package mon

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type GetNodeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetNodeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetNodeListLogic {
	return &GetNodeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetNodeListLogic) GetNodeList(req *types.GetNodeListReq) (resp *types.GetNodeListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
