package mon

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type GetNodeStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetNodeStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetNodeStatLogic {
	return &GetNodeStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetNodeStatLogic) GetNodeStat(req *types.GetNodeStatsReq) (resp *types.GetNodeStatsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
