package mon

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

type GetRouterListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRouterListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRouterListLogic {
	return &GetRouterListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRouterListLogic) GetRouterList(req *types.GetRouterListReq) (resp *types.GetRouterListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
