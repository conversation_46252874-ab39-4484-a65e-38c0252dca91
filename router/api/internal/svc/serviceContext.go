package svc

import (
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/node/rpc/client/nodeservice"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/resolver"
)

type ServiceContext struct {
	Config config.Config

	Redis *redis.Redis

	NodeRPC nodeservice.NodeService

	Validator *utils.Validator
}

func NewServiceContext(c config.Config) *ServiceContext {
	options := make([]zrpc.ClientOption, 0, 2)
	if strings.HasPrefix(c.NodeRPC.Target, string(constants.REDIS)) ||
		strings.HasPrefix(c.NodeRPC.Target, string(constants.REDIS_CLUSTER)) {
		s, err := resolver.GetServiceConfig(c.NodeRPC.Target)
		if err != nil {
			logx.Must(err)
		}

		options = append(options, zrpc.WithDialOption(grpc.WithDefaultServiceConfig(s)))
	}
	if c.ClientOptions.KeepaliveTime > 0 && c.ClientOptions.KeepaliveTimeout > 0 {
		options = append(
			options, zrpc.WithDialOption(
				grpc.WithKeepaliveParams(
					keepalive.ClientParameters{
						Time:                c.ClientOptions.KeepaliveTime,
						Timeout:             c.ClientOptions.KeepaliveTimeout,
						PermitWithoutStream: c.ClientOptions.KeepalivePermitWithoutStream,
					},
				),
			),
		)
	}

	return &ServiceContext{
		Config: c,

		Redis: redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),

		NodeRPC: nodeservice.NewNodeService(zrpc.MustNewClient(c.NodeRPC, options...)),

		Validator: utils.NewValidator(c.Validator.Locale),
	}
}
