package types

type Router struct {
	Rid       string `json:"rid"`        // 路由服务ID
	Host      string `json:"host"`       // 服务地址（主机名称或者IP）
	Port      int64  `json:"port"`       // 服务端口
	StartedAt int64  `json:"started_at"` // 启动时间
}

type Node struct {
	Nid       string `json:"nid"`        // 节点服务ID
	Host      string `json:"host"`       // 服务地址（主机名称或者IP）
	Port      int64  `json:"port"`       // 服务端口
	StartedAt int64  `json:"started_at"` // 启动时间
}

type GetRouterListReq struct{}

type GetRouterListResp struct {
	Total int64    `json:"total"`
	Items []Router `json:"items"`
}

type GetNodeListReq struct{}

type GetNodeListResp struct {
	Total int64  `json:"total"`
	Items []Node `json:"items"`
}

type GetNodeStatsReq struct {
	Nid string `json:"nid" validate:"required"` // 节点服务ID
}

type GetNodeStatsResp struct {
	Stat any `json:"info"`
}
