package middlewares

import (
	"bufio"
	"bytes"
	"net"
	"net/http"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/internal/types"
)

// ClientInfoHandler returns a middleware that set the client info into response header.
// Deprecated: use CustomHeadersHandler instead.
func ClientInfoHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(
		func(w http.ResponseWriter, r *http.Request) {
			var clientInfo types.ClientBasicInfo
			rw := newResponseWriter(w)
			defer func() {
				rw.mutex.Lock()
				defer rw.mutex.Unlock()

				header := w.Header()
				for k, vs := range rw.h {
					header[k] = vs
				}

				if clientInfo.Product != "" {
					header.Set(common.ProductNameHeaderKey, clientInfo.Product)
				}
				if clientInfo.Type != "" {
					header.Set(common.ClientTypeHeaderKey, clientInfo.Type)
				}

				// We don't need to write header 200, because it's written by default.
				// If we write it again, it will cause a warning: `http: superfluous response.WriteHeader call`.
				if rw.code != http.StatusOK {
					w.WriteHeader(rw.code)
				}
				_, _ = w.Write(rw.buf.Bytes())
			}()

			next.ServeHTTP(rw, r.WithContext(internal.WithClientBasicInfo(r.Context(), &clientInfo)))
		},
	)
}

// ClientInfoMiddleware returns a middleware that set the client info into response header.
// Deprecated: use CustomHeadersMiddleware instead.
func ClientInfoMiddleware() rest.Middleware {
	return rest.ToMiddleware(ClientInfoHandler)
}

// CustomHeadersHandler returns a middleware that set the custom headers into response header.
func CustomHeadersHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(
		func(w http.ResponseWriter, r *http.Request) {
			headers := make(http.Header)

			rw := newResponseWriter(w)
			defer func() {
				rw.mutex.Lock()
				defer rw.mutex.Unlock()

				header := w.Header()
				for k, vs := range rw.h {
					header[k] = vs
				}

				for k, vs := range headers {
					if !strings.EqualFold(k[:2], "X-") {
						continue
					}

					for _, v := range vs {
						header.Add(k, v)
					}
				}

				// We don't need to write header 200, because it's written by default.
				// If we write it again, it will cause a warning: `http: superfluous response.WriteHeader call`.
				if rw.code != http.StatusOK {
					w.WriteHeader(rw.code)
				}
				_, _ = w.Write(rw.buf.Bytes())
			}()

			next.ServeHTTP(rw, r.WithContext(internal.WithCustomHeaders(r.Context(), headers)))
		},
	)
}

// CustomHeadersMiddleware returns a middleware that set the custom headers into response header.
func CustomHeadersMiddleware() rest.Middleware {
	return rest.ToMiddleware(CustomHeadersHandler)
}

// responseWriter is a helper to delay sealing a http.ResponseWriter on writing code.
type responseWriter struct {
	w   http.ResponseWriter
	h   http.Header
	buf bytes.Buffer

	mutex       sync.Mutex
	wroteHeader bool
	code        int
}

// newResponseWriter returns a responseWriter.
// If writer is already a responseWriter, it returns writer directly.
func newResponseWriter(writer http.ResponseWriter) *responseWriter {
	return &responseWriter{
		w: writer,
		h: make(http.Header),

		wroteHeader: false,
		code:        http.StatusOK,
	}
}

// Flush implements the http.Flusher interface.
func (w *responseWriter) Flush() {
	flusher, ok := w.w.(http.Flusher)
	if !ok {
		return
	}

	header := w.w.Header()
	for k, v := range w.h {
		header[k] = v
	}

	_, _ = w.w.Write(w.buf.Bytes())
	w.buf.Reset()
	flusher.Flush()
}

// Header returns the underline temporary http.Header.
func (w *responseWriter) Header() http.Header {
	return w.h
}

// Hijack implements the http.Hijacker interface.
// This expands the Response to fulfill http.Hijacker if the underlying http.ResponseWriter supports it.
func (w *responseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	if hijacked, ok := w.w.(http.Hijacker); ok {
		return hijacked.Hijack()
	}

	return nil, nil, errors.New("server doesn't support hijacking")
}

// Push implements the http.Pusher interface.
// This expands the Response to fulfill http.Pusher if the underlying http.ResponseWriter supports it.
func (w *responseWriter) Push(target string, opts *http.PushOptions) error {
	if pusher, ok := w.w.(http.Pusher); ok {
		return pusher.Push(target, opts)
	}

	return http.ErrNotSupported
}

// Write writes bytes into w.
func (w *responseWriter) Write(p []byte) (int, error) {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if !w.wroteHeader {
		w.wroteHeader = true
		w.code = http.StatusOK
	}

	return w.buf.Write(p)
}

// WriteHeader writes code into w, and not sealing the writer.
func (w *responseWriter) WriteHeader(code int) {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if !w.wroteHeader {
		w.wroteHeader = true
		w.code = code
	}
}
