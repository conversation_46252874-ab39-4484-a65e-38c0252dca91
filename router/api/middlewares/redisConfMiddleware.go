package middlewares

import (
	"context"
	"net/http"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

// RedisConfHandler returns a middleware that set the redis conf into request context.
func RedisConfHandler(c redis.RedisConf) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(
			func(w http.ResponseWriter, r *http.Request) {
				next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), common.RedisConfContextKey, c)))
			},
		)
	}
}

// RedisConfMiddleware returns a middleware that set the redis conf into request context.
func RedisConfMiddleware(c redis.RedisConf) rest.Middleware {
	return rest.ToMiddleware(RedisConfHandler(c))
}
