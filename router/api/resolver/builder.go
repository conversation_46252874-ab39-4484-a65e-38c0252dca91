package resolver

import (
	grpcresolver "google.golang.org/grpc/resolver"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	_ grpcresolver.Builder = (*redisBuilder)(nil)
	_ grpcresolver.Builder = (*redisClusterBuilder)(nil)
)

func init() {
	grpcresolver.Register(&redisBuilder{})
	grpcresolver.Register(&redisClusterBuilder{})
}

type redisBuilder struct{}

func (b *redisBuilder) Build(target grpcresolver.Target, cc grpcresolver.ClientConn, opts grpcresolver.BuildOptions) (
	grpcresolver.Resolver, error,
) {
	c, s, err := parseURL(target.URL)
	if err != nil {
		return nil, err
	} else if err = c.Validate(); err != nil {
		return nil, err
	}

	return newResolver(target, cc, c, s)
}

func (b *redisBuilder) Scheme() string {
	return string(constants.REDIS)
}

type redisClusterBuilder struct {
	redisBuilder
}

func (b *redisClusterBuilder) Scheme() string {
	return string(constants.REDIS_CLUSTER)
}
