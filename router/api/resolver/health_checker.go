package resolver

import (
	"context"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/health/grpc_health_v1"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

const (
	healthCheckTimeout       = 2 * time.Second
	healthWatchRetryInterval = 10 * time.Second
	healthCheckInterval      = 30 * time.Second
)

// HealthChecker 为resolver提供健康检查功能
type HealthChecker struct {
	logx.Logger

	ctx         context.Context
	cancel      context.CancelFunc
	mutex       sync.RWMutex
	connections map[string]*healthConnection
}

type healthConnection struct {
	address string
	conn    *grpc.ClientConn
	client  grpc_health_v1.HealthClient

	ctx    context.Context
	cancel context.CancelFunc

	// 使用独立的锁保护健康状态，避免全局锁竞争
	mutex     sync.RWMutex
	isHealthy bool
	lastCheck time.Time
	lastError error
}

// NewHealthChecker 创建新的健康检查器
func NewHealthChecker() *HealthChecker {
	ctx, cancel := context.WithCancel(context.Background())

	hc := &HealthChecker{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, common.ID())),

		ctx:         ctx,
		cancel:      cancel,
		connections: make(map[string]*healthConnection),
	}

	// 启动定期健康检查
	threading.GoSafe(hc.startPeriodicHealthCheck)

	return hc
}

// AddAddress 添加需要健康检查的地址
func (hc *HealthChecker) AddAddress(address string) error {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if _, exists := hc.connections[address]; exists {
		return nil
	}

	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		hc.Errorf("failed to new grpc client, address: %s, error: %v", address, err)
		return err
	}

	ctx, cancel := context.WithCancel(hc.ctx)
	healthConn := &healthConnection{
		address: address,
		conn:    conn,
		client:  grpc_health_v1.NewHealthClient(conn),

		ctx:    ctx,
		cancel: cancel,

		isHealthy: false,
		lastCheck: time.Now(),
	}
	hc.connections[address] = healthConn

	// 启动Watch监控
	threading.GoSafe(
		func() {
			hc.watchHealth(healthConn)
		},
	)

	// 立即进行一次健康检查
	hc.checkHealth(healthConn)

	hc.Infof("added health check for address: %s", address)
	return nil
}

// RemoveAddress 移除地址的健康检查
func (hc *HealthChecker) RemoveAddress(address string) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if healthConn, exists := hc.connections[address]; exists {
		healthConn.cancel()
		if healthConn.conn != nil {
			_ = healthConn.conn.Close()
		}
		delete(hc.connections, address)
		hc.Infof("removed health check for address: %s", address)
	}
}

// IsHealthy 检查地址是否健康
func (hc *HealthChecker) IsHealthy(address string) bool {
	hc.mutex.RLock()
	healthConn, exists := hc.connections[address]
	hc.mutex.RUnlock()

	if !exists {
		return false // 未知地址默认为不健康
	}

	// 使用healthConnection自己的锁来读取健康状态
	healthConn.mutex.RLock()
	defer healthConn.mutex.RUnlock()
	return healthConn.isHealthy
}

// GetHealthStatus 获取所有地址的健康状态
func (hc *HealthChecker) GetHealthStatus() map[string]bool {
	// 先获取所有连接的引用
	hc.mutex.RLock()
	connections := make([]*healthConnection, 0, len(hc.connections))
	addresses := make([]string, 0, len(hc.connections))
	for address, healthConn := range hc.connections {
		connections = append(connections, healthConn)
		addresses = append(addresses, address)
	}
	hc.mutex.RUnlock()

	// 然后分别读取每个连接的健康状态
	status := make(map[string]bool)
	for i, healthConn := range connections {
		healthConn.mutex.RLock()
		status[addresses[i]] = healthConn.isHealthy
		healthConn.mutex.RUnlock()
	}
	return status
}

// checkHealth 执行单次健康检查
func (hc *HealthChecker) checkHealth(healthConn *healthConnection) {
	ctx, cancel := context.WithTimeout(healthConn.ctx, healthCheckTimeout)
	defer cancel()

	req := &grpc_health_v1.HealthCheckRequest{
		Service: "", // 空字符串表示检查整个服务器
	}
	resp, err := healthConn.client.Check(ctx, req)

	healthConn.mutex.Lock()
	defer healthConn.mutex.Unlock()

	healthConn.lastCheck = time.Now()
	if err != nil {
		healthConn.isHealthy = false
		healthConn.lastError = err
		hc.Warnf("got an error when checking health, address: %s, error: %v", healthConn.address, err)
	} else {
		isHealthy := resp.Status == grpc_health_v1.HealthCheckResponse_SERVING
		if healthConn.isHealthy != isHealthy {
			if isHealthy {
				hc.Infof("the grpc connection is healthy, address: %s", healthConn.address)
			} else {
				hc.Warnf("the grpc connection is unhealthy, address: %s, status: %s", healthConn.address, resp.Status)
			}
		}
		healthConn.isHealthy = isHealthy
		healthConn.lastError = nil
	}
}

// watchHealth 使用Watch方法持续监控健康状态
func (hc *HealthChecker) watchHealth(healthConn *healthConnection) {
	req := &grpc_health_v1.HealthCheckRequest{
		Service: "", // 空字符串表示检查整个服务器
	}

	for {
		select {
		case <-hc.ctx.Done():
			hc.Info("got a done signal from health checker context")
			return
		case <-healthConn.ctx.Done():
			hc.Info("got a done signal from health connection context")
			return
		default:
		}

		stream, err := healthConn.client.Watch(healthConn.ctx, req)
		if err != nil {
			hc.Errorf("failed to start health watch, address: %s, error: %v", healthConn.address, err)
			time.Sleep(healthWatchRetryInterval) // 等待后重试
			continue
		}

		for {
			resp, err := stream.Recv()
			if err != nil {
				hc.Warnf("got an error when watching health, address: %s, error: %v", healthConn.address, err)
				break // 跳出内层循环，重新建立stream
			}

			healthConn.mutex.Lock()
			isHealthy := resp.Status == grpc_health_v1.HealthCheckResponse_SERVING
			if healthConn.isHealthy != isHealthy {
				if isHealthy {
					hc.Infof("the grpc connection is healthy, address: %s", healthConn.address)
				} else {
					hc.Warnf(
						"the grpc connection is unhealthy, address: %s, status: %s", healthConn.address, resp.Status,
					)
				}
			}
			healthConn.isHealthy = isHealthy
			healthConn.lastCheck = time.Now()
			healthConn.lastError = nil
			healthConn.mutex.Unlock()
		}

		// 如果到这里，说明stream断开了，等待一段时间后重试
		time.Sleep(healthWatchRetryInterval)
	}
}

// startPeriodicHealthCheck 启动定期健康检查（作为Watch的补充）
func (hc *HealthChecker) startPeriodicHealthCheck() {
	ticker := time.NewTicker(healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-hc.ctx.Done():
			return
		case <-ticker.C:
			hc.performPeriodicCheck()
		}
	}
}

// performPeriodicCheck 执行定期健康检查
func (hc *HealthChecker) performPeriodicCheck() {
	hc.mutex.RLock()
	connections := make([]*healthConnection, 0, len(hc.connections))
	for _, conn := range hc.connections {
		connections = append(connections, conn)
	}
	hc.mutex.RUnlock()

	_ = mr.MapReduceVoid[*healthConnection, any](
		func(source chan<- *healthConnection) {
			for _, healthConn := range connections {
				if healthConn == nil {
					continue
				}
				source <- healthConn
			}
		},
		func(item *healthConnection, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			// 检查连接状态
			if item.conn != nil {
				state := item.conn.GetState()
				if state != connectivity.Ready && state != connectivity.Connecting {
					hc.Warnf("the grpc connection is not ready, address: %s, state: %s", item.address, state)
				}
			}

			// 如果最近没有通过Watch更新，执行一次Check
			if time.Since(item.lastCheck) > time.Minute {
				hc.checkHealth(item)
			}
		},
		func(pipe <-chan any, cancel func(error)) {
		},
		mr.WithContext(hc.ctx),
	)
}

// Close 关闭健康检查器
func (hc *HealthChecker) Close() {
	hc.cancel()

	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	for _, healthConn := range hc.connections {
		healthConn.cancel()
		if healthConn.conn != nil {
			_ = healthConn.conn.Close()
		}
	}
	hc.connections = nil
}

type Statistics struct {
	Total     int `json:"total"`
	Healthy   int `json:"healthy"`
	Unhealthy int `json:"unhealthy"`
}

// GetStatistics 获取健康检查统计信息
func (hc *HealthChecker) GetStatistics() Statistics {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	healthy := 0
	for _, healthConn := range hc.connections {
		if healthConn.isHealthy {
			healthy++
		}
	}

	total := len(hc.connections)
	return Statistics{
		Total:     total,
		Healthy:   healthy,
		Unhealthy: total - healthy,
	}
}
