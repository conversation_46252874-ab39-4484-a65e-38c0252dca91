package resolver

import (
	"context"
	"maps"
	"sync"
	"time"

	"github.com/pkg/errors"
	goredis "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	zeroredis "github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/threading"
	"google.golang.org/grpc/attributes"
	grpcresolver "google.golang.org/grpc/resolver"
	"google.golang.org/grpc/serviceconfig"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/registry"
)

const defaultTickerDuration = 30 * time.Minute // 定时进行`resolve`操作

var (
	_ grpcresolver.Resolver = (*resolver)(nil)

	resolveNowOptions grpcresolver.ResolveNowOptions
)

type resolver struct {
	logx.Logger

	target grpcresolver.Target
	cc     grpcresolver.ClientConn
	c      zeroredis.RedisConf

	ctx    context.Context
	quit   chan lang.PlaceholderType
	rn     chan lang.PlaceholderType // rn channel is used by ResolveNow() to force an immediate resolution of the target.
	ticker *timewheel.Ticker
	wg     sync.WaitGroup
	mu     sync.RWMutex

	rdb   goredis.UniversalClient
	reg   *registry.Registry
	store map[string]grpcresolver.Address
	sc    *serviceconfig.ParseResult

	// 健康检查器
	healthChecker *HealthChecker
}

func newResolver(target grpcresolver.Target, cc grpcresolver.ClientConn, c zeroredis.RedisConf, s string) (
	*resolver, error,
) {
	rdb, err := qetredis.GetClient(c)
	if err != nil {
		return nil, err
	}

	sc := cc.ParseServiceConfig(s)
	if sc.Err != nil {
		return nil, sc.Err
	}

	ctx := context.Background()
	r := &resolver{
		Logger: logx.WithContext(ctx).WithFields(logx.Field(common.ConstServerIDLogFieldKey, common.ID())),

		target: target,
		cc:     cc,
		c:      c,

		ctx:    ctx,
		quit:   make(chan lang.PlaceholderType),
		rn:     make(chan lang.PlaceholderType, 1),
		ticker: timewheel.NewTicker(defaultTickerDuration),

		rdb:   rdb,
		reg:   registry.GetRegistry(c, registry.WithSubscribe()),
		store: make(map[string]grpcresolver.Address),
		sc:    sc,

		healthChecker: NewHealthChecker(),
	}
	threading.GoSafe(r.watch)
	r.ResolveNow(resolveNowOptions)

	r.Infof("create a resolver, target: %s, created_at: %s", r.target, time.Now().Format(time.DateTime))
	return r, nil
}

func (r *resolver) ResolveNow(grpcresolver.ResolveNowOptions) {
	select {
	case r.rn <- lang.Placeholder:
	default:
	}
}

func (r *resolver) Close() {
	r.ticker.Stop()
	close(r.quit)
	r.wg.Wait()

	// 关闭健康检查器
	if r.healthChecker != nil {
		r.healthChecker.Close()
	}
	r.Infof("close the resolver, target: %s, closed_at: %s", r.target, time.Now().Format(time.DateTime))
}

func (r *resolver) watch() {
	r.wg.Add(1)
	defer r.wg.Done()

	for {
		select {
		case <-r.quit:
			return
		case <-r.ticker.C:
			r.ResolveNow(resolveNowOptions)
		case <-r.rn:
			r.resolve()
		case msg, ok := <-r.reg.GetChannel():
			if !ok {
				r.Error("the channel of PubSub has been closed")
				// r.reg = registry.GetRegistry(r.c, registry.WithSubscribe())
				// break // 跳出`select`
				return
			}

			r.Infof("receive a channel message: %s", msg.String())

			event := &pb.Event{}
			if err := protobuf.UnmarshalJSONFromString(msg.Payload, event); err != nil {
				r.Errorf("failed to unmarshal the payload to event, payload: %s, error: %+v", msg.Payload, err)
				break // 跳出`select`
			}

			switch msg.Channel {
			case registry.RouterEventRedisChannel:
			case registry.NodeEventRedisChannel:
				if event.GetTarget() != pb.TargetType_NODE {
					r.Errorf(
						"the channel does not match the event target, channel: %s, target: %s",
						msg.Channel, event.GetTarget().String(),
					)
					continue // 跳出`switch`和`select`
				}

				if err := r.handleNodeEvent(event); err != nil {
					r.Errorf("failed to handle the node event, error: %+v", err)
					continue // 跳出`switch`和`select`
				}
			case registry.ClientEventRedisChannel:
				if event.GetTarget() != pb.TargetType_CLIENT {
					r.Errorf(
						"the channel does not match the event target, channel: %s, target: %s",
						msg.Channel, event.GetTarget().String(),
					)
					continue // 跳出`switch`和`select`
				}

				if err := r.handleClientEvent(event); err != nil {
					r.Errorf("failed to handle the client event, error: %+v", err)
					continue // 跳出`switch`和`select`
				}
			default:
				r.Errorf("unknown channel: %s", msg.Channel)
				continue // 跳出`switch`和`select`
			}

			r.updateTargetState()
		}
	}
}

func (r *resolver) resolve() {
	infos, err := r.reg.GetAllNodeInfo(r.ctx)
	if err != nil {
		r.Errorf("failed to get the info for matching nodes, error: %+v", err)
		return
	}
	r.Infof("matching nodes: [%d, %s]", len(infos), protobuf.MarshalJSONWithMessagesToStringIgnoreError(infos))

	r.mu.Lock()

	// 更新存在的地址
	exists := make(map[string]lang.PlaceholderType, len(infos))
	for _, info := range infos {
		addr := info.GetInfo().Address()
		exists[addr] = lang.Placeholder

		r.store[addr] = grpcresolver.Address{
			Addr:       addr,
			Attributes: attributes.New(common.NodeInfoAttributeKey, info),
		}

		// 为新地址添加健康检查
		if r.healthChecker != nil {
			if err = r.healthChecker.AddAddress(addr); err != nil {
				r.Warnf("failed to add health check for address %s: %v", addr, err)
			}
		}
	}

	maps.DeleteFunc(
		r.store, func(s string, address grpcresolver.Address) bool {
			if _, ok := exists[s]; ok {
				// 地址仍然存在，不删除
				return false
			}

			if r.healthChecker != nil && r.healthChecker.IsHealthy(s) {
				// 如果有健康检查器，且地址是健康的，不删除
				return false
			}

			defer func() {
				// 移除健康检查
				if r.healthChecker != nil {
					r.healthChecker.RemoveAddress(s)
				}
			}()
			return true
		},
	)

	r.mu.Unlock()

	r.updateTargetState()
}

func (r *resolver) updateTargetState() {
	// 过滤健康的地址
	healthy := make([]string, 0, len(r.store))
	unhealthy := make([]string, 0, len(r.store))
	state := grpcresolver.State{
		Addresses:     make([]grpcresolver.Address, 0, len(r.store)),
		Endpoints:     make([]grpcresolver.Endpoint, 0, len(r.store)),
		ServiceConfig: r.sc,
	}

	r.mu.RLock()
	for addr, v := range r.store {
		// 检查健康状态
		if r.healthChecker != nil && !r.healthChecker.IsHealthy(addr) {
			unhealthy = append(unhealthy, addr)
			continue
		}
		healthy = append(healthy, addr)

		state.Addresses = append(state.Addresses, v)
		state.Endpoints = append(
			state.Endpoints, grpcresolver.Endpoint{
				Addresses:  []grpcresolver.Address{v},
				Attributes: v.Attributes,
			},
		)
	}
	r.mu.RUnlock()

	// 记录健康检查统计信息
	if r.healthChecker != nil {
		r.Infof("health check stats: %s", jsonx.MarshalIgnoreError(r.healthChecker.GetStatistics()))
	}

	r.Infof(
		"update target state by resolver, healthy: [%d, %s], unhealthy: [%d, %s], state: %s",
		len(healthy), jsonx.MarshalIgnoreError(healthy),
		len(unhealthy), jsonx.MarshalIgnoreError(unhealthy),
		jsonx.MarshalIgnoreError(state),
	)

	if err := r.cc.UpdateState(state); err != nil {
		r.Errorf(
			"failed to update target state by resolver, state: %s, error: %+v", jsonx.MarshalIgnoreError(state), err,
		)
	}
}

func (r *resolver) handleNodeEvent(event *pb.Event) error {
	r.Infof("receive a node event: %s", protobuf.MarshalJSONIgnoreError(event))

	info := event.GetServerInfo()
	addr := info.Address()
	defer func() {
		if r.healthChecker == nil {
			return
		}

		switch event.Operate {
		case pb.OperateType_CREATE:
			if err := r.healthChecker.AddAddress(addr); err != nil {
				r.Warnf("failed to add address to health checker, address: %s, error: %v", addr, err)
			}
		case pb.OperateType_DELETE:
			r.healthChecker.RemoveAddress(addr)
		default:
		}
	}()

	r.mu.Lock()
	defer r.mu.Unlock()

	switch event.Operate {
	case pb.OperateType_CREATE:
		if r.reg.Key() != info.GetRegistry() {
			r.Warnf(
				"no need to save the node info cause by different registry, resolver: %s, node: %s",
				r.reg.Key(), info.GetRegistry(),
			)
			return nil
		}

		r.store[addr] = grpcresolver.Address{
			Addr: addr,
			Attributes: attributes.New(
				common.NodeInfoAttributeKey, &pb.NodeServerInfo{
					Info:     info,
					Metadata: &pb.NodeServerInfo_Metadata{},
				},
			),
		}
	case pb.OperateType_DELETE:
		delete(r.store, addr)
	default:
		return errors.Errorf(
			"invalid operate type of node event, event: %s", protobuf.MarshalJSONIgnoreError(event),
		)
	}

	return nil
}

func (r *resolver) handleClientEvent(event *pb.Event) error {
	r.Infof("receive a client event: %s", protobuf.MarshalJSONIgnoreError(event))

	attrInfo, err := r.reg.GetNodeAttrInfoByNodeID(r.ctx, event.GetClientInfo().GetNid())
	if err != nil {
		return err
	}

	switch event.Operate {
	case pb.OperateType_CREATE:
		r.createClient(event.GetClientInfo(), attrInfo)
	case pb.OperateType_DELETE:
		r.deleteClient(event.GetClientInfo(), attrInfo)
	default:
		return errors.Errorf(
			"invalid operate type of client event, event: %s", protobuf.MarshalJSONIgnoreError(event),
		)
	}

	return nil
}

func (r *resolver) createClient(c *pb.ClientInfo, attr *pb.AttrInfo) {
	clientID := c.GetCid()
	if clientID == "" {
		r.Errorf("the client id is empty, client info: %s", protobuf.MarshalJSONIgnoreError(c))
		return
	}

	userID := c.GetUserInfo().GetUid()
	if userID == "" {
		r.Warnf("the user id is empty, user info: %s", protobuf.MarshalJSONIgnoreError(c.GetUserInfo()))
	}

	node := attr.GetNode()
	if node == nil {
		r.Errorf("the node attr info is empty, attr info: %s", protobuf.MarshalJSONIgnoreError(attr))
		return
	}

	var (
		clients map[string]*pb.ClientInfo
		client  *pb.ClientInfo

		users map[string]*pb.UserInfo
		user  *pb.UserInfo

		exists bool
	)

	clients = attr.GetClients()
	client, exists = clients[clientID]
	if !exists {
		return
	}

	if userID != "" {
		users = attr.GetUsers()
		user, exists = users[userID]
		if !exists {
			return
		}
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	addr := node.Address()
	v, exists := r.store[addr]
	if exists && v.Attributes != nil {
		if ai, ok := v.Attributes.Value(common.NodeAttrInfoAttributeKey).(*pb.AttrInfo); ok {
			// if attribute information already exists, then add the client information
			cs := ai.GetClients()
			if cs == nil {
				ai.Clients = map[string]*pb.ClientInfo{
					clientID: client,
				}
			} else {
				cs[clientID] = client
			}

			if userID != "" && user != nil {
				us := ai.GetUsers()
				if us == nil {
					ai.Users = map[string]*pb.UserInfo{
						userID: user,
					}
				} else {
					us[userID] = user
				}
			}

			return
		}
	}

	// if there is no attribute information, then create attribute information
	r.store[addr] = grpcresolver.Address{
		Addr:       addr,
		Attributes: attributes.New(common.NodeAttrInfoAttributeKey, attr),
	}
}

func (r *resolver) deleteClient(c *pb.ClientInfo, attr *pb.AttrInfo) {
	clientID := c.GetCid()
	if clientID == "" {
		r.Errorf("the client id is empty, client info: %s", protobuf.MarshalJSONIgnoreError(c))
		return
	}

	userID := c.GetUserInfo().GetUid()
	if userID == "" {
		r.Warnf("the user id is empty, user info: %s", protobuf.MarshalJSONIgnoreError(c.GetUserInfo()))
	}

	node := attr.GetNode()
	if node == nil {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	addr := node.Address()
	v, exists := r.store[addr]

	// if there is no attribute information, then ignore it
	if exists && v.Attributes != nil {
		if ai, ok := v.Attributes.Value(common.NodeAttrInfoAttributeKey).(*pb.AttrInfo); ok {
			// if attribute information already exists, then delete the client information
			if cs := ai.GetClients(); cs != nil {
				delete(cs, clientID)
			}

			if userID != "" {
				if us := ai.GetUsers(); us != nil {
					delete(us, userID)
				}
			}
		}
	}
}
