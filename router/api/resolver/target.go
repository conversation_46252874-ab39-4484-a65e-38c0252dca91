package resolver

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/balancer"
)

const (
	slash = "/"
	comma = ","
)

// parseURL constructs a redis.RedisConf from the given url.URL.
// And the format of the url.URL is as follows:
// Redis Standalone: redis://[:password@]host[:port][/database][?balancer=<balancer name>]
// Or
// Redis Cluster: redis-cluster://[:password@]host1[:port1][,host2[:port2]][,hostN[:portN]][/database][?balancer=<balancer name>]
func parseURL(u url.URL) (c redis.RedisConf, s string, err error) {
	if u.Scheme != string(constants.REDIS) && u.Scheme != string(constants.REDIS_CLUSTER) {
		return c, "", errors.Errorf("the scheme of target is unknown, target: %s", u.String())
	} else if u.Host == "" {
		return c, "", errors.Errorf("the host of target is empty, target: %s", u.String())
	}

	var password string
	if u.User != nil {
		if s, ok := u.User.Password(); ok {
			password = s
		}
	}

	var db int
	path := strings.TrimPrefix(u.Path, slash)
	db, _ = strconv.Atoi(path)

	c = redis.RedisConf{
		Host:        u.Host,
		Type:        redis.NodeType,
		Pass:        password,
		DB:          db,
		NonBlock:    true,
		PingTimeout: time.Second,
	}

	if u.Scheme == string(constants.REDIS_CLUSTER) || strings.Contains(u.Host, comma) {
		c.Type = redis.ClusterType
	}

	s = jsonx.MarshalToStringIgnoreError(
		map[string]any{
			"loadBalancingConfig": []map[string]any{
				{
					balancer.Name: map[string]any{
						"redisConf": c,
					},
				},
			},
		},
	)
	if name := u.Query().Get("balancer"); name == balancer.NameV1 {
		s = fmt.Sprintf(`{"loadBalancingPolicy":"%s"}`, name)
	}

	return c, s, nil
}

func GetServiceConfig(target string) (string, error) {
	u, err := url.Parse(target)
	if err != nil {
		return "", err
	}

	_, s, err := parseURL(*u)
	if err != nil {
		return "", err
	}

	return s, nil
}
