package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
)

const (
	rootCmdUse   = common.RouterServiceName
	rootCmdShort = "The router service is one part of the api-proxy."
	rootCmdLong  = `The router service is one part of the api-proxy.
It is primarily responsible for request routing and event notifications.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
