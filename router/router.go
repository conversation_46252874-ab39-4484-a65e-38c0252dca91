package main

import (
	"github.com/spf13/cobra"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	api "gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/api/server"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/router/cmd"
)

var apiConfigFile = new(string)

func main() {
	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		sg := service.NewServiceGroup()
		defer sg.Stop()

		ss, err := server.NewCombineServer(
			[][]server.Option{
				api.Options(*apiConfigFile),
			},
		)
		if err != nil {
			return err
		}

		for _, s := range ss {
			sg.Add(s)
		}

		sg.Start()

		return nil
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.StringVar(apiConfigFile, "api-config", "api/etc/router.yaml", "the config file of rpc service")

	cobra.CheckErr(root.Execute())
}
