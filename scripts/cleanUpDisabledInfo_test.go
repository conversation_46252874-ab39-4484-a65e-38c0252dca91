package scripts

import (
	"context"
	"errors"
	"flag"
	"testing"

	"github.com/redis/go-redis/v9"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common"
	"gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb"
)

// build command: cd ./scripts; GOOS=linux GOARCH=amd64 go test -c cleanUpDisabledInfo_test.go -o cleanUpDisabledInfo.test.linux_amd64

const (
	clientInfoKey = "clients:info:hash"
	userInfoKey   = "users:info:hash"
)

var redisHost = flag.String("redis_host", "", "redis host")

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestCleanUpDisabledInfo(t *testing.T) {
	if *redisHost == "" {
		t.Fatal("not set the `redis_host`")
	}

	ctx := context.Background()
	rdb := redis.NewClient(
		&redis.Options{
			Addr:         *redisHost,
			Password:     "",
			DB:           0,
			MaxRetries:   3,
			MinIdleConns: 8,
			TLSConfig:    nil,
		},
	)

	cleanUpDisabledClients(t, ctx, rdb)
	cleanUpDisabledUsers(t, ctx, rdb)
}

func cleanUpDisabledClients(t *testing.T, ctx context.Context, rdb *redis.Client) {
	var (
		nodes   = make(map[string]bool, 16)
		clients = make(map[string][]string, 8)

		keys   []string
		cursor uint64
		err    error
	)
	for {
		keys, cursor, err = rdb.HScan(ctx, clientInfoKey, cursor, "", 100).Result()
		if err != nil {
			t.Fatalf("failed to execute `hscan` command, key: %s, cursor: %d, error: %+v", clientInfoKey, cursor, err)
		}

		var field, value string
		for i := 0; i < len(keys); i += 2 {
			field = keys[i]
			value = keys[i+1]

			client := &pb.ClientInfo{}
			if err = protobuf.UnmarshalJSONFromString(value, client); err != nil {
				t.Errorf(
					"failed to unmarshal the value to *pb.ClientInfo, key: %s, field: %s, value: %s, error: %+v",
					clientInfoKey, field, value, err,
				)
				continue
			}

			nid := client.GetNid()
			if nid == "" {
				t.Errorf("the nid is empty, key: %s, field: %s, value: %s", clientInfoKey, field, value)
				continue
			}

			enabled, ok := nodes[nid]
			if !ok {
				nKey := common.NodeInfoKey(nid)
				value, err = rdb.Get(ctx, nKey).Result()
				if err != nil && !errors.Is(err, redis.Nil) {
					t.Errorf("failed to execute `get` command, key: %s, error: %+v", nKey, err)
					continue
				}

				enabled = len(value) != 0
				nodes[nid] = enabled
			}
			if enabled {
				continue
			}

			_, ok = clients[nid]
			if !ok {
				clients[nid] = make([]string, 0, 1024)
			}
			clients[nid] = append(clients[nid], client.GetCid())
		}

		if cursor == 0 {
			break
		}
	}

	for key, value := range clients {
		t.Logf("disabled clients, node: %s, number: %d", key, len(value))

		number, err := rdb.HDel(ctx, clientInfoKey, value...).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			t.Errorf("failed to execute `hdel` command, key: %s, fields: %v, error: %+v", clientInfoKey, value, err)
			continue
		}

		t.Logf("clean up disabled clients, node: %s, number: %d", key, number)
	}
}

func cleanUpDisabledUsers(t *testing.T, ctx context.Context, rdb *redis.Client) {
	var (
		nodes = make(map[string]bool, 16)
		users = make(map[string][]string, 8)

		keys   []string
		cursor uint64
		err    error
	)
	for {
		keys, cursor, err = rdb.HScan(ctx, userInfoKey, cursor, "", 100).Result()
		if err != nil {
			t.Fatalf("failed to execute `hscan` command, key: %s, cursor: %d, error: %+v", userInfoKey, cursor, err)
		}

		var field, value string
		for i := 0; i < len(keys); i += 2 {
			field = keys[i]
			value = keys[i+1]

			user := &pb.UserInfo{}
			if err = protobuf.UnmarshalJSONFromString(value, user); err != nil {
				t.Errorf(
					"failed to unmarshal the value to *pb.UserInfo, key: %s, field: %s, value: %s, error: %+v",
					userInfoKey, field, value, err,
				)
				continue
			}

			nid := user.GetNid()
			if nid == "" {
				t.Errorf("the nid is empty, key: %s, field: %s, value: %s", userInfoKey, field, value)
				continue
			}

			enabled, ok := nodes[nid]
			if !ok {
				nKey := common.NodeInfoKey(nid)
				value, err = rdb.Get(ctx, nKey).Result()
				if err != nil && !errors.Is(err, redis.Nil) {
					t.Errorf("failed to execute `get` command, key: %s, error: %+v", nKey, err)
					continue
				}

				enabled = len(value) != 0
				nodes[nid] = enabled
			}
			if enabled {
				continue
			}

			_, ok = users[nid]
			if !ok {
				users[nid] = make([]string, 0, 1024)
			}
			users[nid] = append(users[nid], user.GetUid())
		}

		if cursor == 0 {
			break
		}
	}

	for key, value := range users {
		t.Logf("disabled users, node: %s, number: %d", key, len(value))

		number, err := rdb.HDel(ctx, userInfoKey, value...).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			t.Errorf("failed to execute `hdel` command, key: %s, fields: %v, error: %+v", userInfoKey, value, err)
			continue
		}

		t.Logf("clean up disabled users, node: %s, number: %d", key, number)
	}
}
