#!/bin/bash

# gRPC连接诊断脚本
# 用于诊断api-proxy-router和api-proxy-node之间的连接问题

echo "=== API Proxy Connection Diagnostics ==="
echo "Time: $(date)"
echo

# 检查Redis连接
echo "1. Checking Redis connectivity..."
redis-cli -h 127.0.0.1 -p 6379 ping
if [ $? -eq 0 ]; then
    echo "✓ Redis is accessible"
else
    echo "✗ Redis connection failed"
fi
echo

# 检查Redis中的节点信息
echo "2. Checking node registrations in Redis..."
echo "Node keys:"
redis-cli -h 127.0.0.1 -p 6379 keys "node:info:*" | head -10
echo
echo "Node count: $(redis-cli -h 127.0.0.1 -p 6379 keys "node:info:*" | wc -l)"
echo

# 检查节点信息的TTL
echo "3. Checking node TTL..."
for key in $(redis-cli -h 127.0.0.1 -p 6379 keys "node:info:*" | head -5); do
    ttl=$(redis-cli -h 127.0.0.1 -p 6379 ttl "$key")
    echo "Key: $key, TTL: $ttl seconds"
done
echo

# 检查router服务状态
echo "4. Checking router service status..."
if pgrep -f "router" > /dev/null; then
    echo "✓ Router service is running"
    echo "Router processes:"
    pgrep -f "router" | xargs ps -p
else
    echo "✗ Router service not found"
fi
echo

# 检查node服务状态
echo "5. Checking node service status..."
if pgrep -f "node" > /dev/null; then
    echo "✓ Node service is running"
    echo "Node processes:"
    pgrep -f "node" | xargs ps -p
else
    echo "✗ Node service not found"
fi
echo

# 检查网络连接
echo "6. Checking network connectivity..."
echo "Router to Redis:"
nc -z 127.0.0.1 6379 && echo "✓ Router can reach Redis" || echo "✗ Router cannot reach Redis"

echo "Router to Node (checking common ports):"
for port in 8081 8082 8083 8084; do
    nc -z 127.0.0.1 $port && echo "✓ Port $port is open" || echo "✗ Port $port is closed"
done
echo

# 检查最近的日志错误
echo "7. Checking recent log errors..."
echo "Router errors (last 10):"
if [ -f "/var/log/router.log" ]; then
    tail -100 /var/log/router.log | grep -i "error\|failed\|zero.*conn" | tail -10
elif [ -f "./router/logs/router.log" ]; then
    tail -100 ./router/logs/router.log | grep -i "error\|failed\|zero.*conn" | tail -10
else
    echo "Router log file not found"
fi
echo

echo "Node errors (last 10):"
if [ -f "/var/log/node.log" ]; then
    tail -100 /var/log/node.log | grep -i "error\|failed" | tail -10
elif [ -f "./node/logs/node.log" ]; then
    tail -100 ./node/logs/node.log | grep -i "error\|failed" | tail -10
else
    echo "Node log file not found"
fi
echo

# 检查系统资源
echo "8. Checking system resources..."
echo "Memory usage:"
free -h
echo
echo "CPU load:"
uptime
echo
echo "Disk usage:"
df -h | grep -E "/$|/var|/tmp"
echo

# 检查网络统计
echo "9. Checking network statistics..."
echo "TCP connections:"
ss -tuln | grep -E ":6379|:8080|:8081|:8082|:8083|:8084"
echo

echo "=== Diagnostics Complete ==="
echo
echo "ANALYSIS:"
echo "The 'zero ready connections' issue is typically caused by:"
echo "1. Redis node registration expiring (TTL too short)"
echo "2. Resolver clearing all addresses when Redis returns empty results"
echo "3. Balancer shutting down connections when resolver updates with empty list"
echo
echo "SOLUTIONS IMPLEMENTED:"
echo "1. Increased node registration TTL from 10s to 30s"
echo "2. Added intelligent address expiration (10 minutes grace period)"
echo "3. Enhanced monitoring and logging"
echo "4. Added gRPC keepalive configuration"
echo
echo "TO VERIFY THE FIX:"
echo "1. Monitor logs for 'keeping existing addresses' messages"
echo "2. Check for 'removing expired address' vs 'removing address from store'"
echo "3. Watch for Connection Monitor and Resolver Monitor statistics"
echo "4. Verify Redis TTL values are consistently > 20 seconds"
