#!/bin/bash

# 深入调查gRPC服务器端可能导致30分钟连接重置的配置

echo "=== Investigating gRPC Server Connection Lifecycle ==="
echo "Time: $(date)"
echo

# 检查go-zero框架的默认gRPC服务器配置
echo "1. Checking go-zero gRPC server default settings..."

# 查看node服务的配置
echo "Node service configuration:"
if [ -f "node/rpc/etc/node.yaml" ]; then
    cat node/rpc/etc/node.yaml
else
    echo "Node config file not found"
fi
echo

# 检查是否有gRPC服务器端的连接管理配置
echo "2. Checking for gRPC server connection management..."

# 查看是否有MaxConnectionAge等配置
echo "Searching for MaxConnectionAge, MaxConnectionIdle in codebase..."
find . -name "*.go" -type f -exec grep -l "MaxConnection\|keepalive\|ServerParameters" {} \; 2>/dev/null | head -10

echo
echo "Searching for specific gRPC server parameters..."
find . -name "*.go" -type f -exec grep -n "MaxConnectionAge\|MaxConnectionIdle\|MaxConnectionAgeGrace" {} \; 2>/dev/null

echo
echo "3. Checking go-zero zrpc default configurations..."

# 检查是否有隐式的gRPC服务器配置
echo "Looking for zrpc.MustNewServer usage..."
find . -name "*.go" -type f -exec grep -A 10 -B 5 "zrpc.MustNewServer" {} \; 2>/dev/null

echo
echo "4. Checking for any 30-minute or 1800-second timeouts..."

# 搜索所有可能的30分钟配置
echo "Searching for 30 minute configurations..."
find . -name "*.go" -name "*.yaml" -name "*.yml" -type f -exec grep -n "30.*[mM]in\|1800" {} \; 2>/dev/null

echo
echo "5. Checking K8s deployment configurations..."

# 检查K8s配置中的探针设置
echo "K8s probe configurations:"
find . -name "*.tpl" -name "*.yaml" -name "*.yml" -type f -exec grep -A 5 -B 5 "Probe\|periodSeconds" {} \; 2>/dev/null

echo
echo "6. Investigating potential gRPC connection reset causes..."

# 检查是否有定时任务或清理机制
echo "Looking for cleanup or maintenance tasks..."
find . -name "*.go" -type f -exec grep -n "cleanup\|clean.*up\|maintenance\|ticker\|timer" {} \; 2>/dev/null | grep -v test | head -20

echo
echo "7. Checking for HTTP/2 or gRPC specific settings..."

# 检查HTTP/2相关配置
echo "Looking for HTTP/2 settings..."
find . -name "*.go" -type f -exec grep -n "http2\|HTTP2\|h2c" {} \; 2>/dev/null

echo
echo "8. Analyzing connection patterns from logs..."

# 如果有日志文件，分析连接模式
echo "Checking for connection pattern in logs..."
if command -v kubectl &> /dev/null; then
    echo "Recent connection logs from router:"
    kubectl logs -l app=api-proxy-router --tail=100 | grep -E "(ready sub conns|connection|conn)" | tail -10
    
    echo
    echo "Recent connection logs from node:"
    kubectl logs -l app=api-proxy-node --tail=100 | grep -E "(connection|conn)" | tail -10
else
    echo "kubectl not available, skipping log analysis"
fi

echo
echo "9. Checking for load balancer or proxy configurations..."

# 检查是否有负载均衡器配置
echo "Looking for load balancer configurations..."
find . -name "*.yaml" -name "*.yml" -type f -exec grep -A 10 -B 5 "loadBalancer\|LoadBalancer\|ingress\|Ingress" {} \; 2>/dev/null

echo
echo "10. Summary of potential causes..."
echo
echo "POTENTIAL ROOT CAUSES FOR 30-MINUTE CONNECTION RESETS:"
echo
echo "A. gRPC Server-side Connection Management:"
echo "   - Default MaxConnectionAge (often 30 minutes in some implementations)"
echo "   - MaxConnectionIdle settings"
echo "   - HTTP/2 connection limits"
echo
echo "B. Infrastructure-level:"
echo "   - K8s service mesh (Istio, Linkerd) connection policies"
echo "   - Load balancer connection timeouts"
echo "   - Network proxy (Envoy, nginx) settings"
echo
echo "C. Application-level:"
echo "   - go-zero framework default settings"
echo "   - Custom connection management logic"
echo "   - Resource cleanup mechanisms"
echo
echo "D. Container/K8s level:"
echo "   - Pod restart policies"
echo "   - Resource limits causing OOM"
echo "   - Node maintenance windows"
echo
echo "NEXT STEPS:"
echo "1. Check go-zero source code for default gRPC server settings"
echo "2. Examine K8s cluster for service mesh or proxy configurations"
echo "3. Monitor actual gRPC connection lifecycle with detailed logging"
echo "4. Consider adding explicit gRPC server connection management settings"

echo
echo "=== Investigation Complete ==="
