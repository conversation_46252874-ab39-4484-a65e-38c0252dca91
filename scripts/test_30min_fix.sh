#!/bin/bash

# 测试30分钟问题修复效果的脚本

echo "=== Testing 30-minute Connection Issue Fix ==="
echo "Time: $(date)"
echo

# 函数：检查连接数
check_connections() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local ready_conns=$(kubectl logs -l app=api-proxy-router --tail=10 | grep "ready sub conns" | tail -1 | grep -o '\[.*\]' | head -1)
    echo "[$timestamp] Ready connections: $ready_conns"
    
    # 检查是否为零连接
    if echo "$ready_conns" | grep -q "\[0,"; then
        echo "  ⚠️  ZERO CONNECTIONS DETECTED!"
        return 1
    else
        echo "  ✓  Connections OK"
        return 0
    fi
}

# 函数：检查Redis中的节点信息
check_redis_nodes() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local node_count=$(redis-cli -h redis-service -p 6379 keys "node:info:*" 2>/dev/null | wc -l)
    echo "[$timestamp] Redis node count: $node_count"
    
    if [ "$node_count" -eq 0 ]; then
        echo "  ⚠️  NO NODES IN REDIS!"
        return 1
    else
        echo "  ✓  Redis nodes OK"
        return 0
    fi
}

# 函数：检查resolver日志
check_resolver_logs() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] Recent resolver activity:"
    
    # 检查是否有保护性日志
    local keeping_logs=$(kubectl logs -l app=api-proxy-router --tail=50 | grep "keeping existing addresses" | wc -l)
    local expire_logs=$(kubectl logs -l app=api-proxy-router --tail=50 | grep "removing expired address" | wc -l)
    local empty_logs=$(kubectl logs -l app=api-proxy-router --tail=50 | grep "consecutive empty resolve" | wc -l)
    
    echo "  - Keeping addresses logs: $keeping_logs"
    echo "  - Expired address logs: $expire_logs"
    echo "  - Empty resolve logs: $empty_logs"
    
    if [ "$keeping_logs" -gt 0 ]; then
        echo "  ✓  Protection mechanism is working"
    fi
}

# 主监控循环
echo "Starting continuous monitoring..."
echo "Will check every 30 seconds for 2 hours (240 checks)"
echo "Press Ctrl+C to stop"
echo

zero_count=0
total_checks=0
start_time=$(date +%s)

for i in {1..240}; do
    echo "=== Check $i/240 ==="
    
    # 检查连接状态
    if ! check_connections; then
        zero_count=$((zero_count + 1))
        echo "  🚨 Zero connection event #$zero_count"
        
        # 立即检查Redis和日志
        check_redis_nodes
        check_resolver_logs
        
        # 记录详细信息
        echo "  📝 Detailed logs:"
        kubectl logs -l app=api-proxy-router --tail=20 | grep -E "(ready sub conns|keeping existing|removing|empty resolve)" | tail -5
    fi
    
    total_checks=$((total_checks + 1))
    
    # 每10次检查显示统计
    if [ $((i % 10)) -eq 0 ]; then
        current_time=$(date +%s)
        elapsed=$((current_time - start_time))
        echo "  📊 Statistics after $elapsed seconds:"
        echo "    - Total checks: $total_checks"
        echo "    - Zero connection events: $zero_count"
        echo "    - Success rate: $(echo "scale=2; ($total_checks - $zero_count) * 100 / $total_checks" | bc)%"
    fi
    
    echo
    sleep 30
done

# 最终统计
echo "=== Final Statistics ==="
echo "Total monitoring time: 2 hours"
echo "Total checks: $total_checks"
echo "Zero connection events: $zero_count"
echo "Success rate: $(echo "scale=2; ($total_checks - $zero_count) * 100 / $total_checks" | bc)%"

if [ "$zero_count" -eq 0 ]; then
    echo "🎉 SUCCESS: No zero connection events detected!"
    echo "The 30-minute issue appears to be fixed."
elif [ "$zero_count" -lt 3 ]; then
    echo "⚠️  IMPROVED: Only $zero_count zero connection events (much better than before)"
    echo "The fix is working but may need further tuning."
else
    echo "❌ ISSUE PERSISTS: $zero_count zero connection events detected"
    echo "The fix may not be complete or there are other issues."
fi

echo
echo "=== Recommendations ==="
if [ "$zero_count" -gt 0 ]; then
    echo "1. Check the resolver logs for 'keeping existing addresses' messages"
    echo "2. Verify Redis connectivity and performance"
    echo "3. Consider increasing addressExpireTime if issues persist"
    echo "4. Monitor for patterns in the timing of zero connection events"
else
    echo "1. The fix appears successful - continue monitoring"
    echo "2. Consider the current settings as baseline for production"
fi
