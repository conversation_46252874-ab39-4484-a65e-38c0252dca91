#!/bin/bash

# 测试30分钟连接稳定性修复效果

echo "=== 30分钟连接稳定性测试 ==="
echo "开始时间: $(date)"
echo

# 配置参数
ROUTER_NAMESPACE="default"
ROUTER_APP_LABEL="api-proxy-router"
NODE_APP_LABEL="api-proxy-node"
TEST_DURATION_MINUTES=35  # 测试35分钟，覆盖30分钟周期
LOG_FILE="/tmp/connection_stability_test_$(date +%Y%m%d_%H%M%S).log"

echo "测试配置:"
echo "- Router命名空间: $ROUTER_NAMESPACE"
echo "- Router标签: $ROUTER_APP_LABEL"
echo "- Node标签: $NODE_APP_LABEL"
echo "- 测试时长: $TEST_DURATION_MINUTES 分钟"
echo "- 日志文件: $LOG_FILE"
echo

# 检查kubectl可用性
if ! command -v kubectl &> /dev/null; then
    echo "错误: kubectl命令不可用"
    exit 1
fi

# 检查pod是否存在
echo "检查服务状态..."
ROUTER_PODS=$(kubectl get pods -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE --no-headers 2>/dev/null | wc -l)
NODE_PODS=$(kubectl get pods -l app=$NODE_APP_LABEL -n $ROUTER_NAMESPACE --no-headers 2>/dev/null | wc -l)

echo "- Router pods: $ROUTER_PODS"
echo "- Node pods: $NODE_PODS"

if [ "$ROUTER_PODS" -eq 0 ]; then
    echo "警告: 未找到router pods"
fi

if [ "$NODE_PODS" -eq 0 ]; then
    echo "警告: 未找到node pods"
fi

echo

# 创建监控函数
monitor_connections() {
    local start_time=$(date +%s)
    local end_time=$((start_time + TEST_DURATION_MINUTES * 60))
    local zero_connection_count=0
    local last_zero_time=""
    
    echo "开始监控连接状态..." | tee -a "$LOG_FILE"
    echo "监控时间: $(date)" | tee -a "$LOG_FILE"
    echo "预计结束: $(date -d "+${TEST_DURATION_MINUTES} minutes")" | tee -a "$LOG_FILE"
    echo "----------------------------------------" | tee -a "$LOG_FILE"
    
    while [ $(date +%s) -lt $end_time ]; do
        current_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        # 获取连接状态日志
        connection_logs=$(kubectl logs -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE --since=10s 2>/dev/null | grep "ready sub conns" | tail -5)
        
        if [ -n "$connection_logs" ]; then
            echo "[$current_time] 连接状态更新:" | tee -a "$LOG_FILE"
            echo "$connection_logs" | while read line; do
                echo "  $line" | tee -a "$LOG_FILE"
                
                # 检查是否出现零连接
                if echo "$line" | grep -q "\[0,"; then
                    zero_connection_count=$((zero_connection_count + 1))
                    last_zero_time="$current_time"
                    echo "  ⚠️  检测到零连接事件 #$zero_connection_count" | tee -a "$LOG_FILE"
                fi
            done
            echo | tee -a "$LOG_FILE"
        fi
        
        # 获取健康检查日志
        health_logs=$(kubectl logs -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE --since=10s 2>/dev/null | grep -E "health|Health" | tail -3)
        if [ -n "$health_logs" ]; then
            echo "[$current_time] 健康检查状态:" | tee -a "$LOG_FILE"
            echo "$health_logs" | while read line; do
                echo "  $line" | tee -a "$LOG_FILE"
            done
            echo | tee -a "$LOG_FILE"
        fi
        
        # 获取resolver监控日志
        resolver_logs=$(kubectl logs -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE --since=10s 2>/dev/null | grep -E "Resolver Monitor|resolver.*stats" | tail -2)
        if [ -n "$resolver_logs" ]; then
            echo "[$current_time] Resolver状态:" | tee -a "$LOG_FILE"
            echo "$resolver_logs" | while read line; do
                echo "  $line" | tee -a "$LOG_FILE"
            done
            echo | tee -a "$LOG_FILE"
        fi
        
        # 每分钟输出进度
        elapsed_minutes=$(( ($(date +%s) - start_time) / 60 ))
        remaining_minutes=$((TEST_DURATION_MINUTES - elapsed_minutes))
        
        if [ $(($(date +%s) % 60)) -eq 0 ]; then
            echo "[$current_time] 测试进度: ${elapsed_minutes}/${TEST_DURATION_MINUTES} 分钟 (剩余: ${remaining_minutes} 分钟)" | tee -a "$LOG_FILE"
            echo "  零连接事件计数: $zero_connection_count" | tee -a "$LOG_FILE"
            if [ -n "$last_zero_time" ]; then
                echo "  最后零连接时间: $last_zero_time" | tee -a "$LOG_FILE"
            fi
            echo | tee -a "$LOG_FILE"
        fi
        
        sleep 10
    done
    
    echo "监控完成!" | tee -a "$LOG_FILE"
    echo "总零连接事件: $zero_connection_count" | tee -a "$LOG_FILE"
    
    return $zero_connection_count
}

# 获取初始状态
echo "获取初始状态..." | tee -a "$LOG_FILE"
echo "Router pods状态:" | tee -a "$LOG_FILE"
kubectl get pods -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE | tee -a "$LOG_FILE"
echo | tee -a "$LOG_FILE"

echo "Node pods状态:" | tee -a "$LOG_FILE"
kubectl get pods -l app=$NODE_APP_LABEL -n $ROUTER_NAMESPACE | tee -a "$LOG_FILE"
echo | tee -a "$LOG_FILE"

echo "当前连接状态:" | tee -a "$LOG_FILE"
kubectl logs -l app=$ROUTER_APP_LABEL -n $ROUTER_NAMESPACE --tail=10 | grep "ready sub conns" | tail -3 | tee -a "$LOG_FILE"
echo | tee -a "$LOG_FILE"

# 开始监控
monitor_connections
zero_events=$?

# 生成测试报告
echo "========================================" | tee -a "$LOG_FILE"
echo "测试报告" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"
echo "测试时间: $(date)" | tee -a "$LOG_FILE"
echo "测试时长: $TEST_DURATION_MINUTES 分钟" | tee -a "$LOG_FILE"
echo "零连接事件总数: $zero_events" | tee -a "$LOG_FILE"

if [ $zero_events -eq 0 ]; then
    echo "✅ 测试结果: 成功! 未检测到零连接事件" | tee -a "$LOG_FILE"
    echo "   30分钟连接问题已修复" | tee -a "$LOG_FILE"
elif [ $zero_events -le 2 ]; then
    echo "⚠️  测试结果: 部分成功，零连接事件显著减少" | tee -a "$LOG_FILE"
    echo "   建议进一步调优配置参数" | tee -a "$LOG_FILE"
else
    echo "❌ 测试结果: 失败，仍存在频繁的零连接事件" | tee -a "$LOG_FILE"
    echo "   需要进一步调查问题原因" | tee -a "$LOG_FILE"
fi

echo | tee -a "$LOG_FILE"
echo "详细日志保存在: $LOG_FILE" | tee -a "$LOG_FILE"

# 提供故障排除建议
if [ $zero_events -gt 0 ]; then
    echo | tee -a "$LOG_FILE"
    echo "故障排除建议:" | tee -a "$LOG_FILE"
    echo "1. 检查node服务的MaxConnectionAge配置是否生效:" | tee -a "$LOG_FILE"
    echo "   kubectl logs -l app=$NODE_APP_LABEL | grep -i 'MaxConnection\\|keepalive'" | tee -a "$LOG_FILE"
    echo | tee -a "$LOG_FILE"
    echo "2. 检查gRPC服务器日志中的连接关闭信息:" | tee -a "$LOG_FILE"
    echo "   kubectl logs -l app=$NODE_APP_LABEL | grep -i 'connection\\|close\\|goaway'" | tee -a "$LOG_FILE"
    echo | tee -a "$LOG_FILE"
    echo "3. 验证健康检查是否正常工作:" | tee -a "$LOG_FILE"
    echo "   kubectl logs -l app=$ROUTER_APP_LABEL | grep -i health" | tee -a "$LOG_FILE"
    echo | tee -a "$LOG_FILE"
    echo "4. 检查Redis连接状态:" | tee -a "$LOG_FILE"
    echo "   kubectl logs -l app=$ROUTER_APP_LABEL | grep -i redis" | tee -a "$LOG_FILE"
fi

echo | tee -a "$LOG_FILE"
echo "测试完成: $(date)" | tee -a "$LOG_FILE"

exit $zero_events
