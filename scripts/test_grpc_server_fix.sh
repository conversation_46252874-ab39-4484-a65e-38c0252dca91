#!/bin/bash

# 测试gRPC服务器端连接管理修复的脚本

echo "=== Testing gRPC Server Connection Management Fix ==="
echo "Time: $(date)"
echo

# 检查配置是否正确应用
echo "1. Verifying configuration changes..."

echo "Node service configuration:"
if [ -f "node/rpc/etc/node.yaml" ]; then
    echo "ServerOptions configuration:"
    grep -A 10 "ServerOptions:" node/rpc/etc/node.yaml
else
    echo "❌ Node config file not found"
fi
echo

echo "2. Checking code changes..."

echo "Config struct changes:"
if grep -q "ServerOptions" node/rpc/internal/config/config.go; then
    echo "✓ ServerOptions struct added to config"
else
    echo "❌ ServerOptions struct not found in config"
fi

echo "Server creation changes:"
if grep -q "buildGRPCServerOptions" node/rpc/server/server.go; then
    echo "✓ buildGRPCServerOptions function added"
else
    echo "❌ buildGRPCServerOptions function not found"
fi

if grep -q "keepalive.ServerParameters" node/rpc/server/server.go; then
    echo "✓ gRPC keepalive parameters configured"
else
    echo "❌ gRPC keepalive parameters not configured"
fi
echo

echo "3. Building and testing the changes..."

# 尝试构建项目以验证语法正确性
echo "Building node service..."
cd node/rpc
if go build -o /tmp/test-node-service ./cmd/main.go 2>/dev/null; then
    echo "✓ Node service builds successfully"
    rm -f /tmp/test-node-service
else
    echo "❌ Node service build failed"
    echo "Build errors:"
    go build -o /tmp/test-node-service ./cmd/main.go 2>&1 | head -10
fi
cd ../..
echo

echo "4. Analyzing the fix..."

echo "WHAT THIS FIX ADDRESSES:"
echo "- MaxConnectionAge: Set to effectively infinite (1 year) to prevent 30-minute resets"
echo "- MaxConnectionIdle: Set to effectively infinite to prevent idle connection drops"
echo "- MaxConnectionAgeGrace: Set to effectively infinite for graceful handling"
echo "- Keepalive parameters: Properly configured for connection health"
echo

echo "5. Expected behavior after deployment..."

echo "BEFORE FIX:"
echo "- Connections reset every ~30 minutes due to default MaxConnectionAge"
echo "- 'ready sub conns: [0, null]' followed by gradual recovery"
echo "- Predictable timing pattern"
echo

echo "AFTER FIX:"
echo "- Connections should remain stable indefinitely"
echo "- No periodic connection resets"
echo "- Only connection drops due to actual network issues or service restarts"
echo

echo "6. Deployment recommendations..."

echo "DEPLOYMENT STEPS:"
echo "1. Deploy node services first (they are backward compatible)"
echo "2. Monitor connection stability for 1-2 hours"
echo "3. If stable, the fix is working correctly"
echo "4. Router services don't need changes for this specific fix"
echo

echo "MONITORING COMMANDS:"
echo "# Watch for connection resets"
echo "kubectl logs -f -l app=api-proxy-router | grep 'ready sub conns'"
echo
echo "# Check for keepalive activity"
echo "kubectl logs -f -l app=api-proxy-node | grep -i keepalive"
echo
echo "# Monitor connection patterns"
echo "watch 'kubectl logs -l app=api-proxy-router --tail=20 | grep \"ready sub conns\" | tail -5'"
echo

echo "7. Validation criteria..."

echo "SUCCESS INDICATORS:"
echo "✓ No 'ready sub conns: [0, null]' logs after 35+ minutes"
echo "✓ Stable connection count over extended periods"
echo "✓ No predictable 30-minute reset pattern"
echo

echo "FAILURE INDICATORS:"
echo "❌ Continued 30-minute connection resets"
echo "❌ New connection issues or instability"
echo "❌ Service startup failures"
echo

echo "8. Rollback plan..."

echo "IF ISSUES OCCUR:"
echo "1. Revert node/rpc/etc/node.yaml to remove ServerOptions section"
echo "2. Revert node/rpc/internal/config/config.go changes"
echo "3. Revert node/rpc/server/server.go changes"
echo "4. Redeploy node services"
echo

echo "ROLLBACK COMMANDS:"
echo "git checkout HEAD~1 -- node/rpc/etc/node.yaml"
echo "git checkout HEAD~1 -- node/rpc/internal/config/config.go"
echo "git checkout HEAD~1 -- node/rpc/server/server.go"
echo "kubectl rollout restart deployment api-proxy-node"
echo

echo "=== Test Complete ==="
echo
echo "NEXT STEPS:"
echo "1. Review the configuration and code changes above"
echo "2. Deploy to a test environment first if possible"
echo "3. Monitor for at least 35 minutes to confirm no 30-minute resets"
echo "4. If successful, deploy to production with careful monitoring"
